from lumaai import LumaAI
import requests
import time
from pathlib import Path
import asyncio
from typing import List, Optional
import os
from config import settings
from models.video import VideoClip

LUMA_AUTH_TOKEN = settings.lumma_api_key
VIDEO_STORAGE_PATH = Path("videos")
VIDEO_STORAGE_PATH.mkdir(parents=True, exist_ok=True)

class VideoGenerationService:
    def __init__(self):
        self.client = LumaAI(auth_token=LUMA_AUTH_TOKEN)

    async def generate_video(self, prompt: str) -> VideoClip:
        try:
            generation = self.client.generations.create(prompt=prompt)
            return generation.id
        except Exception as e:
            raise Exception(f"Failed to start video generation: {str(e)}")

    async def check_generation_status(self, generation_id: str) -> tuple[str, Optional[str]]:
        try:
            generation = self.client.generations.get(id=generation_id)
            if generation.state == "completed":
                return "completed", generation.assets.video
            elif generation.state == "failed":
                return "failed", generation.failure_reason
            return generation.state, None
        except Exception as e:
            raise Exception(f"Failed to check generation status: {str(e)}")

    async def download_video(self, video_url: str, file_path: Path):
        try:
            response = requests.get(video_url, stream=True)
            response.raise_for_status()
            with open(file_path, 'wb') as file:
                file.write(response.content)
        except Exception as e:
            raise Exception(f"Failed to download video: {str(e)}")