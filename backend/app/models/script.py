from typing import List, Optional, Union, Literal
from datetime import datetime
from pydantic import BaseModel



class ScriptSegment(BaseModel):
    id: int
    script: str
    narrator: str
    narrator_audio: Optional[str] = None

class AdScripts(BaseModel):
    segments: List[ScriptSegment]
    
class Script(BaseModel):
    id: str
    scripts: AdScripts
    audio_id: Optional[str] = None
    voice: str = "nova"