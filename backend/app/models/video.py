from typing import List, Optional, Dict
from datetime import datetime
from pydantic import BaseModel

class ScriptSegment(BaseModel):
    id: int
    script: str
    narrator: str
    narrator_audio: Optional[str] = None

class ScriptContent(BaseModel):
    segments: List[ScriptSegment]

class Position(BaseModel):
    x: float
    y: float

class TextOverlay(BaseModel):
    id: str
    text: str
    startTime: float
    duration: float
    position: Position
    fontSize: float
    color: str

class Script(BaseModel):
    id: str
    scripts: ScriptContent
    audio_id: Optional[str] = None
    user_audio: Optional[str] = None
    textOverlays: Optional[List[TextOverlay]] = None
    elevenlabs_voice_id: Optional[List[str]] = None

class GenerationSegment(BaseModel):
    segment_id: int
    generation_id: Optional[str] = None
    status: str
    video_url: Optional[str] = None
    narrator_audio: Optional[str] = None
    created_at: Optional[str] = None
    failed_at: Optional[str] = None
    completed_at: Optional[str] = None
    error: Optional[str] = None

class Generation(BaseModel):
    status: str
    segments: List[GenerationSegment]
    updated_at: str
    error: Optional[str] = None

class VideoAdCampaign(BaseModel):
    id: Optional[str] = None
    brandName: str
    summary: str
    description: str
    adLength: str

class VideoAdCampaignResponse(BaseModel):
    id: str
    title: str
    brandName: str
    summary: str
    description: str
    adLength: str
    createdAt: datetime
    status: str
    scripts: List[Script]
    generations: Optional[Dict[str, Generation]] = None