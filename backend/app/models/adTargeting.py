from typing import List, Optional, Union, Literal
from pydantic import BaseModel, Field, HttpUrl, constr
import re

# 使用 annotated-types 进行正则验证
from typing_extensions import Annotated
from pydantic import AfterValidator

def validate_id(v: str) -> str:
    if not re.match(r"^[a-zA-Z0-9-]+$", v):
        raise ValueError("ID must contain only letters, numbers, and hyphens")
    return v

IdStr = Annotated[str, AfterValidator(validate_id)]

class Competitor(BaseModel):
    name: str = Field(..., min_length=1)
    product: str = Field(..., min_length=1)
    rating: str = Field(..., pattern=r"^\d(\.\d)? out of 5 stars$")
    reviews: int = Field(..., ge=0)
    liked_aspects: List[str] = Field(..., min_items=1)
    negative_aspects: List[str]
    link: Optional[HttpUrl] = None

class Description(BaseModel):
    sub_title: str = Field(..., min_length=1)
    description: str = Field(..., min_length=10)

class Review(BaseModel):
    description: str = Field(..., min_length=10)
    reviewer: Optional[str] = None
    link: Optional[HttpUrl] = None

class ReviewGroup(BaseModel):
    sub_title: str = Field(..., min_length=1)
    reviews: List[Review] = Field(..., min_items=1)

class SubSection(BaseModel):
    id: IdStr = Field(..., min_length=1)
    sub_title: str = Field(..., min_length=1)
    descriptions: Optional[List[Description]] = None
    review_groups: Optional[List[ReviewGroup]] = None

class Section(BaseModel):
    id: IdStr = Field(..., min_length=1)
    title: str = Field(..., min_length=1)
    type: Literal["tags", "list", "structured", "competitor"]
    data: Union[List[str], List[SubSection], List[Competitor]] = Field(..., min_items=1)

class TargetingResults(BaseModel):
    title: str = Field(..., min_length=1)
    sections: List[Section] = Field(..., min_items=1)