# Adtargeting_AI-1/milvus_connector.py
import os
import sys
import polars as pl
import yaml
import json
import fnmatch
from pymilvus import connections, utility, CollectionSchema, FieldSchema, DataType, Collection
from typing import List, Dict, Any, Optional
from .config import settings
import logging
from pydantic import BaseModel, Field, ValidationError
import gc

#import config

logger = logging.getLogger(__name__)


# --- Pydantic Models for CSV Processing Configuration ---
class ParserOptionsModel(BaseModel):
    """Pydantic model for parser_options in CSV configuration."""
    separator: str = Field(default=',')
    encoding: str = Field(default='utf-8-lossy') # Consider 'utf-8' first, fallback to 'utf-8-lossy' if problematic
    has_header: bool = Field(default=True)
    ignore_errors: bool = Field(default=True) # Polars: try to skip rows with errors.
    infer_schema_length: Optional[int] = Field(default=10000) # Polars: number of rows to infer dtypes. None for all.
    dtypes: Optional[Dict[str, str]] = Field(default_factory=dict) # Polars: map column names to Polars dtypes string representations
    null_values: Optional[List[str]] = Field(default_factory=list)
    truncate_ragged_lines: Optional[bool] = Field(default=True)  # Changed to True to handle inconsistent field counts
    # low_memory: Optional[bool] = Field(default=False) # Removed: This is a pandas-specific parameter
    csv_reader_batch_size: Optional[int] = Field(default=None) # For Polars read_csv_batched


class TextUnificationModel(BaseModel):
    """Pydantic model for text_unification in CSV configuration."""
    columns_for_searchable_document: List[str] = Field(min_length=1)
    format_string: Optional[str] = None

class MetadataExtractionModel(BaseModel):
    """Pydantic model for metadata_extraction in CSV configuration."""
    original_row_identifier_column: Optional[str] = None
    columns_for_key_original_fields: List[str] = Field(default_factory=list)

class CSVConfigurationModel(BaseModel):
    """Pydantic model for a single CSV configuration entry."""
    name: str
    file_pattern: str
    parser_options: ParserOptionsModel = Field(default_factory=ParserOptionsModel)
    text_unification: TextUnificationModel
    metadata_extraction: MetadataExtractionModel

class AllCSVConfigurationsModel(BaseModel):
    """Pydantic model for the root of the CSV configuration file."""
    csv_configurations: List[CSVConfigurationModel] = Field(default_factory=list)


class MilvusDBConnector:
    """
    A connector class to interact with Milvus vector database.
    Handles connection, schema creation, data insertion, and search.
    """
    def __init__(self, host: str = settings.vector_db_host, port: str = settings.vector_db_port, alias: str = "default"):
        self.alias = alias
        self.host = host
        self.port = port
        self.dev_mode = settings.dev_mode  # Use config value instead of hardcoded False
        
        if self.dev_mode:
            logger.info(f"DEV_MODE enabled: Simulating Milvus connection to {self.host}:{self.port}")
            return
            
        try:
            logger.info(f"Attempting to connect to Milvus at {self.host}:{self.port} with alias '{self.alias}'")
            existing_connections = connections.list_connections()
            is_connected = any(conn_alias == self.alias and connected for conn_alias, connected in existing_connections)

            if is_connected:
                logger.info(f"Connection with alias '{self.alias}' already established and active.")
            else:
                if any(conn_alias == self.alias for conn_alias, _ in existing_connections):
                    try:
                        connections.disconnect(self.alias)
                        logger.info(f"Disconnected existing inactive connection for alias '{self.alias}'.")
                    except Exception as e_disconnect:
                        logger.warning(f"Could not disconnect existing alias '{self.alias}': {e_disconnect}. Proceeding with connect attempt.")
                connections.connect(alias=self.alias, host=self.host, port=self.port)
                logger.info(f"Successfully connected to Milvus with alias '{self.alias}'.")
        except Exception as e:
            logger.error(f"Failed to connect to Milvus: {e}", exc_info=True)
            raise ConnectionError(f"Could not connect to Milvus: {e}") from e

    def _collection_exists(self, collection_name: str) -> bool:
        """Checks if a collection exists in Milvus."""
        if self.dev_mode:
            logger.info(f"DEV_MODE: Simulating collection exists check for '{collection_name}'")
            return False
        
        try:
            return utility.has_collection(collection_name, using=self.alias)
        except Exception as e:
            logger.error(f"Error checking if collection '{collection_name}' exists: {e}", exc_info=True)
            return False # Assume it doesn't exist on error to potentially allow recreation if intended

    def _create_collection(self, collection_name: str, dim: int) -> Collection:
        """Creates a new collection in Milvus with a predefined schema."""
        if self._collection_exists(collection_name):
            logger.warning(f"Collection '{collection_name}' already exists. Returning existing instance.")
            return Collection(collection_name, using=self.alias)

        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True, description="Auto-generated unique ID"),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=dim, description="Embedding vector"),
            FieldSchema(name="searchable_document", dtype=DataType.VARCHAR, max_length=settings.max_text_length * 2, description="Concatenated text content that was embedded"),
            FieldSchema(name="source_file", dtype=DataType.VARCHAR, max_length=512, description="Name of the original CSV file"),
            FieldSchema(name="original_row_id", dtype=DataType.VARCHAR, max_length=256, description="Identifier of the row in its original CSV file"),
            FieldSchema(name="key_fields_json", dtype=DataType.VARCHAR, max_length=16384, description="JSON string of original key fields and their values") # Max length for VARCHAR in Milvus is often 65535
        ]
        schema = CollectionSchema(fields, description=f"{collection_name} for RAG with processed CSV data", enable_dynamic_field=False)

        try:
            collection = Collection(collection_name, schema=schema, using=self.alias)
            logger.info(f"Collection '{collection_name}' created successfully with new schema.")
            index_params = {
                "metric_type": settings.default_milvus_metric_type,
                "index_type": settings.default_milvus_index_type,
                "params": settings.default_milvus_index_params,
            }
            collection.create_index(field_name="embedding", index_params=index_params)
            logger.info(f"Index created on 'embedding' field for collection '{collection_name}' with params: {index_params}.")
            return collection
        except Exception as e:
            logger.error(f"Failed to create collection '{collection_name}': {e}", exc_info=True)
            raise

    def get_or_create_collection(self, collection_name: str, dim: Optional[int] = None) -> Collection:
        """Gets an existing collection or creates a new one if it doesn't exist."""
        if self.dev_mode:
            logger.info(f"DEV_MODE: Simulating get_or_create_collection for '{collection_name}' with dim={dim}")
            # Create a mock Collection object or return None in dev mode
            return None
        
        if self._collection_exists(collection_name):
            logger.info(f"Collection '{collection_name}' found.")
            collection = Collection(collection_name, using=self.alias)
            # Ensure collection is loaded for searching, etc.
            if not collection.has_index(): # Or a more direct check if it's loaded if available
                 logger.warning(f"Collection '{collection_name}' exists but may not have an index or isn't loaded. Attempting to load.")
            try:
                collection.load() # Load collection for searching.
                logger.info(f"Collection '{collection_name}' loaded.")
            except Exception as e: # Catch specific Milvus errors if possible
                 logger.error(f"Error loading collection '{collection_name}': {e}", exc_info=True)
                 # Depending on the error, you might want to raise it or handle it.
            return collection
        else:
            if dim is None:
                error_msg = "Dimension 'dim' must be provided to create a new collection if it doesn't exist."
                logger.error(error_msg)
                raise ValueError(error_msg)
            logger.info(f"Collection '{collection_name}' not found. Creating new collection with dimension {dim}.")
            return self._create_collection(collection_name, dim)

    def add_processed_data(self, collection_name: str, processed_data_batch: List[Dict[str, Any]], embeddings_batch: List[List[float]]):
        """Adds a batch of processed data records and their embeddings to Milvus."""
        if not processed_data_batch or not embeddings_batch:
            logger.warning("Processed data or embeddings batch is empty. Nothing to add.")
            return
        if len(processed_data_batch) != len(embeddings_batch):
            error_msg = "The number of processed data records and embeddings must be the same."
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        if self.dev_mode:
            logger.info(f"DEV_MODE: Simulating add_processed_data for collection '{collection_name}' with {len(processed_data_batch)} records")
            return
        
        # Assuming embeddings_batch is not empty, so embeddings_batch[0] is safe
        dim = len(embeddings_batch[0]) if embeddings_batch else 0
        if dim == 0:
            logger.error("Embedding dimension is 0. Cannot proceed with Milvus insertion.")
            return

        collection = self.get_or_create_collection(collection_name, dim=dim)

        # Prepare data for Milvus insertion
        # Ensure all lists are of the same length and data types match schema
        insert_data = [
            embeddings_batch,
            [str(record.get('searchable_document', ''))[:settings.max_text_length * 2 -1] for record in processed_data_batch], # Truncate to fit VARCHAR
            [str(record.get('source_file', 'N/A'))[:511] for record in processed_data_batch],
            [str(record.get('original_row_id', 'N/A'))[:255] for record in processed_data_batch],
            [json.dumps(record.get('key_original_fields', {}))[:16383] for record in processed_data_batch] # Truncate JSON string
        ]

        try:
            insert_result = collection.insert(insert_data)
            logger.info(f"Inserted {len(processed_data_batch)} items into '{collection_name}'. PKs (first 5): {insert_result.primary_keys[:5]}")
            collection.flush() # Ensure data is written to disk
            logger.info(f"Flush command issued for collection '{collection_name}'.")
        except Exception as e: # Catch specific Milvus errors if possible
            logger.error(f"Failed to add processed data to collection '{collection_name}': {e}", exc_info=True)
            raise

    def search(self, collection_name: str, vector: List[float], top_k: int = 10, expr: Optional[str] = None) -> List[Dict[str, Any]]:
        """Searches the specified collection in Milvus."""
        if not vector:
            logger.warning("Search vector is empty. Returning no results.")
            return []
        
        if self.dev_mode:
            logger.info(f"DEV_MODE: Simulating search in collection '{collection_name}' with vector length {len(vector)}, top_k={top_k}")
            # Return an empty result in dev mode
            return []

        output_fields = ["id", "searchable_document", "source_file", "original_row_id", "key_fields_json"] # Ensure 'id' is not fetched as it's part of hit metadata
        collection = self.get_or_create_collection(collection_name, dim=len(vector)) # dim is needed if collection might be created
        
        # Ensure collection is loaded before search
        if not collection.is_empty: # A simple check, load ensures it's ready
            try:
                collection.load()
            except Exception as e:
                logger.error(f"Error loading collection '{collection_name}' for search: {e}")
                # Decide if to raise or return empty. For robustness, try to proceed if load fails but collection exists.

        search_params = {
            "metric_type": settings.default_milvus_metric_type, # e.g., "L2", "IP"
            "params": settings.default_milvus_search_params,   # e.g., {"nprobe": 10} for IVF, {"ef": 100} for HNSW
        }

        try:
            logger.debug(f"Searching collection '{collection_name}' with vector, top_k={top_k}, expr='{expr}', output_fields={output_fields}")
            # Milvus search expects a list of vectors for the `data` parameter
            results = collection.search(
                data=[vector],
                anns_field="embedding", # The field name of your vector data
                param=search_params,
                limit=top_k,
                expr=expr,             # Optional: scalar filtering expression
                output_fields=output_fields, # Fields to return from the search
                consistency_level="Strong" # Or "Bounded", "Eventually", "Session"
            )
            
            search_results = []
            if results:
                for hits_for_one_query in results: # results is a list of Hits objects
                    for hit in hits_for_one_query:   # Each Hits object contains a list of Hit objects
                        result_item = {"id": hit.id, "distance": hit.distance}
                        # Access entity fields if they were requested and returned
                        for field in output_fields:
                            if field != "id": # id and distance are already there
                                try:
                                    value = hit.entity.get(field) if hasattr(hit, 'entity') and hit.entity else None
                                    if field == "key_fields_json" and isinstance(value, str):
                                        try:
                                            result_item[field] = json.loads(value)
                                        except json.JSONDecodeError:
                                            logger.warning(f"Could not parse key_fields_json for hit ID {hit.id}: '{value[:100]}...'")
                                            result_item[field] = {"raw_value": value} # Store raw if unparsable
                                    else:
                                        result_item[field] = value
                                except Exception as e_field:
                                    logger.warning(f"Could not retrieve field '{field}' for hit ID {hit.id}: {e_field}")
                                    result_item[field] = None
                        search_results.append(result_item)
            logger.info(f"Search in '{collection_name}' found {len(search_results)} results for top_k={top_k}.")
            return search_results
        except Exception as e: # Catch specific Milvus errors
            logger.error(f"Failed to search in collection '{collection_name}': {e}", exc_info=True)
            return []

    def delete_collection(self, collection_name: str):
        """Deletes a collection from Milvus."""
        if self._collection_exists(collection_name):
            try:
                utility.drop_collection(collection_name, using=self.alias)
                logger.info(f"Collection '{collection_name}' deleted successfully.")
            except Exception as e: # Catch specific Milvus errors
                logger.error(f"Failed to delete collection '{collection_name}': {e}", exc_info=True)
                # Optionally re-raise
        else:
            logger.info(f"Collection '{collection_name}' does not exist, no need to delete.")

    def close_connection(self):
        """Closes the connection to Milvus."""
        if self.dev_mode:
            logger.info(f"DEV_MODE: Simulating Milvus connection close for alias '{self.alias}'")
            return
            
        try:
            # Check if the alias exists in connections
            existing_connections = connections.list_connections()
            if any(conn_alias == self.alias for conn_alias, _ in existing_connections):
                connections.disconnect(self.alias)
                logger.info(f"Disconnected from Milvus with alias '{self.alias}'.")
            else:
                logger.info(f"No connection found for alias '{self.alias}' to disconnect.")
        except Exception as e: # Catch specific Milvus errors
            logger.error(f"Error disconnecting from Milvus (alias: {self.alias}): {e}", exc_info=True)


class CSVConnector:
    """
    Processes multiple heterogeneous CSV files based on a YAML configuration.
    Extracts text for embedding and structured metadata.
    Uses Pydantic for configuration validation and processes large CSVs in batches.
    """
    def __init__(self, config_path: str = settings.csv_processing_config_path, csv_dir: str = settings.csv_data_directory):
        self.config_path = config_path
        self.csv_dir = csv_dir
        self.csv_configs: List[CSVConfigurationModel] = []
        self.all_processed_data: List[Dict[str, Any]] = []

        self._load_yaml_config()
        if self.csv_configs: # Only process if configs were loaded successfully
            self._process_all_csvs()
        else:
            logger.warning(f"No valid CSV configurations loaded from '{config_path}'. CSVConnector will not process any files.")

    def _load_yaml_config(self):
        """Loads and validates the CSV processing configurations from the YAML file using Pydantic."""
        if not os.path.exists(self.config_path):
            logger.error(f"CRITICAL: CSV processing configuration file not found: '{self.config_path}'. Cannot proceed.")
            return # Do not proceed if config file is missing

        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                yaml_data = yaml.safe_load(f)

            if not yaml_data or 'csv_configurations' not in yaml_data:
                logger.error(f"'csv_configurations' key not found or YAML file is empty in '{self.config_path}'.")
                return # Do not proceed if essential key is missing

            # Validate the structure using Pydantic
            validated_config = AllCSVConfigurationsModel.model_validate(yaml_data)
            self.csv_configs = validated_config.csv_configurations
            logger.info(f"Successfully loaded and validated {len(self.csv_configs)} CSV configurations from '{self.config_path}'.")

        except ValidationError as e:
            logger.error(f"CRITICAL: Validation error in CSV configuration file '{self.config_path}':\n{e}", exc_info=False) # Pydantic error is usually descriptive
            self.csv_configs = [] # Ensure configs list is empty on validation failure
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration file '{self.config_path}': {e}", exc_info=True)
            self.csv_configs = []
        except Exception as e: # Catch-all for other unexpected errors
            logger.error(f"An unexpected error occurred while loading or validating YAML config '{self.config_path}': {e}", exc_info=True)
            self.csv_configs = []

    def _find_config_for_file(self, file_name: str) -> Optional[CSVConfigurationModel]:
        """Finds the first matching Pydantic configuration model for a given file name."""
        for conf_model in self.csv_configs:
            if fnmatch.fnmatch(file_name, conf_model.file_pattern): # Use fnmatch for wildcard support
                logger.debug(f"File '{file_name}' matched pattern '{conf_model.file_pattern}' from config '{conf_model.name}'.")
                return conf_model
        logger.warning(f"No configuration found for file '{file_name}'. It will be skipped.")
        return None

    def _unify_row_text_and_metadata(self, row: Dict[str, Any], file_config_model: CSVConfigurationModel, source_file: str, global_row_idx_offset: int, batch_row_idx: int) -> Optional[Dict[str, Any]]:
        """Processes a single row based on its matched Pydantic configuration model."""
        try:
            text_unification_conf = file_config_model.text_unification
            cols_for_doc = text_unification_conf.columns_for_searchable_document
            format_string = text_unification_conf.format_string
            searchable_document = ""
            # Calculate an approximate global row index for logging/ID generation if needed
            current_global_row_idx = global_row_idx_offset + batch_row_idx 

            if format_string:
                try:
                    # Ensure all values are strings for formatting, handle None gracefully
                    format_args = {key: str(row.get(key, "")) for key in cols_for_doc}
                    searchable_document = format_string.format(**format_args)
                except KeyError as e: # If format_string refers to a key not in cols_for_doc or row
                    logger.warning(f"KeyError formatting string for {source_file}, global row approx {current_global_row_idx}: {e}. Key might be missing from 'columns_for_searchable_document' or the CSV row. Falling back to simple join.")
                    # Fallback: simple join of specified columns if formatting fails
                    doc_parts = [f"{col_name}: {str(row.get(col_name, ''))}" for col_name in cols_for_doc if str(row.get(col_name, '')).strip()]
                    searchable_document = ". ".join(doc_parts)
            else: # No format_string provided, just join the specified columns
                doc_parts = [f"{col_name}: {str(row.get(col_name, ''))}" for col_name in cols_for_doc if str(row.get(col_name, '')).strip()] # Only include non-empty, non-whitespace values
                searchable_document = ". ".join(doc_parts)

            if not searchable_document.strip(): # If the document is empty or only whitespace
                logger.debug(f"Skipping row approx {current_global_row_idx} from {source_file} (empty searchable_document after unification).")
                return None

            metadata_conf = file_config_model.metadata_extraction
            orig_id_col = metadata_conf.original_row_identifier_column
            
            original_row_id_value = row.get(orig_id_col) if orig_id_col else None
            if original_row_id_value is not None and str(original_row_id_value).strip():
                 original_row_id = str(original_row_id_value)
            else: # Fallback if original_row_identifier_column is not specified, or its value is missing/empty
                 original_row_id = f"autogen_{os.path.splitext(source_file)[0]}_row_{current_global_row_idx}"


            # Extract specified key_original_fields, ensuring values are not None
            key_fields = {col_name: row.get(col_name) for col_name in metadata_conf.columns_for_key_original_fields if row.get(col_name) is not None}

            return {
                "searchable_document": searchable_document,
                "source_file": source_file,
                "original_row_id": original_row_id,
                "key_original_fields": key_fields
            }
        except Exception as e: # Catch-all for unexpected errors during row processing
            logger.error(f"Error processing row approx {global_row_idx_offset + batch_row_idx} from {source_file} with config '{file_config_model.name}': {e}", exc_info=True)
            return None

    def _process_all_csvs(self):
        """Scans the CSV directory, and for each file, finds a config and processes it in batches."""
        if not os.path.isdir(self.csv_dir):
            logger.error(f"CSV data directory not found: '{self.csv_dir}'.")
            return

        overall_processed_record_count = 0
        for file_name in os.listdir(self.csv_dir):
            if not file_name.lower().endswith('.csv'): # Simple check for CSV files
                continue

            file_path = os.path.join(self.csv_dir, file_name)
            file_config_model = self._find_config_for_file(file_name)

            if not file_config_model: # If no config matches, skip this file
                continue

            parser_opts_model = file_config_model.parser_options
            # Determine batch size: use specific from config if provided, else global default from config.py
            batch_size_for_file = parser_opts_model.csv_reader_batch_size if parser_opts_model.csv_reader_batch_size is not None else settings.csv_reader_batch_size
            logger.info(f"Processing '{file_path}' using config '{file_config_model.name}' with batch_size={batch_size_for_file}.")

            try:
                # Prepare Polars dtypes if specified in config
                polars_dtypes_actual = None
                if parser_opts_model.dtypes:
                    # Convert string dtype names from config to actual Polars dtype objects
                    polars_dtypes_actual = {}
                    for col, dtype_str in parser_opts_model.dtypes.items():
                        try:
                            polars_dtype = getattr(pl, dtype_str)
                            polars_dtypes_actual[col] = polars_dtype
                        except AttributeError:
                            logger.warning(f"Polars dtype '{dtype_str}' for column '{col}' in config for {file_name} is invalid and will be ignored.")
                    if not polars_dtypes_actual: # If all specified dtypes were invalid
                        polars_dtypes_actual = None


                file_processed_record_count = 0
                global_row_idx_offset = 0 # For generating unique IDs or logging approximate global row number

                # Use Polars read_csv_batched for memory efficiency
                batched_reader = pl.read_csv_batched(
                    source=file_path,
                    separator=parser_opts_model.separator,
                    has_header=parser_opts_model.has_header,
                    encoding=parser_opts_model.encoding, # e.g., 'utf-8' or 'utf-8-lossy'
                    ignore_errors=parser_opts_model.ignore_errors, # Try to skip problematic rows
                    infer_schema_length=parser_opts_model.infer_schema_length, # How many rows to use for schema inference
                    dtypes=polars_dtypes_actual, # Predefined dtypes
                    null_values=parser_opts_model.null_values if parser_opts_model.null_values else None,
                    truncate_ragged_lines=parser_opts_model.truncate_ragged_lines, # Strings to interpret as null
                    batch_size=batch_size_for_file # Number of rows per DataFrame batch
                )

                while True:
                    batches = batched_reader.next_batches(1) # Get one DataFrame batch
                    if not batches: # No more data
                        break
                    
                    batch_df = batches[0]
                    if batch_df.is_empty(): # Skip if the batch is empty
                        continue

                    logger.debug(f"Processing batch of {batch_df.height} rows from '{file_name}'. Offset: {global_row_idx_offset}")
                    
                    # Process each row in the current DataFrame batch
                    for i in range(batch_df.height):
                        row_dict = batch_df.row(i, named=True) # Get row as dictionary
                        
                        processed_record = self._unify_row_text_and_metadata(
                            row_dict, 
                            file_config_model, 
                            file_name, 
                            global_row_idx_offset, 
                            i # current row index within this batch
                        )
                        if processed_record:
                            self.all_processed_data.append(processed_record)
                            file_processed_record_count += 1
                    global_row_idx_offset += batch_df.height # Update the global offset

                logger.info(f"Successfully processed {file_processed_record_count} records from '{file_name}'.")
                overall_processed_record_count += file_processed_record_count

            except Exception as e: # Catch errors during reading or processing of a specific file
                logger.error(f"Failed to read or process CSV file '{file_path}' in batches: {e}", exc_info=True)
                # Continue to the next file if one fails
        
        logger.info(f"Finished processing all CSVs. Total processed records suitable for indexing: {overall_processed_record_count}.")


    def get_processed_data(self) -> List[Dict[str, Any]]:
        """Returns all processed data records from all CSVs."""
        if not self.all_processed_data and self.csv_configs: # If configs exist but no data was processed
             logger.warning("get_processed_data: No data was processed. Check CSV directory, file patterns in config, or previous error logs.")
        return self.all_processed_data

    def search_processed_data(self, query_text: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Performs a simple keyword search on the 'searchable_document' of locally processed data."""
        if not self.all_processed_data:
            logger.warning("No processed CSV data available to search locally (keyword search).")
            return []
        if not query_text: # or not query_text.strip()
            logger.warning("Query text is empty for local CSV keyword search.")
            return []

        query_lower = query_text.lower()
        # This is a simple "contains" search, not ranked by relevance beyond order of appearance.
        # For more advanced keyword search, consider libraries like Whoosh or a proper search engine.
        matching_records = []
        for record in self.all_processed_data:
            doc = record.get("searchable_document")
            if isinstance(doc, str) and query_lower in doc.lower():
                matching_records.append(record)
                if len(matching_records) == top_k: # Stop once top_k are found
                    break
        
        logger.info(f"Local CSV keyword search for '{query_text}' found {len(matching_records)} matches (limited to top_k={top_k}).")
        return matching_records


class JSONConnector:
    """
    Processes JSON files from a specified directory based on a configuration.
    Extracts text for keyword searching and structured metadata for context enrichment.

    The connector reads each JSON file, expecting either a single JSON object or a list
    of JSON objects. It then transforms these objects into a standardized format,
    including a 'searchable_document' field and 'key_original_fields' for metadata.
    """
    def __init__(self, json_dir: str = settings.json_data_directory, processing_config: Dict = settings.json_processing_config):
        """
        Initializes the JSONConnector.

        Args:
            json_dir (str): The directory containing JSON files to process.
                            Defaults to `config.JSON_DATA_DIRECTORY`.
            processing_config (Dict): A dictionary defining how to process JSON files.
                                      It should specify 'key_fields' (for metadata extraction)
                                      and 'search_fields' (for creating the searchable document).
                                      Defaults to `config.JSON_PROCESSING_CONFIG`.
        """
        self.json_dir = json_dir
        self.processing_config = processing_config
        self.all_processed_data: List[Dict[str, Any]] = []
        # Use the module-level logger, consistent with other classes in this file.
        # self.logger = logging.getLogger(__name__) # This was creating a new logger instance effectively
        # No, using logger = logging.getLogger(__name__) at the top of the file is correct and standard.
        # The self.logger assignment was redundant if logger is already defined at module scope.
        # Re-confirmed: The original self.logger = logging.getLogger(__name__) is fine.
        # My comment above was incorrect. Using the existing module `logger` directly is also fine,
        # but assigning it to self.logger is a common pattern. Let's stick to the existing pattern.
        self.logger = logger 

        self._process_all_json_files()

    def _process_all_json_files(self):
        """
        Scans the JSON directory, reads each .json file, and processes its content.
        JSON files can contain a single JSON object or a list of JSON objects.
        Each object is transformed into a dictionary with fields suitable for searching and ingestion.
        
        Enhanced with safety measures and optimized for large file processing:
        - File size limits to prevent memory issues (configurable, default 500MB)
        - Timeout protection for large file processing (configurable, default 5 minutes)
        - Progress tracking for large files
        - Memory-efficient processing
        - Graceful error handling and skipping of problematic files
        """
        import signal
        import time
        
        # Configuration for safety limits (now configurable via environment variables)
        MAX_FILE_SIZE_MB = settings.json_max_file_size_mb
        MAX_PROCESSING_TIME_SECONDS = settings.json_max_processing_time_seconds
        
        def timeout_handler(signum, frame):
            raise TimeoutError("JSON file processing timeout")
        
        self.logger.info(f"Starting JSON data processing from directory: '{self.json_dir}'.")
        self.logger.info(f"Processing limits: Max file size: {MAX_FILE_SIZE_MB}MB, Max processing time: {MAX_PROCESSING_TIME_SECONDS}s")
        
        if not os.path.isdir(self.json_dir):
            self.logger.error(f"JSON data directory '{self.json_dir}' not found. No JSON files will be processed.")
            return

        processed_object_count = 0
        skipped_files = []
        total_files_found = len([f for f in os.listdir(self.json_dir) if f.lower().endswith('.json')])
        
        self.logger.info(f"Found {total_files_found} JSON files to process")
        
        for file_index, file_name in enumerate(os.listdir(self.json_dir), 1):
            if not file_name.lower().endswith('.json'): # Process only .json files
                continue

            file_path = os.path.join(self.json_dir, file_name)
            
            # Check file size before processing
            try:
                file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                if file_size_mb > MAX_FILE_SIZE_MB:
                    self.logger.warning(f"Skipping large JSON file '{file_name}' ({file_size_mb:.1f}MB > {MAX_FILE_SIZE_MB}MB limit). Consider processing separately or increasing JSON_MAX_FILE_SIZE_MB.")
                    skipped_files.append(f"{file_name} (too large: {file_size_mb:.1f}MB)")
                    continue
                    
                self.logger.info(f"[{file_index}/{total_files_found}] Processing JSON file: '{file_name}' ({file_size_mb:.1f}MB)")
            except OSError as e:
                self.logger.error(f"Could not check size of file '{file_path}': {e}")
                continue

            # Set up timeout protection
            if sys.platform == "win32":
               pass #because SIGALARM is not available on Windows
            else:
               old_handler = signal.signal(signal.SIGALRM, timeout_handler)
               signal.alarm(MAX_PROCESSING_TIME_SECONDS)
            
            try:
                start_time = time.time()
                
                # Try to load and process the JSON file
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f) # Load the entire JSON file content
                    
                loading_time = time.time() - start_time
                self.logger.debug(f"JSON file '{file_name}' loaded in {loading_time:.2f} seconds")
                
            except TimeoutError:
                self.logger.warning(f"Skipping JSON file '{file_name}' due to loading timeout (>{MAX_PROCESSING_TIME_SECONDS}s). File may be too large or complex. Consider increasing JSON_MAX_PROCESSING_TIME_SECONDS.")
                skipped_files.append(f"{file_name} (timeout)")
                continue
            except json.JSONDecodeError:
                self.logger.warning(f"Could not decode JSON from file: '{file_path}'. File will be skipped.")
                skipped_files.append(f"{file_name} (invalid JSON)")
                continue
            except Exception as e: # Catch other file reading errors
                self.logger.error(f"Error reading JSON file '{file_path}': {e}", exc_info=True)
                skipped_files.append(f"{file_name} (read error)")
                continue
            finally:
                # Always restore the original signal handler and cancel the alarm
              if sys.platform != "win32":
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)

            # JSON file can contain a single object or a list of objects
            json_objects = []
            if isinstance(data, list):
                json_objects.extend(data)
                self.logger.info(f"JSON file '{file_name}' contains {len(json_objects)} objects in an array. Processing ALL {len(json_objects)} objects.")
            elif isinstance(data, dict):
                json_objects.append(data)
                self.logger.info(f"JSON file '{file_name}' contains 1 object. Processing the object.")
            else:
                self.logger.warning(f"JSON file '{file_path}' does not contain a valid JSON object or list of objects. Skipping.")
                skipped_files.append(f"{file_name} (invalid format)")
                continue
            
            # Process each JSON object found in the file
            file_processed_count = 0
            processing_start_time = time.time()
            
            # Show progress for large files
            show_progress = len(json_objects) > 10000
            if show_progress:
                self.logger.info(f"Processing large file with {len(json_objects)} objects. Progress will be logged every 10,000 objects.")
            
            for index, json_obj in enumerate(json_objects):
                if not isinstance(json_obj, dict):
                    self.logger.debug(f"Skipping item at index {index} in '{file_path}' as it is not a dictionary (JSON object).")
                    continue

                # Extract key fields as defined in the processing configuration
                extracted_key_fields = {}
                for key_field in self.processing_config.key_fields:
                #for key_field in self.processing_config.get("key_fields", []):
                    if key_field in json_obj:
                        extracted_key_fields[key_field] = json_obj[key_field]

                # Construct the searchable document from specified search fields
                searchable_document_parts = []
                for search_field in self.processing_config.search_fields:
                    if search_field in json_obj:
                        value = json_obj[search_field]
                        # If a search field contains a list (e.g., tags, comments), extend the document parts
                        if isinstance(value, list): 
                            searchable_document_parts.extend([str(item) for item in value if isinstance(item, (str, int, float))])
                        # If it's a simple value, append it
                        elif isinstance(value, (str, int, float)):
                            searchable_document_parts.append(str(value))
                        # Other types in search_fields are ignored for text generation.
                
                searchable_document = ". ".join(searchable_document_parts) # Join parts with a period and space

                # Determine the original_row_id for this JSON object
                original_row_id = None
                # Priority 1: Use "id" field if it was extracted as a key_field and is present
                if "id" in extracted_key_fields and extracted_key_fields["id"] is not None:
                    original_row_id = str(extracted_key_fields["id"])
                else:
                    # Priority 2: Fallback to the first non-null extracted key_field as an identifier
                    for kf_name, kf_value in extracted_key_fields.items():
                        if kf_value is not None:
                            original_row_id = str(kf_value)
                            self.logger.debug(f"Used fallback key_field '{kf_name}' for original_row_id in '{file_path}'.")
                            break
                
                # Priority 3: If no suitable key_field found, generate an ID based on filename and index
                if original_row_id is None: 
                    original_row_id = f"{os.path.splitext(file_name)[0]}_obj_{index}"
                    self.logger.debug(f"Generated fallback original_row_id '{original_row_id}' for object in '{file_name}' at index {index}.")

                # Skip if the object yields no searchable text and no key fields were extracted
                if not searchable_document.strip() and not extracted_key_fields:
                    self.logger.debug(f"Skipping object in '{file_name}' at index {index} due to empty searchable_document and no key_fields.")
                    continue
                
                # Append the processed record
                self.all_processed_data.append({
                    "searchable_document": searchable_document,
                    "source_file": file_name, # Store the original filename
                    "original_row_id": original_row_id, # Store the determined/generated ID
                    "key_original_fields": extracted_key_fields, # These are the metadata fields
                    "original_data": json_obj # Store the complete original JSON object for reference
                })
                processed_object_count += 1
                file_processed_count += 1

                
                # Progress logging for large files
                if show_progress and (index + 1) % 10000 == 0:
                    elapsed_time = time.time() - processing_start_time
                    rate = (index + 1) / elapsed_time
                    estimated_total_time = len(json_objects) / rate
                    remaining_time = estimated_total_time - elapsed_time
                    self.logger.info(f"Progress: {index + 1:,}/{len(json_objects):,} objects processed ({(index + 1)/len(json_objects)*100:.1f}%) - Rate: {rate:.0f} objects/sec - ETA: {remaining_time:.1f}s")
            
            processing_time = time.time() - processing_start_time
            if file_processed_count > 0:
                rate = file_processed_count / processing_time
                self.logger.info(f"✅ Successfully processed ALL {file_processed_count:,} objects from '{file_name}' in {processing_time:.2f}s (Rate: {rate:.0f} objects/sec)")
            else:
                self.logger.warning(f"No valid objects found in '{file_name}'")
            
        
        # Summary logging
        if processed_object_count > 0:
            self.logger.info(f"🎉 COMPLETED: Finished processing JSON files. Total processed JSON objects: {processed_object_count:,} from {total_files_found - len(skipped_files)} files.")
        else:
            self.logger.info("Finished processing JSON files. No JSON objects were processed (check directory or file contents).")
            
        if skipped_files:
            self.logger.warning(f"⚠️  Skipped {len(skipped_files)} JSON files due to issues: {', '.join(skipped_files)}")
            self.logger.info("To process large files, consider:")
            self.logger.info("1. Increasing JSON_MAX_FILE_SIZE_MB in your environment variables")
            self.logger.info("2. Increasing JSON_MAX_PROCESSING_TIME_SECONDS for complex files")
            self.logger.info("3. Splitting very large JSON files into smaller chunks")
            self.logger.info("4. Processing them separately outside of startup")

    def get_processed_data(self) -> List[Dict[str, Any]]:
        """
        Returns all processed data records aggregated from all valid JSON files.

        Each record is a dictionary containing:
        - 'searchable_document': The concatenated text from specified search fields.
        - 'source_file': The name of the JSON file from which the record originated.
        - 'original_row_id': An identifier for the object (derived or generated).
        - 'key_original_fields': A dictionary of extracted key metadata fields.
        - 'original_data': The original JSON object.

        Returns:
            List[Dict[str, Any]]: A list of processed data records.
                                 Returns an empty list if no data was processed.
        """
        if not self.all_processed_data:
            self.logger.warning("JSONConnector.get_processed_data: No data was processed or is available. Ensure JSON files exist and were processed correctly.")
        return self.all_processed_data

    def search_processed_data(self, query_text: str, top_k: int = 15) -> List[Dict[str, Any]]:
        """
        Performs a simple case-insensitive keyword search on the 'searchable_document'
        of locally processed JSON data. This method is intended for direct keyword matching
        on the data held by this connector instance.

        Args:
            query_text (str): The text to search for.
            top_k (int): The maximum number of matching records to return. Defaults to 15.

        Returns:
            List[Dict[str, Any]]: A list of search result items, structured for compatibility
                                 with the agent_framework. Each item includes:
                                 - 'source': "json_keyword"
                                 - 'score': A simple match indicator (1.0 for now).
                                 - 'document': The 'searchable_document' of the matching record.
                                 - 'metadata': Contains 'source_file', 'original_data',
                                               'key_fields' (from 'key_original_fields'),
                                               and 'original_row_id'.
                                 Returns an empty list if no data is processed, query is empty,
                                 or no matches are found.
        """
        if not self.all_processed_data:
            self.logger.warning("JSONConnector.search_processed_data: No processed JSON data available to search.")
            self.logger.warning("JSONConnector.search_processed_data: No processed JSON data available to search.")
            return []
        if not query_text or not query_text.strip(): # Check for empty or whitespace-only query
            self.logger.warning("JSONConnector.search_processed_data: Query text is empty. Returning no results.")
            return []

        query_lower = query_text.lower() # Case-insensitive search
        matching_records = []

        for record in self.all_processed_data:
            doc = record.get("searchable_document", "")
            if query_lower in doc.lower(): # Simple substring match
                # Construct the search result item in the format expected by agent_framework
                search_result_item = {
                    "source": "json_keyword", # Identifies the origin of this result
                    "score": 1.0, # Basic score for keyword match; could be enhanced
                    "document": doc,
                    "metadata": {
                        "source_file": record.get("source_file", "N/A"),
                        "original_data": record.get("original_data", {}), # The full original JSON object
                        # 'key_fields' in the output metadata should contain the 'key_original_fields'
                        # extracted during processing, as this is what agent_framework expects for JSON results.
                        "key_fields": record.get("key_original_fields", {}), 
                        "original_row_id": record.get("original_row_id", "N/A")
                    }
                }
                matching_records.append(search_result_item)
        
        # Sort results: prioritize matches where query appears earlier, then by document length (shorter is better).
        # This is a basic heuristic for relevance.
        matching_records.sort(key=lambda x: (
            x["document"].lower().find(query_lower), # Position of query in document
            len(x["document"]) # Length of document
        ))
        # The previous sort key `not x["document"].lower().startswith(query_lower)` was good too.
        # `find` gives more granular sorting if the term is not at the start.

        final_results = matching_records[:top_k] # Return only the top_k results
        
        if final_results:
            self.logger.info(f"JSONConnector.search_processed_data: Found {len(final_results)} local JSON matches for '{query_text}' (top_k={top_k}).")
        else:
            self.logger.info(f"JSONConnector.search_processed_data: No local JSON matches found for '{query_text}'.")
            
        return final_results
