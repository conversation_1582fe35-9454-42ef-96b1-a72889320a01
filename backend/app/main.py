# app/main.py
import os
from pathlib import Path
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from .routes import ad_targeting, video_editing, marketing_ai
from .config import AUDIO_DIR
import logging


logger = logging.getLogger(__name__)

app = FastAPI()


CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.abspath(os.path.join(CURRENT_DIR, '../'))
AUDIO_DIR = Path(os.path.join(PROJECT_DIR, 'audio'))
AUDIO_DIR.mkdir(parents=True, exist_ok=True)


# CORS 设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000", 
        "http://127.0.0.1:3000",
        "https://fylow.com",
        "https://www.fylow.com",
        "http://fylow.com",
        "http://www.fylow.com"
    ],
    allow_credentials=True,
    allow_methods=["*"],  
    allow_headers=["*"],  
)

# app.include_router(ad_targeting.router, prefix="/ad_targeting", tags=["ad targeting"])
# app.include_router(video_editing.router, prefix="/video_editing", tags=["video editing"])
app.include_router(marketing_ai.router, prefix="/marketing-ai", tags=["marketing ai"])

# print(f"Mounting audio directory: {AUDIO_DIR.absolute()}")
# app.mount("/audio", StaticFiles(directory=str(AUDIO_DIR)), name="audio")

# @app.get("/audio/{path:path}")
# async def serve_audio(path: str):
#     try:
#         file_path = AUDIO_DIR / path
#         logger.info(f"Requested audio file: {file_path}")
        
#         if not file_path.exists() or not file_path.is_file():
#             logger.error(f"File not found: {file_path}")
#             raise HTTPException(status_code=404, detail="Audio file not found")
        
#         headers = {
#             'Accept-Ranges': 'bytes',
#             'Content-Type': 'audio/mpeg',
#             'Cache-Control': 'public, max-age=3600',
#             'Access-Control-Allow-Origin': '*',
#         }
        
#         return FileResponse(
#             path=file_path,
#             media_type='audio/mpeg',
#             headers=headers
#         )
#     except Exception as e:
#         logger.error(f"Error serving audio file: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))