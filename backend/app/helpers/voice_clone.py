import os
import json
import requests
from typing import List, Optional
from pathlib import Path
from ..config import settings

def clone_voice(
    name: str,
    description: str,
    files: List[str],
    remove_background_noise: bool = True,
    labels: Optional[dict] = None
) -> str:
    """
    Clone a voice using ElevenLabs API
    
    Args:
        name: Voice name
        description: Voice description
        files: List of audio file paths
        remove_background_noise: Whether to remove background noise
        labels: Dictionary of labels
    
    Returns:
        Voice ID if successful, empty string if failed
    """
    url = "https://api.elevenlabs.io/v1/voices/add"
    opened_files = []
    
    try:
        # Prepare form data
        form_data = {
            'name': name,
            'description': description,
            'remove_background_noise': str(remove_background_noise).lower()
        }
        
        if labels:
            form_data['labels'] = json.dumps(labels)
        
        # Prepare files
        files_data = []
        for file_path in files:
            if os.path.exists(file_path):
                file_obj = open(file_path, 'rb')
                opened_files.append(file_obj)
                files_data.append(
                    ('files', (os.path.basename(file_path), file_obj, 'audio/wav'))
                )
            else:
                print(f"File not found: {file_path}")
        
        if not files_data:
            print("No valid audio files found")
            return ''
        
        # Set headers
        headers = {
            'xi-api-key': settings.elevenlabs_api_key,
            'Accept': 'application/json'
        }
        
        # Send request
        response = requests.post(
            url,
            data=form_data,
            files=files_data,
            headers=headers
        )
        
        # Check response
        if response.status_code == 200:
            result = response.json()
            voice_id = result.get('voice_id', '')
            if voice_id:
                print(f"Voice cloning successful! Voice ID: {voice_id}")
                return voice_id
            else:
                print("Could not get voice_id")
                return ''
        elif response.status_code == 422:
            error_detail = response.json().get('detail', [])
            print("Request parameter error:")
            for error in error_detail:
                print(f"- Location: {error.get('loc')}")
                print(f"- Message: {error.get('msg')}")
                print(f"- Type: {error.get('type')}")
            return ''
        else:
            print(f"Request failed, status code: {response.status_code}")
            print(f"Error message: {response.text}")
            return ''
            
    except Exception as e:
        print(f"Error during voice cloning: {e}")
        return ''
    finally:
        # Close all opened files
        for f in opened_files:
            try:
                f.close()
            except:
                pass
