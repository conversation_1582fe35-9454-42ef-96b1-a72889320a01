import json
import requests
import re
from typing import List, Dict, Any, Union, Generator, Tu<PERSON>
from app.models.adTargeting import Section, SubSection
from app.prompts.prompts import PROMPTS
from openai import OpenAI
from pydantic import BaseModel
from fastapi import HTTPException
from ..config import settings
from contextlib import contextmanager
import logging
import time
import os
import pandas as pd
import concurrent.futures
from functools import partial

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

client = OpenAI(api_key=settings.openai_api_key)

@contextmanager
def timer(name: str):
    """Context manager for timing code blocks and logging progress"""
    start_time = time.time()
    logger.info(f"Starting {name}...")
    yield
    elapsed_time = time.time() - start_time
    logger.info(f"Finished {name} in {elapsed_time:.2f} seconds")


def extract_json_content(output_string):
    match = re.search(r'```json\n(.*?)\n```', output_string, re.DOTALL)
    if match:
        json_content_str = match.group(1)
        try:
            return json.loads(json_content_str)
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {e}", flush=True)
    return None


from typing import List, Dict, Any, Union
from pydantic import BaseModel
from fastapi import HTTPException
from fastapi.responses import StreamingResponse
import aiohttp
import json

async def create_new_session(
    api_key: str,
    chat_id: str,
    ragflow_base_url: str
) -> str:
    """
    Creates a new session on RAG.

    Parameters
    ----------
    api_key : str
        The API key to authenticate with the RAG API.
    chat_id : str
        The ID of the chat to create a session for.
    ragflow_base_url : str
        The base URL of the RAG API

    Returns
    -------
    str
        The response data from the API containing the session information.

    Raises
    ------
    requests.exceptions.RequestException
        If the API request fails.
    """
    url = f"{ragflow_base_url}/api/v1/chats/{chat_id}/sessions"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    payload = {
        "name": "rag_adTargeting"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status
                    )
                
                data = await response.json()
                return data['data']['id']
    
    except Exception as e:
        raise Exception(f"Failed to create session: {str(e)}")


async def delete_sessions(
    api_key: str,
    chat_id: str,
    session_ids: List[str],
    ragflow_base_url: str
) -> dict:
    """
    Deletes multiple sessions from a chat by their IDs.

    Parameters
    ----------
    api_key : str
        The API key to authenticate with the RAG API.
    chat_id : str
        The ID of the chat containing the sessions.
    session_ids : List[str]
        List of session IDs to delete.
    ragflow_base_url : str
        The base URL of the RAG API

    Returns
    -------
    dict
        The response data from the API.

    Raises
    ------
    ValueError
        If session_ids list is empty.
    requests.exceptions.RequestException
        If the API request fails.
    """
    if not session_ids:
        raise ValueError("session_ids list cannot be empty")

    url = f"{ragflow_base_url}/api/v1/chats/{chat_id}/sessions"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    payload = {
        "ids": session_ids
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.delete(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status
                    )
                
                return await response.json()
    
    except Exception as e:
        raise Exception(f"Failed to delete sessions: {str(e)}")

class ChatCompletionRequest(BaseModel):
    question: str
    stream: bool = False

class ChatResponse(BaseModel):
    code: int
    data: Union[str, bool, dict]

async def handle_chat_completion(
    api_key: str,
    chat_id: str,
    question: str,
    ragflow_base_url: str,
    stream: bool = False,
    timeout: int = 300  # 5 minutes timeout
) -> Union[Dict[str, Any], aiohttp.StreamReader]:
    """
    Handle conversation with chat assistant asynchronously, including session creation and cleanup
    """
    session_id = None
    client_session = None
    
    try:
        # Create session
        session_id = await create_new_session(api_key, chat_id, ragflow_base_url)
        if not session_id:
            raise ValueError("Failed to get session ID from response")

        # Create client session with timeout
        timeout_config = aiohttp.ClientTimeout(total=timeout)
        client_session = aiohttp.ClientSession(timeout=timeout_config)

        # Send chat completion request
        url = f"{ragflow_base_url}/api/v1/chats/{chat_id}/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        payload = {
            "question": question,
            "stream": stream,
            "session_id": session_id
        }

        try:
            response = await client_session.post(url, headers=headers, json=payload)
            if response.status != 200:
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status
                )

            if stream:
                return StreamingResponse(
                    content=handle_stream(response, client_session, api_key, chat_id, session_id, ragflow_base_url),
                    media_type="text/event-stream"
                )
            else:
                result = await response.json()
                await client_session.close()
                
                # Clean up session
                try:
                    await delete_sessions(api_key, chat_id, [session_id], ragflow_base_url)
                except Exception as e:
                    logger.warning(f"Warning: Failed to delete session: {str(e)}")
                
                return result

        except asyncio.TimeoutError:
            logger.error("Request timed out")
            raise HTTPException(
                status_code=504,
                detail="Request timed out. Please try again."
            )

    except Exception as e:
        # Ensure session cleanup even on error
        if client_session:
            await client_session.close()
            
        if session_id:
            try:
                await delete_sessions(api_key, chat_id, [session_id], ragflow_base_url)
            except Exception as del_e:
                logger.warning(f"Error deleting session after failure: {str(del_e)}")
        
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=500,
            detail=f"Chat completion failed: {str(e)}"
        )

async def handle_stream(
    response: aiohttp.ClientResponse,
    client_session: aiohttp.ClientSession,
    api_key: str,
    chat_id: str,
    session_id: str,
    ragflow_base_url: str
):
    """
    Handle streaming response and cleanup
    """
    try:
        async for line in response.content:
            if line:
                try:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data:'):
                        data = line_str[5:].strip()
                        yield f"data: {data}\n\n"
                except json.JSONDecodeError:
                    continue
    finally:
        # Cleanup
        await client_session.close()
        try:
            await delete_sessions(api_key, chat_id, [session_id], ragflow_base_url)
        except Exception as e:
            print(f"Error deleting session: {str(e)}")


def get_section_prompts(audience) -> Dict[str, str]:
    """
    Generate prompts for each section based on the ad targeting request.

    Parameters
    ----------
    audience : str
        The target audience for the ad.

    Returns
    -------
    Dict[str, str]
        A dictionary with section names as keys and their prompts as values.
    """
    return {
        "Social Media Platforms": PROMPTS["social_media"].format(audience=audience),
        "Demographic": PROMPTS["demographics"].format(audience=audience),
        "Behavior": PROMPTS["behavior"].format(audience=audience),
        "Interests": PROMPTS["interests"].format(audience=audience),
        "Psychographic Segmentation": PROMPTS["psychographic"].format(audience=audience),
        "Keywords and Phrases": PROMPTS["keywords"].format(audience=audience),
        # "Competitor Analysis": PROMPTS["competitor"].format(audience=audience)
    }



from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class AnalysisConfig:
    """Configuration for different types of analysis"""
    prompt_key: str
    section_id: str
    section_title: str
    section_type: str
    default_data: Any

async def analyze_audience(
    audience: str,
    analysis_type: str,
    configs: Dict[str, AnalysisConfig]
):
    """
    Generic analysis function for different aspects of audience analysis using RAG.
    
    Parameters
    ----------
    audience : str
        The target audience/product to analyze
    analysis_type : str
        Type of analysis to perform (e.g., 'social_media', 'demographics')
    configs : Dict[str, AnalysisConfig]
        Configuration dictionary containing settings for different analysis types
        
    Returns
    -------
    Section
        A Section object containing the analysis results
    """
    try:
        # Get configuration for the requested analysis type
        config = configs.get(analysis_type)
        if not config:
            raise ValueError(f"Unknown analysis type: {analysis_type}")
            
        # Get the predefined prompt and format it with the audience
        prompt = PROMPTS[config.prompt_key].format(audience=audience)
        
        # Use handle_chat_completion to get response
        response = await handle_chat_completion(
            api_key=settings.rag_api_key,
            chat_id=settings.rag_chat_id,
            question=prompt,
            stream=False,
            ragflow_base_url=settings.rag_base_url
        )
        
        # Extract data from response
        extracted_data = extract_json_content(response['data']['answer'])
        data = extracted_data if extracted_data else config.default_data
        
        if analysis_type == "social_media":
            return Section(
                id=config.section_id,
                title="Recommended Distribution Channels",
                type=config.section_type,
                data=data
            )
        else:
        # Create and return Section
            return Section(
                id=config.section_id,
                title=config.section_title,
                type=config.section_type,
                data=data
            )
        
    except Exception as e:
        print(f"Error in {analysis_type} analysis: {str(e)}")
        raise  # Re-raise the exception for proper error handling

# Configuration for different types of analysis
ANALYSIS_CONFIGS = {
    "social_media": AnalysisConfig(
        prompt_key="social_media",
        section_id="ch-001",
        section_title="Recommended Distribution Channels",
        section_type="tags",
        default_data=["Instagram", "YouTube", "TikTok"]
    ),
    "demographics": AnalysisConfig(
        prompt_key="demographics",
        section_id="demo-001",
        section_title="Demographics",
        section_type="list",
        default_data=[
            "Age range: 25-40",
            "Gender: Both",
            "Location: Urban areas, Suburban areas",
            "Income Level: Middle income, Upper-middle income",
            "Education Level: College, Bachelors degree"
        ]
    ),
    "interests": AnalysisConfig(
        prompt_key="interests",
        section_id="int-001",
        section_title="Interests",
        section_type="list",
        default_data=[
            "Activities: Gaming, Work/Office Use, Home/Personal Use, Streaming",
            "Preferences: Ease of Assembly, Sturdiness and Build Quality, Design Features, Size Appropriateness",
            "Pastimes: Multi-Monitor Setup, Customization and Versatility",
        ]
    ),
    "keywords": AnalysisConfig(
        prompt_key="keywords",
        section_id="keywords-001",
        section_title="Keywords and Phrases",
        section_type="list",
        default_data=[
            "Ease of Assembly",
            "Value for Money",
            "Design",
            "Sturdiness",
            "Size",
            "Functionality",
            "LED Lights",
            "Durability Issues (Negative)",
            "Screw Quality Issues (Negative)",
        ]
    )
}

from pydantic import BaseModel
from typing import Dict, List, Optional
import asyncio
from openai import OpenAI

# Pydantic Models
class Review(BaseModel):
    description: str
    reviewer: Optional[str] = None
    link: Optional[str] = None

class ReviewGroup(BaseModel):
    subTitle: str
    reviews: List[Review]

class Description(BaseModel):
    subTitle: str
    description: str

class SubSection(BaseModel):
    id: str
    subTitle: str
    descriptions: Optional[List[Description]] = None
    reviewGroups: Optional[List[ReviewGroup]] = None

# Configuration
@dataclass
class SectionConfig:
    """Configuration for analysis sections with subsections"""
    section_id: str
    section_title: str
    subsection_id_prefix: str
    default_data: List[str]

# Configuration
BEHAVIOR_CONFIGS = {
    "Usage Pattern": SectionConfig(
        section_id="beh-001",
        section_title="Usage Patterns",
        subsection_id_prefix="beh",
        default_data=[
            "Frequent use during work hours",
            "Daily usage for extended periods",
            "Multiple device connections"
        ]
    ),
    "Satisfaction Level": SectionConfig(
        section_id="beh-002",
        section_title="Satisfaction Factors",
        subsection_id_prefix="beh",
        default_data=[
            "High satisfaction with durability",
            "Moderate satisfaction with performance"
        ]
    ),
    "Buying Behavior": SectionConfig(
        section_id="beh-003",
        section_title="Purchase Behavior",
        subsection_id_prefix="beh",
        default_data=[
            "Researches extensively before purchase",
            "Compares multiple options",
            "Reads reviews and ratings"
        ]
    )
}

PSYCHOGRAPHIC_CONFIGS = {
    "Personality": SectionConfig(
        section_id="psy-001",
        section_title="Personality Traits",
        subsection_id_prefix="psy",
        default_data=[
            "Tech-savvy",
            "Quality-conscious",
            "Practical"
        ]
    ),
    "Lifestyle": SectionConfig(
        section_id="psy-002",
        section_title="Lifestyle Attributes",
        subsection_id_prefix="psy",
        default_data=[
            "Work-life balance focused",
            "Tech-savvy"
        ]
    ),
    "Value": SectionConfig(
        section_id="psy-003",
        section_title="Core Values",
        subsection_id_prefix="psy",
        default_data=[
            "Values efficiency",
            "Appreciates good design",
            "Environmentally conscious"
        ]
    )
}

# GPT Processing
SYSTEM_PROMPT = """You are an expert at analyzing and structuring content into clear, concise insights. Your task is to convert detailed content into structured data following specific guidelines."""

USER_PROMPT_TEMPLATE = """Given the following detailed content:

        {content}

        Transform this content into structured insights following these guidelines:

        1. Create concise, impactful summaries that:
        - Focus on key points and main ideas
        - Keep sentences brief (2-3 lines maximum)
        - Include reviewer names in [brackets] when available

        2. Structure the output to match the SubSection model with:
        - id: Use a meaningful identifier (e.g., "{id_prefix}-001" for {type}-related content)
        - subTitle: A brief section heading
        - descriptions: List of Description objects containing subTitle and description
        - reviewGroups: List of ReviewGroup objects containing subTitle and reviews"""


def get_config(type: str) -> Dict:
    if type in BEHAVIOR_CONFIGS:
        return BEHAVIOR_CONFIGS[type]
    elif type in PSYCHOGRAPHIC_CONFIGS:
        return PSYCHOGRAPHIC_CONFIGS[type]
    raise ValueError(f"Invalid type: {type}")

async def process_gpt_insights(
    content: str,
    type: str,
    id_prefix: str,
    client: OpenAI
) -> SubSection:
    """Process content through GPT and return structured insights"""
    try:
        config = get_config(type)
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-2024-08-06",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {
                    "role": "user",
                    "content": USER_PROMPT_TEMPLATE.format(
                        content=content,
                        id_prefix=id_prefix,
                        type=type
                    )
                }
            ],
            response_format=SubSection,
        )
        return completion.choices[0].message.parsed
        
    except ValueError as e:
        print(f"Configuration error: {str(e)}")
        raise
        
    except Exception as e:
        print(f"Error in GPT processing: {str(e)}")
        raise


def parse_sections(text):
    """
    Parse text with section markers into a dictionary with standardized keys
    """
    # 首先检查输入类型
    if not isinstance(text, str):
        if text is None:
            return {}
        try:
            text = str(text)
        except Exception as e:
            logger.error(f"Error converting text to string: {str(e)}")
            return {}

    section_mapping = {
        'USAGE SECTION': 'Usage Pattern',
        'SATISFACTION SECTION': 'Satisfaction Level',
        'BUYING SECTION': 'Buying Behavior',
        'PERSONALITY SECTION': 'Personality',
        'LIFESTYLE SECTION': 'Lifestyle',
        'VALUE[S]* SECTION': 'Value'
    }
    
    sections = {}
    # Clean special markers (##number$$) and extra spaces
    cleaned_text = re.sub(r'##\d+\$+', '', text)
    
    # Use regex to match content of each section
    for old_name, new_name in section_mapping.items():
        pattern = rf'\[{old_name} START\](.*?)\[{old_name} END\]'
        matches = re.findall(pattern, cleaned_text, re.DOTALL)
        if matches:
            sections[new_name] = matches[0].strip()
    
    return sections


async def analyze_behavior(audience: str, client: OpenAI) -> List[Section]:
    """
    Analyze user behavior patterns with timing logs
    """
    try:
        with timer("getting behavior prompts"):
            behavior_prompts = PROMPTS["behavior"].format(audience=audience)
        
        processed_sections = []  # List to store processed subsections
        rag_responses = {}
        
        # RAG responses timing
        with timer("RAG processing"):
            try:
                logger.info("Getting RAG response for behavior analysis")
                response = await handle_chat_completion(
                    api_key=settings.rag_api_key,
                    chat_id=settings.rag_chat_id,
                    question=behavior_prompts,
                    stream=False,
                    ragflow_base_url=settings.rag_base_url
                )
                rag_responses = response['data']['answer']
            except Exception as e:
                logger.error(f"Error getting RAG response for behavior analysis: {str(e)}")
                rag_responses = None
                
        section_dict = parse_sections(rag_responses)  # Dictionary of sections

        # GPT processing timing
        with timer("GPT processing"):
            for behavior_type, raw_response in section_dict.items():
                try:
                    logger.info(f"Processing GPT insights for {behavior_type}")
                    config = BEHAVIOR_CONFIGS[behavior_type]
                    
                    if raw_response:
                        subsection = await process_gpt_insights(
                            content=raw_response,
                            type=behavior_type,
                            id_prefix=config.subsection_id_prefix,
                            client=client
                        )
                        
                        #get_reviewer_link for each review
                        if subsection.reviewGroups:
                            for group in subsection.reviewGroups:
                                for review in group.reviews:
                                    if review.reviewer:
                                        new_reviewer = review.reviewer.split(",")[0]
                                        r,l = get_reviewer_link(new_reviewer)
                                        review.reviewer = r
                                        review.link = l
                    
                    else:
                        subsection = SubSection(
                            id=f"{config.subsection_id_prefix}-{len(processed_sections)+1:03d}",
                            subTitle=behavior_type,
                            descriptions=[
                                Description(
                                    subTitle="Default Analysis",
                                    description=item
                                ) for item in config.default_data
                            ]
                        )
                
                    processed_sections.append(subsection)
                    
                except Exception as e:
                    logger.error(f"Error processing {behavior_type}: {str(e)}")
                    raise
        
        return {
            "id":"beh-001",
            "title":"Behavior",
            "type": "structured",
            "data":processed_sections
        }
        
    except Exception as e:
        logger.error(f"Error in behavior analysis: {str(e)}")
        raise

# Synchronous wrapper
def analyze_behavior_sync(audience: str, client: OpenAI) -> List[Section]:
    """Synchronous wrapper for behavior analysis"""
    return asyncio.run(analyze_behavior(audience, client))


async def analyze_psychographic(audience: str, client: OpenAI) -> List[Section]:
    """
    Analyze user psychographic patterns with timing logs
    """
    try:
        with timer("getting psychographic prompts"):
            psychographic_prompts = PROMPTS["psychographic"].format(audience=audience)
        
        processed_sections = []  # List to store processed subsections
        rag_responses = {}
        
        # RAG responses timing
        with timer("RAG processing"):
            try:
                logger.info("Getting RAG response for psychographic analysis")
                response = await handle_chat_completion(
                    api_key=settings.rag_api_key,
                    chat_id=settings.rag_chat_id_gpt_4,
                    question=psychographic_prompts,
                    stream=False,
                    ragflow_base_url=settings.rag_base_url
                )
                rag_responses = response['data']['answer']
            except Exception as e:
                logger.error(f"Error getting RAG response for psychographic analysis: {str(e)}")
                rag_responses = None
        logger.info("------------------------------------------------------------")
        logger.info(rag_responses)
        logger.info("------------------------------------------------------------")
                
        section_dict = parse_sections(rag_responses)  # Dictionary of sections
        
        for psychographic_type, raw_response in section_dict.items():
            logger.info(psychographic_type)

        # GPT processing timing
        with timer("GPT processing"):
            for psychographic_type, raw_response in section_dict.items():
                try:
                    logger.info(f"Processing GPT insights for {psychographic_type}")
                    config = PSYCHOGRAPHIC_CONFIGS[psychographic_type]
                    
                    if raw_response:
                        subsection = await process_gpt_insights(
                            content=raw_response,
                            type=psychographic_type,
                            id_prefix=config.subsection_id_prefix,
                            client=client
                        )
                        
                        #check subsection.reviewGroups
                        if subsection.reviewGroups:
                            for group in subsection.reviewGroups:
                                for review in group.reviews:
                                    if review.reviewer:
                                        new_reviewer = review.reviewer.split(",")[0]
                                        r,l = get_reviewer_link(new_reviewer)
                                        review.reviewer = r
                                        review.link = l
                    else:
                        subsection = SubSection(
                            id=f"{config.subsection_id_prefix}-{len(processed_sections)+1:03d}",
                            subTitle=behavior_type,
                            descriptions=[
                                Description(
                                    subTitle="Default Analysis",
                                    description=item
                                ) for item in config.default_data
                            ]
                        )
                
                    processed_sections.append(subsection)
                    
                except Exception as e:
                    logger.error(f"Error processing {psychographic_type}: {str(e)}")
                    raise
                    
        return {
            "id":"psychographic-001",
            "title":"Psychographic Segmentation",
            "type": "structured",
            "data":processed_sections
        }
        
    except Exception as e:
        logger.error(f"Error in Psychographic analysis: {str(e)}")
        raise

# Synchronous wrapper
def analyze_psychographic_sync(audience: str, client: OpenAI) -> List[Section]:
    """Synchronous wrapper for psychographic analysis"""
    return asyncio.run(analyze_psychographic(audience, client))


def get_reviewer_link(reviewer_name: str):
    """
    Get the link for a reviewer from the CSV file. If the reviewer has multiple entries,
    only the first one will be used.
    
    Args:
        reviewer_name (str): Name of the reviewer to search for
        
    Returns:
        Tuple[str, str]: A tuple containing (reviewer_name, link)
                        If reviewer not found, returns (reviewer_name, "")
    """
    # Get the directory containing the CSV files
    knowledge_base_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                    "database", "knowledge_base")
    
    # Initialize variables
    found_link = ""
    updated_reviewer = reviewer_name

    
    # Search through all CSV files in the knowledge base directory
    for filename in os.listdir(knowledge_base_dir):
        if filename.endswith(".csv"):
            file_path = os.path.join(knowledge_base_dir, filename)
            try:
                # Read the CSV file
                df = pd.read_csv(file_path)
                
                # Find all entries for this reviewer
                reviewer_entries = df[df["reviewer_name"] == reviewer_name]
                
                
                
                if not reviewer_entries.empty:
                    # Get the first entry's link
                    first_entry = reviewer_entries.iloc[0]
                    found_link = first_entry["product_review_page"]
                    break  # Stop searching once we find the reviewer
                    
            except Exception as e:
                logger.error(f"Error reading CSV file {filename}: {str(e)}")
                continue
    
    return updated_reviewer, found_link


class ProductExtractionResponse(BaseModel):
    product: str

async def extract_product_from_audience(audience: str, client: OpenAI) -> str:
    """
    Extract product information from the audience string using OpenAI.
    
    Parameters
    ----------
    audience : str
        The original audience description.
    client : OpenAI
        The OpenAI client instance.
        
    Returns
    -------
    str
        product : str
    """
    SYSTEM_PROMPT = """You are an assistant that identifies the product or service being described in the audience description.
    Your task is to extract only the product name or service being advertised from the description."""
    
    try:
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-2024-08-06",  
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": audience}  
            ],
            response_format=ProductExtractionResponse
        )
        # Return the extracted product name
        return completion.choices[0].message.parsed
    except Exception as e:
        logger.error(f"Error extracting product from audience: {str(e)}")
        return audience 


async def get_complete_audience_analysis(audience: str, configs: Dict[str, AnalysisConfig]) -> Dict:
    """
    Get complete audience analysis including all sections.
    
    Parameters
    ----------
    audience : str
        The target audience/product to analyze
    configs : Dict[str, AnalysisConfig]
        Configuration dictionary containing settings for different analysis types
        
    Returns
    -------
    Dict
        A dictionary containing the title and all analysis sections
    """
    logger.info("Starting complete audience analysis")
    
    
    # First extract product and clean audience string
    logger.info("Processing audience information...")
    processed_audience = await extract_product_from_audience(audience, client)
        
    if not processed_audience:
        raise ValueError("Failed to process audience information")
            
    logger.info(f"Successfully processed audience information: {processed_audience}")
    
    tasks = [
        analyze_audience(processed_audience, "social_media", configs),
        analyze_audience(processed_audience, "demographics", configs),
        analyze_audience(processed_audience, "interests", configs),
        analyze_behavior(processed_audience, client),
        analyze_psychographic(processed_audience, client),
        analyze_audience(processed_audience, "keywords", configs)
    ]

    try:
        # Execute all async tasks concurrently
        results = await asyncio.gather(*tasks)
        
        # Unpack results
        social_media_section = results[0]
        demographics_section = results[1]
        interests_section = results[2]
        behavior_sections = results[3]
        psychographic_sections = results[4]
        keywords_section = results[5]
        
        logger.info("Successfully completed all analysis tasks")

        # Build result
        result = {
            "title": "Market Insights, Audience analysis, and Target Results",
            "sections": [
                social_media_section,
                demographics_section,
                behavior_sections,
                interests_section,
                psychographic_sections,
                keywords_section
            ]
        }
        
        logger.info("Successfully completed complete audience analysis")
        return result
        
    except Exception as e:
        logger.error(f"Error in complete audience analysis: {str(e)}", exc_info=True)
        raise
