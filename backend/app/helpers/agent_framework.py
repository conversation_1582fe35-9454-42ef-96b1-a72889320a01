import re
import json
import logging
import sys
import asyncio
from typing import Dict, List, Any, TypedDict, Optional, Callable, Literal
from ..config import settings
try:
    from langchain_core.messages import BaseMessage #, HumanMessage, AIMessage, SystemMessage (Not directly used here, but good for future)
except ImportError:
    # Fallback if langchain_core is not available
    BaseMessage = Any

try:
    from langgraph.graph import StateGraph, START, END
except ImportError:
    try:
        from langgraph.graph import StateGraph
        # Fallback for older versions
        START = "__start__"
        END = "__end__"
    except ImportError:
        # If langgraph is not available at all, we need to handle this
        raise ImportError("LangGraph is required but not installed. Please install with: pip install langgraph")

import tiktoken # For token counting
from langchain_community.tools import DuckDuckGoSearchResults

from .. import config
from ..milvus_connector import CSVConnector, MilvusDBConnector, JSONConnector
from .llm_interface import call_llm, extract_json, format_output, get_embeddings
try:
    from prompts.prompt_templates import PromptManager
except ImportError:
    # Fallback if prompt_templates.py doesn't exist
    class PromptManager:
        def __init__(self):
            pass
        
        def get_extraction_prompt(self, question: str) -> str:
            return f"""Extract the target audience or product from this question. Return a JSON object with an "audience" field.

Question: {question}

Please identify the main subject or audience being discussed. Return your response as JSON:
{{"audience": "the identified audience or product"}}"""
        
        def get_classification_prompt(self, question: str) -> str:
            return f"""Classify this question into one of these analysis types: {', '.join(config.AGENT_TYPES.keys())}

Question: {question}

Return only the single word classification type, or "unclear" if the question is too general."""
        
        def get_prompt(self, agent_type: str, audience: str, context: str) -> str:
            return f"""You are an expert analyst specializing in {agent_type} analysis.

Analyze the {agent_type} of the "{audience}" audience based on the provided context data.

Context Data:
{context}

Please provide a comprehensive {agent_type} analysis including:
1. Key findings and insights
2. Detailed breakdown of relevant metrics
3. Actionable recommendations

Format your response with both structured JSON data and human-readable text."""

"""
Agent Framework Module

This module implements a comprehensive agent framework using LangGraph for orchestrating 
asynchronous analysis workflows. It provides:

1. Query understanding and audience/entity extraction
2. Data fetching from Milvus (vector DB) and local processed data (CSV/JSON)
3. DuckDuckGo web search integration for external data supplementation
4. Agent type determination for specialized analysis
5. Final analysis generation by specialized agents

The framework supports both synchronous and asynchronous execution:
- Use get_workflow() for backward compatibility (synchronous)
- Use get_async_workflow() for improved performance (asynchronous)

Key Features:
- Concurrent data fetching from multiple sources
- Token-based context management
- Intelligent routing based on query complexity
- Web search integration for real-time data supplementation
- Comprehensive error handling and logging
"""

# Logger already configured by config.py
logger = logging.getLogger(__name__)


# Define the structure for retrieved data context
class RetrievedContext(TypedDict):
    source: str # e.g., "milvus_semantic", "csv_keyword"
    score: Optional[float] # e.g., Milvus distance or keyword score
    document: str # The searchable_document text
    metadata: Dict[str, Any] # Includes source_file, original_row_id, key_fields (parsed)

# Update the state definition
class AnalysisState(TypedDict):
    """Type for the analysis state, including embeddings and structured data."""
    question: str
    question_embedding: Optional[List[float]]
    audience: Optional[str]
    retrieved_data: List[RetrievedContext] # This will now be token-limited
    vector_db: MilvusDBConnector
    csv_data: CSVConnector
    agent_type: Optional[str]
    analysis_results: Dict[str, Any]
    formatted_output: str
    error_message: Optional[str]
    web_search_results: Optional[List[str]] # Web search results from DuckDuckGo
    json_data: JSONConnector
    messages: List[BaseMessage]


prompt_manager = PromptManager()


try:
    tokenizer = tiktoken.get_encoding("cl100k_base")
except Exception as e:
    logger.error(f"Failed to initialize tiktoken tokenizer: {e}. Token counting will be approximate.", exc_info=True)
    tokenizer = None # Fallback if tiktoken fails

def count_tokens(text: str) -> int:
    """Counts tokens in a string using tiktoken, with a fallback."""
    if tokenizer:
        return len(tokenizer.encode(text))
    else:
        # Fallback: very rough estimate (avg 4 chars per token)
        return len(text) // 4

# --- Async versions of the main workflow functions ---

async def extract_query_info_async(state: AnalysisState) -> AnalysisState:
    """
    Async version of extract_query_info.
    Extracts the core question and audience/entity from the input using an LLM.
    Also generates the question embedding.
    """
    logger.info("Executing Node (Async): extract_query_info")
    question = state.get("question", "")
    new_state = state.copy()
    new_state["error_message"] = None

    try:
        # Use asyncio.to_thread for CPU-bound operations
        question_embedding = await asyncio.to_thread(get_embeddings, [question])
        question_embedding = question_embedding[0]
        new_state["question_embedding"] = question_embedding
        logger.info(f"Generated embedding for question: {question[:50]}...")
    except Exception as e:
        logger.error(f"Failed to generate embedding for question: {e}", exc_info=True)
        new_state["error_message"] = f"Failed to get embedding: {e}"
        new_state["question_embedding"] = None # Ensure it's None on failure

    # LLM call to extract audience
    extraction_prompt = prompt_manager.get_extraction_prompt(question)
    audience = None
    try:
        response_content = await asyncio.to_thread(call_llm, extraction_prompt)
        info = extract_json(response_content) # Assumes extract_json handles potential errors
        audience = info.get("audience") if isinstance(info, dict) else None
        logger.info(f"LLM extraction for audience result: {info}")
    except Exception as e:
        logger.warning(f"LLM call or JSON extraction failed for audience: {e}. Attempting fallback.", exc_info=True)
        # Fallback handled below

    if not audience:
        logger.info("LLM did not identify audience, attempting fallback regex extraction.")
        audience = extract_audience_fallback(question)
        if audience:
            logger.info(f"Fallback extraction identified audience: {audience}")
        else:
            logger.warning("Fallback extraction also failed to identify audience.")
            # This error message will be handled by the routing logic
            new_state["error_message"] = (new_state["error_message"] + " | " if new_state["error_message"] else "") + \
                                         "Could not identify a target audience or product in the question."

    new_state["audience"] = audience
    return new_state

async def fetch_relevant_data_async(state: AnalysisState) -> AnalysisState:
    """
    Async version of fetch_relevant_data.
    Fetches relevant data using semantic search (Milvus) and keyword search (CSV),
    then combines, deduplicates, and selects documents based on a token budget.
    """
    logger.info("Executing Node (Async): fetch_relevant_data")
    question = state.get("question", "")
    question_embedding = state.get("question_embedding")
    audience = state.get("audience", "")
    vector_db: MilvusDBConnector = state["vector_db"]
    csv_data: CSVConnector = state["csv_data"]
    json_data: JSONConnector = state["json_data"]
    new_state = state.copy()
    new_state["retrieved_data"] = []

    all_retrieved_contexts: List[RetrievedContext] = []

    # Create async tasks for concurrent data fetching
    tasks = []

    # 1. Semantic Search (Milvus) - async task
    if question_embedding:
        async def milvus_search():
            try:
                logger.info(f"Performing Milvus search for question: {question[:50]}...")
                if settings.dev_mode:
                    logger.info("Running in DEV_MODE - skipping Milvus operations")
                    return []
                # Run Milvus search in thread pool since it's likely synchronous
                milvus_results = await asyncio.to_thread(
                    vector_db.search,
                    collection_name=settings.milvus_collection_name,
                    vector=question_embedding,
                    top_k=30
                )
                logger.info(f"Milvus search returned {len(milvus_results)} results.")
                contexts = []
                for res in milvus_results:
                    metadata = {
                        "source_file": res.get("source_file", "N/A"),
                        "original_row_id": res.get("original_row_id", "N/A"),
                        "key_fields": res.get("key_fields_json", {})
                    }
                    context: RetrievedContext = {
                        "source": "milvus_semantic",
                        "score": res.get("distance"),
                        "document": res.get("searchable_document", ""),
                        "metadata": metadata
                    }
                    contexts.append(context)
                return contexts
            except Exception as e:
                logger.error(f"Error during Milvus search: {e}", exc_info=True)
                new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"Milvus search failed: {e}"
                return []
        
        tasks.append(milvus_search())

    # 2. Keyword Search (CSVConnector) - async task
    keyword_query = audience if audience else question
    if keyword_query:
        async def csv_search():
            try:
                logger.info(f"Performing CSV keyword search for: {keyword_query[:50]}...")
                csv_keyword_results = await asyncio.to_thread(
                    csv_data.search_processed_data,
                    query_text=keyword_query,
                    top_k=30
                )
                logger.info(f"CSV keyword search returned {len(csv_keyword_results)} results.")
                contexts = []
                for res in csv_keyword_results:
                    metadata = {
                        "source_file": res.get("source_file", "N/A"),
                        "original_row_id": res.get("original_row_id", "N/A"),
                        "key_fields": res.get("key_original_fields", {})
                    }
                    context: RetrievedContext = {
                        "source": "csv_keyword",
                        "score": None,
                        "document": res.get("searchable_document", ""),
                        "metadata": metadata
                    }
                    contexts.append(context)
                return contexts
            except Exception as e:
                logger.error(f"Error during CSV keyword search: {e}", exc_info=True)
                new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"CSV keyword search failed: {e}"
                return []
        
        tasks.append(csv_search())

    # 3. JSON Keyword Search - async task
    async def json_search():
        try:
            logger.info(f"Performing JSON keyword search for: {keyword_query[:50]}...")
            json_results = await asyncio.to_thread(
                json_data.search_processed_data,
                query_text=keyword_query,
                top_k=15
            )
            logger.info(f"JSON search returned {len(json_results)} results.")
            contexts = []
            for res in json_results:
                metadata = {
                    "source_file": res.get("source_file", "N/A"),
                    "original_data": res.get("original_data", {}),
                    "key_fields": res.get("key_fields_json", {})
                }
                context: RetrievedContext = {
                    "source": "json_keyword",
                    "score": res.get("score"),
                    "document": res.get("searchable_document", ""),
                    "metadata": metadata
                }
                contexts.append(context)
            return contexts
        except Exception as e:
            logger.error(f"Error during JSON search: {e}")
            new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"JSON search failed: {e}"
            return []
    
    if keyword_query:
        tasks.append(json_search())

    # Execute all search tasks concurrently
    if tasks:
        search_results = await asyncio.gather(*tasks, return_exceptions=True)
        for result in search_results:
            if isinstance(result, Exception):
                logger.error(f"Search task failed with exception: {result}")
            elif isinstance(result, list):
                all_retrieved_contexts.extend(result)

    # 4. Process results (same as synchronous version)
    unique_contexts_by_document: Dict[str, RetrievedContext] = {}
    all_retrieved_contexts.sort(key=lambda x: (x['source'] != 'milvus_semantic', x.get('score') if x.get('score') is not None else float('inf')))

    for context in all_retrieved_contexts:
        doc_text = context.get("document", "")
        if doc_text and doc_text not in unique_contexts_by_document:
            unique_contexts_by_document[doc_text] = context

    sorted_unique_contexts = list(unique_contexts_by_document.values())

    final_selected_contexts: List[RetrievedContext] = []
    current_total_tokens = 0
    token_budget = settings.max_retrieved_tokens

    logger.info(f"Applying token budget of {token_budget} to {len(sorted_unique_contexts)} unique retrieved documents.")

    for context_item in sorted_unique_contexts:
        doc_text = context_item.get("document", "")
        if not doc_text:
            continue

        item_tokens = count_tokens(doc_text)

        if not final_selected_contexts:
            final_selected_contexts.append(context_item)
            current_total_tokens += item_tokens
            if item_tokens > token_budget:
                logger.warning(f"The first document itself ({item_tokens} tokens) exceeds the token budget ({token_budget}). It has been included, but context may be too large.")
        elif current_total_tokens + item_tokens <= token_budget:
            final_selected_contexts.append(context_item)
            current_total_tokens += item_tokens
        else:
            logger.info(f"Token budget reached. Stopping at {len(final_selected_contexts)} documents with {current_total_tokens} tokens.")
            break

    new_state["retrieved_data"] = final_selected_contexts
    if not final_selected_contexts:
        logger.warning("No documents selected for context after applying token budget.")
    else:
        logger.info(f"Selected {len(final_selected_contexts)} documents for LLM context, totaling ~{current_total_tokens} tokens.")
    return new_state

async def determine_agent_type_async(state: AnalysisState) -> AnalysisState:
    """Async version of determine_agent_type. Determine the appropriate analysis agent type using LLM classification."""
    logger.info("Executing Node (Async): determine_agent_type")
    question = state.get("question", "")
    new_state = state.copy()

    classification_prompt = prompt_manager.get_classification_prompt(question)
    agent_type = None

    try:
        response_content = await asyncio.to_thread(call_llm, classification_prompt)
        response_content = response_content.strip().lower()
        logger.info(f"Agent classification LLM response: '{response_content}'")
        if response_content in config.AGENT_TYPES:
            agent_type = response_content
            logger.info(f"Determined agent type: {response_content}")
        elif response_content == "unclear":
            logger.info("LLM classified question as 'unclear' - question is too general for specific analysis.")
            new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + \
                                         "Question is too general. Please specify what type of analysis you need."
        else:
            logger.warning(f"LLM classification response '{response_content}' not in valid AGENT_TYPES and not 'unclear'. Question appears to be too general.")
            new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + \
                                         "Question is too general. Please specify what type of analysis you need."
    except Exception as e:
        logger.error(f"LLM call failed for agent type determination: {e}.", exc_info=True)
        new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + \
                                     f"Agent type determination failed: {e}"

    new_state["agent_type"] = agent_type
    return new_state

async def perform_web_search_async(state: AnalysisState) -> AnalysisState:
    """
    Async version of perform_web_search.
    Performs a web search using DuckDuckGo to supplement internal data.
    """
    logger.info("Executing Node (Async): perform_web_search")
    question = state.get("question", "")
    audience = state.get("audience", "")
    new_state = state.copy()
    
    # Check if web search is enabled in config
    if not settings.ddg_search_enabled:
        logger.info("Web search is disabled in configuration. Skipping.")
        new_state["web_search_results"] = []
        return new_state
    
    # Skip if no audience was identified
    if not audience:
        logger.warning("No audience identified, skipping web search.")
        new_state["web_search_results"] = []
        return new_state
        
    # Create search queries based on audience and analysis type
    base_query = f"{audience} audience demographics interests"
    agent_type = state.get("agent_type")
    if agent_type:
        # Modify query based on analysis type
        if agent_type == "demographics":
            search_query = f"{audience} user demographics age gender location income education"
        elif agent_type == "interests":
            search_query = f"{audience} user interests preferences hobbies activities"
        elif agent_type == "keywords":
            search_query = f"{audience} common phrases features user reviews sentiments"
        elif agent_type == "usage":
            search_query = f"{audience} usage patterns frequency utilization behavior"
        elif agent_type == "satisfaction":
            search_query = f"{audience} customer satisfaction reviews ratings feedback"
        elif agent_type == "purchase":
            search_query = f"{audience} purchase behavior buying habits frequency motivation"
        elif agent_type == "personality":
            search_query = f"{audience} personality traits user psychology"
        elif agent_type == "lifestyle":
            search_query = f"{audience} lifestyle daily routines product integration"
        elif agent_type == "values":
            search_query = f"{audience} values sustainability ethics priorities aspirations"
        else:
            search_query = base_query
    else:
        search_query = base_query
    
    # Add search instructions from config if available
    if hasattr(settings, 'ddg_search_instructions') and settings.ddg_search_instructions:
        search_query = f"{search_query} {settings.ddg_search_instructions}"
    
    try:
        logger.info(f"Performing DuckDuckGo search with query: '{search_query}'")
        # Configure the search tool with settings from config
        search_tool = DuckDuckGoSearchResults(
            num_results=settings.ddg_num_results if hasattr(config, 'DDG_NUM_RESULTS') else 3,
        )
        
        # Execute the search asynchronously
        search_results = await asyncio.to_thread(search_tool.invoke, {"query": search_query})
        
        # Process and format search results for LLM context
        web_results = []
        results_list = []
        if isinstance(search_results, list):
            results_list = search_results
        elif isinstance(search_results, dict):
            results_list = search_results.get("results", search_results.get("content", []))
        
        for result in results_list:
            if isinstance(result, dict):
                snippet = result.get("snippet", result.get("content", ""))
                title = result.get("title", "")
                link = result.get("link", result.get("url", ""))
                if snippet:
                    formatted_result = f"Title: {title}\nURL: {link}\nContent: {snippet}\n---"
                    web_results.append(formatted_result)
            elif isinstance(result, str):
                web_results.append(f"Content: {result}\n---")
        
        # Add web search results to state
        new_state["web_search_results"] = web_results
        logger.info(f"Retrieved {len(web_results)} web search results.")
    except Exception as e:
        logger.error(f"Web search error: {e}", exc_info=True)
        new_state["web_search_results"] = []
        new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"Web search failed: {e}"
    
    return new_state

async def analyze_audience_async(state: AnalysisState) -> AnalysisState:
    """Async version of analyze_audience. Perform analysis based on the determined agent type and retrieved data."""
    logger.info("Executing Node (Async): analyze_audience")
    audience = state.get("audience", "")
    agent_type = state.get("agent_type")
    retrieved_data: List[RetrievedContext] = state.get("retrieved_data", [])
    web_search_results = state.get("web_search_results", [])
    new_state = state.copy()
    
    # Ensure we have a valid agent_type
    if not agent_type:
        logger.error("analyze_audience called without a valid agent_type.")
        new_state["error_message"] = "Internal error: No analysis type specified"
        new_state["formatted_output"] = "An internal error occurred. Please try again."
        new_state["analysis_results"] = {"error": "No analysis type specified", "status_message": "error"}
        return new_state

    context_parts = []
    
    # First add internal data context
    if retrieved_data:
        for i, item in enumerate(retrieved_data):
            context_str = f"Context Document {i+1} (Source: {item['source']}):\n"
            context_str += f"Text: {item.get('document', '')}\n"
            metadata = item.get('metadata', {})
            key_fields = metadata.get('key_fields', {})
            if key_fields and isinstance(key_fields, dict):
                 metadata_str = json.dumps({k: v for k, v in key_fields.items() if v is not None and str(v).strip() != ""})
                 if metadata_str != '{}':
                    context_str += f"Associated Data: {metadata_str}\n"
            context_str += "---\n"
            context_parts.append(context_str)
    
    # Add web search results if available
    if web_search_results:
        context_parts.append("\nWEB SEARCH RESULTS:\n")
        for i, result in enumerate(web_search_results):
            context_parts.append(f"Web Result {i+1}:\n{result}\n")
    
    if context_parts:
        context_for_llm = "\n".join(context_parts)
    else:
        logger.warning("No context data available for analysis after filtering.")
        context_for_llm = "No specific data context was retrieved for this audience. Please provide analysis based on general knowledge and the specific request."

    # Get prompt for the agent type
    analysis_prompt_str = prompt_manager.get_prompt(agent_type, audience, context_for_llm)

    structured_data = {}
    formatted_output = "Analysis could not be completed due to an issue."
    raw_response_content = ""

    try:
        logger.info(f"Calling LLM for agent type '{agent_type}' on audience '{audience}'. Context length (chars): {len(context_for_llm)}")
        raw_response_content = await asyncio.to_thread(call_llm, analysis_prompt_str)

        try:
            structured_data = extract_json(raw_response_content)
            if not isinstance(structured_data, dict) or not structured_data:
                 logger.warning(f"extract_json did not return a non-empty dictionary. Raw response: {raw_response_content[:200]}...")
                 if not structured_data:
                    structured_data = {"error": "LLM did not return structured data in the expected JSON format."}
        except json.JSONDecodeError as json_e:
            logger.error(f"Failed to decode JSON from LLM response: {json_e}. Raw response: {raw_response_content[:200]}...", exc_info=True)
            structured_data = {"error": f"JSON Decode Error from LLM response: {json_e}"}
        except Exception as parse_e:
             logger.error(f"Error extracting JSON: {parse_e}", exc_info=True)
             structured_data = {"error": f"JSON Extraction Error: {parse_e}"}

        formatted_output = format_output(raw_response_content)
        if not formatted_output and structured_data and "error" not in structured_data:
            logger.info("LLM response was primarily JSON. No separate formatted text extracted.")

    except Exception as e:
        logger.error(f"LLM call failed during analysis for agent '{agent_type}': {e}", exc_info=True)
        new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"Analysis LLM call failed: {e}"
        structured_data = {"error": f"LLM call failed during analysis: {e}"}

    analysis_results_dict = {
        "agent_type": agent_type,
        "audience": audience,
        "structured_data": structured_data,
        "formatted_output": formatted_output,
        "raw_response": raw_response_content,
        "used_web_search": len(web_search_results) > 0
    }

    new_state["analysis_results"] = analysis_results_dict
    new_state["formatted_output"] = formatted_output
    return new_state

# --- Conditional Edges ---

def route_query_extraction(state: AnalysisState) -> Literal["fetch_data", "__end__"]:
    """Determine next step based on whether audience was extracted and no fatal errors occurred."""
    logger.info("Executing Edge Logic: route_query_extraction")
    if state.get("error_message") and "Could not identify a target audience" in state["error_message"]:
        logger.error(f"Routing to END due to audience extraction failure: {state['error_message']}")
        # Prepare a specific message for this case
        state["formatted_output"] = state.get("error_message", "Could not determine the audience or product from your question. Please rephrase.")
        state["analysis_results"] = {"error": state["formatted_output"], "status_message": "clarification_needed"}
        return END
    if state.get("error_message"): # Other errors during extraction (e.g., embedding failure)
        logger.error(f"Routing to END due to error in query extraction phase: {state['error_message']}")
        state["formatted_output"] = state.get("error_message", "An error occurred during query processing.")
        state["analysis_results"] = {"error": state["formatted_output"], "status_message": "error"}
        return END
    if state.get("audience"):
        logger.info("Audience found, routing to fetch_data.")
        return "fetch_data" # Name of the next node
    else: # Should be caught by error_message check above, but as a safeguard
        logger.warning("No audience extracted and no specific error message, routing to END.")
        state["formatted_output"] = "Could not determine the audience or product. Please rephrase."
        state["analysis_results"] = {"error": state["formatted_output"], "status_message": "clarification_needed"}
        return END

def route_analysis_result(state: AnalysisState) -> Literal["__end__"]:
     """Always routes to end after analysis, checking for errors during analysis."""
     logger.info("Executing Edge Logic: route_analysis_result")
     # Errors from previous steps should have already routed to END.
     # This checks for errors specifically from the analyze_audience node or upstream if not caught.
     if state.get("error_message") and "Analysis LLM call failed" in state.get("error_message", ""):
         logger.error(f"Analysis completed with error: {state['error_message']}")
         # Ensure formatted_output reflects the error if not already set by analyze_audience
         if not state.get("formatted_output") or state.get("formatted_output") == "Analysis could not be completed.":
            state["formatted_output"] = f"An error occurred during analysis: {state['error_message']}"
         if "error" not in state.get("analysis_results", {}): # Ensure error is in structured results
            state["analysis_results"] = state.get("analysis_results", {})
            state["analysis_results"]["error"] = state["error_message"]
            state["analysis_results"]["status_message"] = "error"

     elif not state.get("analysis_results") or not state.get("analysis_results").get("structured_data"):
        logger.error("Analysis node ran but produced no structured data and no explicit error message.")
        state["formatted_output"] = "Analysis did not produce a result."
        state["analysis_results"] = {"error": "Analysis failed to produce structured data.", "status_message": "error"}
     else:
         logger.info("Analysis node completed.")
     return END

def route_agent_determination(state: AnalysisState) -> Literal["web_search", "__end__"]:
    """Route based on whether a valid agent type was determined."""
    logger.info("Executing Edge Logic: route_agent_determination")
    
    agent_type = state.get("agent_type")
    
    if not agent_type:
        logger.warning("No valid agent type determined. Routing to END with clarification request.")
        
        # Create a helpful message that guides the user to ask more specific questions
        available_types = list(config.AGENT_TYPES.keys())
        clarification_message = (
            "I need more specific information to provide a targeted analysis. "
            "Please specify what type of analysis you're looking for:\n\n"
            "• **Demographics** - Age, gender, location, income, education of users\n"
            "• **Interests** - Hobbies, preferences, activities users engage in\n"
            "• **Keywords** - Key phrases, features, and sentiments mentioned\n"
            "• **Usage** - How and when users interact with the product\n"
            "• **Satisfaction** - User opinions, ratings, and feedback\n"
            "• **Purchase** - Buying patterns, timing, and motivations\n"
            "• **Personality** - Psychological traits and characteristics\n"
            "• **Lifestyle** - Daily routines and product integration\n"
            "• **Values** - Core beliefs, priorities, and aspirations\n\n"
            "For example:\n"
            "- 'What are the demographics of iPhone users?'\n"
            "- 'What interests do Tesla owners have?'\n"
            "- 'What keywords do people use when reviewing gaming laptops?'"
        )
        
        state["formatted_output"] = clarification_message
        state["analysis_results"] = {
            "error": "Question too general - analysis type clarification needed",
            "status_message": "clarification_needed",
            "available_analysis_types": available_types,
            "clarification_guidance": clarification_message
        }
        return END
    
    logger.info(f"Valid agent type '{agent_type}' determined. Proceeding to web search.")
    return "web_search"

# --- Workflow Construction ---

def get_workflow(vector_db: MilvusDBConnector, csv_data: CSVConnector, json_data: JSONConnector) -> Callable[[str], Dict[str, Any]]:
    """
    Creates and returns the synchronous analysis workflow graph.

    This function constructs a LangGraph StateGraph, defines the nodes (processing steps)
    and edges (transitions and conditional routing) for the analysis pipeline.
    This synchronous version is maintained for potential backward compatibility or specific use cases
    where synchronous execution is preferred.

    Args:
        vector_db (MilvusDBConnector): An initialized instance of the MilvusDBConnector.
        csv_data (CSVConnector): An initialized instance of the CSVConnector.
        json_data (JSONConnector): An initialized instance of the JSONConnector.

    Returns:
        Callable[[str], Dict[str, Any]]: A callable function that takes a question string
                                         as input and returns a dictionary containing the
                                         analysis results.
    """
    logger.info("Building LangGraph synchronous workflow...")
    workflow = StateGraph(AnalysisState)

    # Add nodes
    workflow.add_node("extract_query", extract_query_info)
    workflow.add_node("fetch_data", fetch_relevant_data)
    workflow.add_node("determine_agent", determine_agent_type)
    workflow.add_node("web_search", perform_web_search)  # Add web search node
    workflow.add_node("analyze_audience", analyze_audience)

    # Define entry point
    workflow.set_entry_point("extract_query") # Use set_entry_point

    # Define conditional routing after query extraction
    workflow.add_conditional_edges(
        "extract_query",
        route_query_extraction,
        {
            "fetch_data": "fetch_data",
            END: END
        }
    )

    # Define linear flow after data fetching
    workflow.add_edge("fetch_data", "determine_agent")
    
    # Add conditional routing after agent type determination
    workflow.add_conditional_edges(
        "determine_agent",
        route_agent_determination,
        {
            "web_search": "web_search",
            END: END
        }
    )
    
    workflow.add_edge("web_search", "analyze_audience")  # From web_search to analyze_audience
    workflow.add_edge("analyze_audience", END) # Directly end after analysis

    # Compile the workflow
    compiled_workflow = workflow.compile()
    logger.info("Workflow compiled successfully.")

    # --- Workflow Execution Function ---
    def process_question(question_text: str) -> Dict[str, Any]:
        """Processes a question using the compiled workflow."""
        logger.info(f"Processing question (workflow input): {question_text[:100]}...")
        initial_state: AnalysisState = { # Explicitly type for clarity
            "question": question_text,
            "question_embedding": None,
            "audience": None,
            "retrieved_data": [],
            "vector_db": vector_db,
            "csv_data": csv_data,
            "agent_type": None,
            "analysis_results": {},
            "formatted_output": "",
            "error_message": None,
            "web_search_results": [],
            "json_data": json_data, # Use the provided JSONConnector instance
        }

        final_state: Optional[AnalysisState] = None # Initialize to None
        try:
            final_state = compiled_workflow.invoke(initial_state, {"recursion_limit": 15}) # Add recursion limit
            logger.info("Workflow execution completed.")

            # Default status assuming success if no specific error conditions met below
            status = "success"
            message = final_state.get("formatted_output", "Analysis complete.") # Default success message

            # Check for errors or specific conditions from the final_state
            if final_state.get("error_message"):
                logger.error(f"Workflow ended with error in state: {final_state['error_message']}")
                status = "error"
                message = final_state['error_message']
                # If audience extraction failed specifically
                if "Could not identify a target audience" in final_state['error_message']:
                    status = "clarification_needed"
                    message = "Could not determine the audience or product from your question. Please rephrase."
                # If question is too general for agent type determination
                elif "Question is too general" in final_state['error_message']:
                    status = "clarification_needed"
                    message = final_state.get("formatted_output", "Please specify what type of analysis you need.")

            elif final_state.get("analysis_results", {}).get("status_message") == "clarification_needed":
                status = "clarification_needed"
                message = final_state.get("formatted_output", "Clarification needed for the query.")
            elif final_state.get("analysis_results", {}).get("status_message") == "error":
                status = "error"
                message = final_state.get("formatted_output", "An error occurred during analysis.")
            elif not final_state.get("audience") and status == "success": # Should ideally be caught by clarification_needed
                logger.warning("Workflow ended successfully but no audience was determined. This might indicate a logic gap.")
                status = "clarification_needed"
                message = "Audience could not be determined, though no explicit error was raised."
            elif status == "success" and (not final_state.get("analysis_results") or not final_state.get("analysis_results", {}).get("structured_data")):
                 logger.warning("Workflow successful but no structured data in analysis_results.")
                 # Potentially set status to error if structured_data is mandatory for success
                 # status = "error"
                 # message = "Analysis completed but failed to generate structured data."

            analysis_res = final_state.get("analysis_results", {})
            return {
                "status": status,
                "message": message, # This is the human-readable message/error
                "audience": final_state.get("audience", ""),
                "agent_type": final_state.get("agent_type", ""),
                "formatted_output": final_state.get("formatted_output", ""), # Human-readable analysis
                "structured_data": analysis_res.get("structured_data", {}),
                "raw_response": analysis_res.get("raw_response", ""),
                "web_search_used": analysis_res.get("used_web_search", False)
            }

        except Exception as e:
            logger.critical(f"Unhandled exception during workflow invocation: {e}", exc_info=True)
            # Try to get some info from initial_state or final_state if partially populated
            audience_info = initial_state.get("audience") if not final_state else final_state.get("audience")
            agent_type_info = initial_state.get("agent_type") if not final_state else final_state.get("agent_type")
            return {
                "status": "error",
                "message": f"An unexpected critical error occurred in the workflow: {e}",
                "audience": audience_info,
                "agent_type": agent_type_info,
                "formatted_output": "",
                "structured_data": {},
                "web_search_used": False
            }
    return process_question

# Example of how to get and use the workflow (usually called from main.py or fast_api.py)
# This block is for testing purposes only and can be removed or commented out for production.
if __name__ == '__main__':
    print("Setting up dummy connectors for agent_framework.py testing...")
    try:
        # Ensure config points to valid paths/settings for CSVConnector to load something
        # or mock its get_processed_data if no actual CSVs are present for testing.
        mock_vector_db = MilvusDBConnector() # Assumes Milvus is running or connection will be handled
        mock_csv_data = CSVConnector() # Assumes config.CSV_PROCESSING_CONFIG_PATH and CSV_DATA_DIRECTORY are set

        if not mock_csv_data.get_processed_data():
             print("WARNING: CSVConnector processed no data. Ensure CSVs and config are correct for testing.")
             # Optionally, create dummy processed data for testing flow if needed:
             # mock_csv_data.all_processed_data = [
             #    {"searchable_document": "Test CSV Data about product X", "source_file": "test.csv", "original_row_id": "1", "key_original_fields": {"name": "Product X"}},
             #    {"searchable_document": "Another review of product X, it is good.", "source_file": "test.csv", "original_row_id": "2", "key_original_fields": {"name": "Product X"}},
             # ]

        analyzer_workflow = get_workflow(mock_vector_db, mock_csv_data)

        test_questions = [
            "Tell me about the demographics of people who buy electric cars.",
            "What are the interests of smartphone users?",
            "Analyze this thing.", # Should fail audience extraction
            "Keywords for coffee drinkers",
            # Add more test cases as needed
        ]

        for i, question in enumerate(test_questions):
            print(f"\n--- Testing Question {i+1}: '{question}' ---")
            result = analyzer_workflow(question)
            print(f"Result {i+1}:")
            print(json.dumps(result, indent=2))
            print("--- End Test ---")

    except Exception as main_e:
        print(f"\nError during agent_framework.py testing setup or execution: {main_e}", file=sys.stderr)
        import traceback
        traceback.print_exc()

    finally:
        if 'mock_vector_db' in locals() and mock_vector_db:
            mock_vector_db.close_connection()
        print("\nAgent_framework.py testing finished.")

def perform_web_search(state: AnalysisState) -> AnalysisState:
    """
    Performs a web search using DuckDuckGo to supplement internal data.
    """
    logger.info("Executing Node: perform_web_search")
    question = state.get("question", "")
    audience = state.get("audience", "")
    new_state = state.copy()
    
    # Check if web search is enabled in config
    if not settings.ddg_search_enabled:
        logger.info("Web search is disabled in configuration. Skipping.")
        new_state["web_search_results"] = []
        return new_state
    
    # Skip if no audience was identified
    if not audience:
        logger.warning("No audience identified, skipping web search.")
        new_state["web_search_results"] = []
        return new_state
        
    # Create search queries based on audience and analysis type
    base_query = f"{audience} audience demographics interests"
    agent_type = state.get("agent_type")
    if agent_type:
        # Modify query based on analysis type
        if agent_type == "demographics":
            search_query = f"{audience} user demographics age gender location income education"
        elif agent_type == "interests":
            search_query = f"{audience} user interests preferences hobbies activities"
        elif agent_type == "keywords":
            search_query = f"{audience} common phrases features user reviews sentiments"
        elif agent_type == "usage":
            search_query = f"{audience} usage patterns frequency utilization behavior"
        elif agent_type == "satisfaction":
            search_query = f"{audience} customer satisfaction reviews ratings feedback"
        elif agent_type == "purchase":
            search_query = f"{audience} purchase behavior buying habits frequency motivation"
        elif agent_type == "personality":
            search_query = f"{audience} personality traits user psychology"
        elif agent_type == "lifestyle":
            search_query = f"{audience} lifestyle daily routines product integration"
        elif agent_type == "values":
            search_query = f"{audience} values sustainability ethics priorities aspirations"
        else:
            search_query = base_query
    else:
        search_query = base_query
    
    # Add search instructions from config if available
    if hasattr(settings, 'ddg_search_instructions') and settings.ddg_search_instructions:
        search_query = f"{search_query} {settings.ddg_search_instructions}"
    
    try:
        logger.info(f"Performing DuckDuckGo search with query: '{search_query}'")
        # Configure the search tool with settings from config
        search_tool = DuckDuckGoSearchResults(
            num_results=settings.ddg_num_results if hasattr(config, 'DDG_NUM_RESULTS') else 3,
        )
        
        # Execute the search
        search_results = search_tool.invoke({"query": search_query})
        
        # Process and format search results for LLM context
        web_results = []
        results_list = []
        if isinstance(search_results, list):
            results_list = search_results
        elif isinstance(search_results, dict):
            results_list = search_results.get("results", search_results.get("content", []))
        
        for result in results_list:
            if isinstance(result, dict):
                snippet = result.get("snippet", result.get("content", ""))
                title = result.get("title", "")
                link = result.get("link", result.get("url", ""))
                if snippet:
                    formatted_result = f"Title: {title}\nURL: {link}\nContent: {snippet}\n---"
                    web_results.append(formatted_result)
            elif isinstance(result, str):
                web_results.append(f"Content: {result}\n---")
        
        # Add web search results to state
        new_state["web_search_results"] = web_results
        logger.info(f"Retrieved {len(web_results)} web search results.")
    except Exception as e:
        logger.error(f"Web search error: {e}", exc_info=True)
        new_state["web_search_results"] = []
        new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"Web search failed: {e}"
    
    return new_state

def extract_audience_fallback(text: str) -> Optional[str]:
    """Extract audience/product using improved pattern matching as a fallback."""
    patterns = [
        r"\b(?:about|for|of|regarding)\s+(?:the\s+)?([\w\s\-]+?)(?:\?|\.|$|,| who| that)",
        r"([\w\s\-]+?)\s+(?:users|customers|buyers|audience|market)\b",
        r"\b(?:analyze|understand|target)\s+(?:the\s+)?([\w\s\-]+?)(?:\?|\.|$|,)",
        r"\b(?:people|users|consumers)\s+(?:who|that)\s+(?:use|buy|like|are interested in)\s+([\w\s\-]+)"
    ]
    text_lower = text.lower()
    for pattern in patterns:
        matches = re.findall(pattern, text_lower)
        if matches:
            potential_audience = matches[0].strip()
            if potential_audience and len(potential_audience) > 1 and \
               potential_audience not in ["me", "you", "them", "is", "are", "was", "were"]:
                return potential_audience
    return None

def extract_query_info(state: AnalysisState) -> AnalysisState:
    """
    Extracts the core question and audience/entity from the input using an LLM.
    Also generates the question embedding.
    """
    logger.info("Executing Node: extract_query_info")
    question = state.get("question", "")
    new_state = state.copy()
    new_state["error_message"] = None

    try:
        question_embedding = get_embeddings([question])[0]
        new_state["question_embedding"] = question_embedding
        logger.info(f"Generated embedding for question: {question[:50]}...")
    except Exception as e:
        logger.error(f"Failed to generate embedding for question: {e}", exc_info=True)
        new_state["error_message"] = f"Failed to get embedding: {e}"
        new_state["question_embedding"] = None # Ensure it's None on failure

    # LLM call to extract audience
    extraction_prompt = prompt_manager.get_extraction_prompt(question)
    audience = None
    try:
        response_content = call_llm(extraction_prompt)
        info = extract_json(response_content) # Assumes extract_json handles potential errors
        audience = info.get("audience") if isinstance(info, dict) else None
        logger.info(f"LLM extraction for audience result: {info}")
    except Exception as e:
        logger.warning(f"LLM call or JSON extraction failed for audience: {e}. Attempting fallback.", exc_info=True)
        # Fallback handled below

    if not audience:
        logger.info("LLM did not identify audience, attempting fallback regex extraction.")
        audience = extract_audience_fallback(question)
        if audience:
            logger.info(f"Fallback extraction identified audience: {audience}")
        else:
            logger.warning("Fallback extraction also failed to identify audience.")
            # This error message will be handled by the routing logic
            new_state["error_message"] = (new_state["error_message"] + " | " if new_state["error_message"] else "") + \
                                         "Could not identify a target audience or product in the question."

    new_state["audience"] = audience
    return new_state

# --- Add async workflow function ---

def get_async_workflow(vector_db: MilvusDBConnector, csv_data: CSVConnector, json_data: JSONConnector) -> Callable[[str], Any]:
    """
    Creates and returns the asynchronous analysis workflow graph.

    This function constructs a LangGraph StateGraph using asynchronous node functions,
    allowing for concurrent execution of I/O-bound tasks like data fetching and LLM calls.
    This is the preferred workflow for performance.

    Args:
        vector_db (MilvusDBConnector): An initialized instance of the MilvusDBConnector.
        csv_data (CSVConnector): An initialized instance of the CSVConnector.
        json_data (JSONConnector): An initialized instance of the JSONConnector.

    Returns:
        Callable[[str], Any]: An asynchronous callable function that takes a question string
                               as input and returns a dictionary containing the
                               analysis results. This callable should be awaited.
    """
    logger.info("Building async LangGraph workflow...")
    workflow = StateGraph(AnalysisState)

    # Add async nodes
    workflow.add_node("extract_query", extract_query_info_async)
    workflow.add_node("fetch_data", fetch_relevant_data_async)
    workflow.add_node("determine_agent", determine_agent_type_async)
    workflow.add_node("web_search", perform_web_search_async)
    workflow.add_node("analyze_audience", analyze_audience_async)

    # Define entry point
    workflow.set_entry_point("extract_query")

    # Define conditional routing after query extraction
    workflow.add_conditional_edges(
        "extract_query",
        route_query_extraction,
        {
            "fetch_data": "fetch_data",
            END: END
        }
    )

    # Define linear flow after data fetching
    workflow.add_edge("fetch_data", "determine_agent")
    
    # Add conditional routing after agent type determination
    workflow.add_conditional_edges(
        "determine_agent",
        route_agent_determination,
        {
            "web_search": "web_search",
            END: END
        }
    )
    
    workflow.add_edge("web_search", "analyze_audience")
    workflow.add_edge("analyze_audience", END)

    # Compile the workflow
    compiled_workflow = workflow.compile()
    logger.info("Async workflow compiled successfully.")

    # --- Async Workflow Execution Function ---
    async def process_question_async(question_text: str) -> Dict[str, Any]:
        """Processes a question using the compiled async workflow."""
        logger.info(f"Processing question (async workflow input): {question_text[:100]}...")
        initial_state: AnalysisState = {
            "question": question_text,
            "question_embedding": None,
            "audience": None,
            "retrieved_data": [],
            "vector_db": vector_db,
            "csv_data": csv_data,
            "agent_type": None,
            "analysis_results": {},
            "formatted_output": "",
            "error_message": None,
            "web_search_results": [],
            "json_data": json_data, # Use the provided JSONConnector instance
            "messages": [],

        }

        final_state: Optional[AnalysisState] = None
        try:
            # Use astream for async execution
            final_state = await compiled_workflow.ainvoke(initial_state, {"recursion_limit": 15})
            logger.info("Async workflow execution completed.")

            # Default status assuming success if no specific error conditions met below
            status = "success"
            message = final_state.get("formatted_output", "Analysis complete.")

            # Check for errors or specific conditions from the final_state
            if final_state.get("error_message"):
                logger.error(f"Async workflow ended with error in state: {final_state['error_message']}")
                status = "error"
                message = final_state['error_message']
                # If audience extraction failed specifically
                if "Could not identify a target audience" in final_state['error_message']:
                    status = "clarification_needed"
                    message = "Could not determine the audience or product from your question. Please rephrase."
                # If question is too general for agent type determination
                elif "Question is too general" in final_state['error_message']:
                    status = "clarification_needed"
                    message = final_state.get("formatted_output", "Please specify what type of analysis you need.")

            elif final_state.get("analysis_results", {}).get("status_message") == "clarification_needed":
                status = "clarification_needed"
                message = final_state.get("formatted_output", "Clarification needed for the query.")
            elif final_state.get("analysis_results", {}).get("status_message") == "error":
                status = "error"
                message = final_state.get("formatted_output", "An error occurred during analysis.")
            elif not final_state.get("audience") and status == "success":
                logger.warning("Async workflow ended successfully but no audience was determined.")
                status = "clarification_needed"
                message = "Audience could not be determined, though no explicit error was raised."

            analysis_res = final_state.get("analysis_results", {})
            return {
                "status": status,
                "message": message,
                "audience": final_state.get("audience", ""),
                "agent_type": final_state.get("agent_type", ""),
                "formatted_output": final_state.get("formatted_output", ""),
                "structured_data": analysis_res.get("structured_data", {}),
                "raw_response": analysis_res.get("raw_response", ""),
                "web_search_used": analysis_res.get("used_web_search", False),

            }

        except Exception as e:
            logger.critical(f"Unhandled exception during async workflow invocation: {e}", exc_info=True)
            # Try to get some info from initial_state or final_state if partially populated
            audience_info = initial_state.get("audience") if not final_state else final_state.get("audience")
            agent_type_info = initial_state.get("agent_type") if not final_state else final_state.get("agent_type")
            return {
                "status": "error",
                "message": f"An unexpected critical error occurred in the async workflow: {e}",
                "audience": audience_info,
                "agent_type": agent_type_info,
                "formatted_output": "",
                "structured_data": {},
                "web_search_used": False,

            }
    
    return process_question_async

# --- Synchronous functions (for backward compatibility) ---

def fetch_relevant_data(state: AnalysisState) -> AnalysisState:
    """
    Fetches relevant data using semantic search (Milvus) and keyword search (CSV),
    then combines, deduplicates, and selects documents based on a token budget.
    """
    logger.info("Executing Node: fetch_relevant_data")
    question = state.get("question", "")
    question_embedding = state.get("question_embedding")
    audience = state.get("audience", "")
    vector_db: MilvusDBConnector = state["vector_db"]
    csv_data: CSVConnector = state["csv_data"]
    json_data: JSONConnector = state["json_data"]
    new_state = state.copy()
    new_state["retrieved_data"] = []

    all_retrieved_contexts: List[RetrievedContext] = []

    # 1. Semantic Search (Milvus)
    if question_embedding:
        try:
            logger.info(f"Performing Milvus search for question: {question[:50]}...")
            if settings.dev_mode:
                logger.info("Running in DEV_MODE - skipping Milvus operations")
                return []
            milvus_results = vector_db.search(
                collection_name=settings.milvus_collection_name,
                vector=question_embedding,
                top_k=30
            )
            logger.info(f"Milvus search returned {len(milvus_results)} results.")
            for res in milvus_results:
                metadata = {
                    "source_file": res.get("source_file", "N/A"),
                    "original_row_id": res.get("original_row_id", "N/A"),
                    "key_fields": res.get("key_fields_json", {})
                }
                context: RetrievedContext = {
                    "source": "milvus_semantic",
                    "score": res.get("distance"),
                    "document": res.get("searchable_document", ""),
                    "metadata": metadata
                }
                all_retrieved_contexts.append(context)
        except Exception as e:
            logger.error(f"Error during Milvus search: {e}", exc_info=True)
            new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"Milvus search failed: {e}"

    # 2. Keyword Search (CSVConnector)
    keyword_query = audience if audience else question
    if keyword_query:
        try:
            logger.info(f"Performing CSV keyword search for: {keyword_query[:50]}...")
            csv_keyword_results = csv_data.search_processed_data(
                query_text=keyword_query,
                top_k=30
            )
            logger.info(f"CSV keyword search returned {len(csv_keyword_results)} results.")
            for res in csv_keyword_results:
                metadata = {
                    "source_file": res.get("source_file", "N/A"),
                    "original_row_id": res.get("original_row_id", "N/A"),
                    "key_fields": res.get("key_original_fields", {})
                }
                context: RetrievedContext = {
                    "source": "csv_keyword",
                    "score": None,
                    "document": res.get("searchable_document", ""),
                    "metadata": metadata
                }
                all_retrieved_contexts.append(context)
        except Exception as e:
            logger.error(f"Error during CSV keyword search: {e}", exc_info=True)
            new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"CSV keyword search failed: {e}"

    # 3. JSON Keyword Search
    try:
        logger.info(f"Performing JSON keyword search for: {keyword_query[:50]}...")
        json_results = json_data.search_processed_data(
            query_text=keyword_query,
            top_k=15
        )
        logger.info(f"JSON search returned {len(json_results)} results.")
        for res in json_results:
            metadata = {
                "source_file": res.get("source_file", "N/A"),
                "original_data": res.get("original_data", {}),
                "key_fields": res.get("key_fields_json", {})
            }
            context: RetrievedContext = {
                "source": "json_keyword",
                "score": res.get("score"),
                "document": res.get("searchable_document", ""),
                "metadata": metadata
            }
            all_retrieved_contexts.append(context)
    except Exception as e:
        logger.error(f"Error during JSON search: {e}")
        new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"JSON search failed: {e}"

    # 4. Combine, Deduplicate, Sort, and Select Final Context by Token Budget
    unique_contexts_by_document: Dict[str, RetrievedContext] = {}
    all_retrieved_contexts.sort(key=lambda x: (x['source'] != 'milvus_semantic', x.get('score') if x.get('score') is not None else float('inf')))

    for context in all_retrieved_contexts:
        doc_text = context.get("document", "")
        if doc_text and doc_text not in unique_contexts_by_document:
            unique_contexts_by_document[doc_text] = context

    sorted_unique_contexts = list(unique_contexts_by_document.values())

    final_selected_contexts: List[RetrievedContext] = []
    current_total_tokens = 0
    token_budget = settings.max_retrieved_tokens

    logger.info(f"Applying token budget of {token_budget} to {len(sorted_unique_contexts)} unique retrieved documents.")

    for context_item in sorted_unique_contexts:
        doc_text = context_item.get("document", "")
        if not doc_text:
            continue

        item_tokens = count_tokens(doc_text)

        if not final_selected_contexts:
            final_selected_contexts.append(context_item)
            current_total_tokens += item_tokens
            if item_tokens > token_budget:
                logger.warning(f"The first document itself ({item_tokens} tokens) exceeds the token budget ({token_budget}). It has been included, but context may be too large.")
        elif current_total_tokens + item_tokens <= token_budget:
            final_selected_contexts.append(context_item)
            current_total_tokens += item_tokens
        else:
            logger.info(f"Token budget reached. Stopping at {len(final_selected_contexts)} documents with {current_total_tokens} tokens.")
            break

    new_state["retrieved_data"] = final_selected_contexts
    if not final_selected_contexts:
        logger.warning("No documents selected for context after applying token budget.")
    else:
        logger.info(f"Selected {len(final_selected_contexts)} documents for LLM context, totaling ~{current_total_tokens} tokens.")
    return new_state

def determine_agent_type(state: AnalysisState) -> AnalysisState:
    """Determine the appropriate analysis agent type using LLM classification."""
    logger.info("Executing Node: determine_agent_type")
    question = state.get("question", "")
    new_state = state.copy()

    classification_prompt = prompt_manager.get_classification_prompt(question)
    agent_type = None

    try:
        response_content = call_llm(classification_prompt).strip().lower()
        logger.info(f"Agent classification LLM response: '{response_content}'")
        if response_content in config.AGENT_TYPES:
            agent_type = response_content
            logger.info(f"Determined agent type: {response_content}")
        elif response_content == "unclear":
            logger.info("LLM classified question as 'unclear' - question is too general for specific analysis.")
            new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + \
                                         "Question is too general. Please specify what type of analysis you need."
        else:
            logger.warning(f"LLM classification response '{response_content}' not in valid AGENT_TYPES and not 'unclear'. Question appears to be too general.")
            new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + \
                                         "Question is too general. Please specify what type of analysis you need."
    except Exception as e:
        logger.error(f"LLM call failed for agent type determination: {e}.", exc_info=True)
        new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + \
                                     f"Agent type determination failed: {e}"

    new_state["agent_type"] = agent_type
    return new_state

def analyze_audience(state: AnalysisState) -> AnalysisState:
    """Perform analysis based on the determined agent type and retrieved data."""
    logger.info("Executing Node: analyze_audience")
    audience = state.get("audience", "")
    agent_type = state.get("agent_type")
    retrieved_data: List[RetrievedContext] = state.get("retrieved_data", [])
    web_search_results = state.get("web_search_results", [])
    new_state = state.copy()
    
    # Ensure we have a valid agent_type
    if not agent_type:
        logger.error("analyze_audience called without a valid agent_type.")
        new_state["error_message"] = "Internal error: No analysis type specified"
        new_state["formatted_output"] = "An internal error occurred. Please try again."
        new_state["analysis_results"] = {"error": "No analysis type specified", "status_message": "error"}
        return new_state

    context_parts = []
    
    # First add internal data context
    if retrieved_data:
        for i, item in enumerate(retrieved_data):
            context_str = f"Context Document {i+1} (Source: {item['source']}):\n"
            context_str += f"Text: {item.get('document', '')}\n"
            metadata = item.get('metadata', {})
            key_fields = metadata.get('key_fields', {})
            if key_fields and isinstance(key_fields, dict):
                 metadata_str = json.dumps({k: v for k, v in key_fields.items() if v is not None and str(v).strip() != ""})
                 if metadata_str != '{}':
                    context_str += f"Associated Data: {metadata_str}\n"
            context_str += "---\n"
            context_parts.append(context_str)
    
    # Add web search results if available
    if web_search_results:
        context_parts.append("\nWEB SEARCH RESULTS:\n")
        for i, result in enumerate(web_search_results):
            context_parts.append(f"Web Result {i+1}:\n{result}\n")
    
    if context_parts:
        context_for_llm = "\n".join(context_parts)
    else:
        logger.warning("No context data available for analysis after filtering.")
        context_for_llm = "No specific data context was retrieved for this audience. Please provide analysis based on general knowledge and the specific request."

    # Get prompt for the agent type
    analysis_prompt_str = prompt_manager.get_prompt(agent_type, audience, context_for_llm)

    structured_data = {}
    formatted_output = "Analysis could not be completed due to an issue."
    raw_response_content = ""

    try:
        logger.info(f"Calling LLM for agent type '{agent_type}' on audience '{audience}'. Context length (chars): {len(context_for_llm)}")
        raw_response_content = call_llm(analysis_prompt_str)

        try:
            structured_data = extract_json(raw_response_content)
            if not isinstance(structured_data, dict) or not structured_data:
                 logger.warning(f"extract_json did not return a non-empty dictionary. Raw response: {raw_response_content[:200]}...")
                 if not structured_data:
                    structured_data = {"error": "LLM did not return structured data in the expected JSON format."}
        except json.JSONDecodeError as json_e:
            logger.error(f"Failed to decode JSON from LLM response: {json_e}. Raw response: {raw_response_content[:200]}...", exc_info=True)
            structured_data = {"error": f"JSON Decode Error from LLM response: {json_e}"}
        except Exception as parse_e:
             logger.error(f"Error extracting JSON: {parse_e}", exc_info=True)
             structured_data = {"error": f"JSON Extraction Error: {parse_e}"}

        formatted_output = format_output(raw_response_content)
        if not formatted_output and structured_data and "error" not in structured_data:
            logger.info("LLM response was primarily JSON. No separate formatted text extracted.")

    except Exception as e:
        logger.error(f"LLM call failed during analysis for agent '{agent_type}': {e}", exc_info=True)
        new_state["error_message"] = (new_state.get("error_message", "") + " | " if new_state.get("error_message") else "") + f"Analysis LLM call failed: {e}"
        structured_data = {"error": f"LLM call failed during analysis: {e}"}

    analysis_results_dict = {
        "agent_type": agent_type,
        "audience": audience,
        "structured_data": structured_data,
        "formatted_output": formatted_output,
        "raw_response": raw_response_content,
        "used_web_search": len(web_search_results) > 0
    }

    new_state["analysis_results"] = analysis_results_dict
    new_state["formatted_output"] = formatted_output
    return new_state
