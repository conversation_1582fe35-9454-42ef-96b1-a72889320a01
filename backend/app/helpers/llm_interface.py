# Adtargeting_AI-1/llm_interface.py
"""Interface for LLM API calls (Chat Completion, Embeddings) and response handling."""

import os
import json
import re
import logging
from typing import Dict, Any, List, Optional
import openai
from openai import OpenAI, RateLimitError, APIError, APITimeoutError # Import specific errors
import requests
from ..config import settings
try:
    import tqdm
except ImportError:
    # Fallback if tqdm is not available
    class MockTqdm:
        def __init__(self, total=None, desc=None, unit=None):
            self.total = total
            self.desc = desc
            self.unit = unit or ""
            self.n = 0
            
        def update(self, n=1):
            self.n += n
            if self.total:
                print(f"\r{self.desc}: {self.n}/{self.total} {self.unit}", end="", flush=True)
            
        def __enter__(self):
            return self
            
        def __exit__(self, *args):
            print()  # New line after progress
    
    tqdm.tqdm = MockTqdm

try:
    import tiktoken
    tokenizer = tiktoken.get_encoding("cl100k_base")
except ImportError:
    tokenizer = None

# Project imports
from .. import config # Ensure config is loaded

# Configure logging using settings from config.py
logging.basicConfig(level=settings.log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- OpenAI Client Initialization ---
OPENAI_KEY = settings.openai_api_key
# Initialize the client once, potentially reusing it if safe in your execution context.
# For FastAPI/multi-threaded apps, consider thread-local or request-scoped clients if needed.
try:
    llm_client = OpenAI(api_key=OPENAI_KEY, max_retries=settings.max_retries_llm)
    logger.info("OpenAI client initialized successfully.")
except Exception as e:
    logger.critical(f"Failed to initialize OpenAI client: {e}", exc_info=True)
    # Depending on the application structure, you might raise this error
    # or handle it gracefully later when calls are made.
    llm_client = None

# --- Core LLM Call Function ---


RESPONSE_PROMPT = '''
You are a helpful and conversational AI assistant specialized in audience analysis and data interpretation.
Follow these instructions carefully:
- Respond in a clear, friendly and human like tone.
- Format insights, metrics, and recommendations using bullet points or numbered lists. Avoid long unstructured paragraphs.
- Begin each section with a title (e.g., Key Insights 📌, Supporting Data 📊, Recommendations 🧠 (if any)) followed by bullet points.
- Provide insightful, complete, relevant answers with details.
- Use emojis where they enchance readability and tone (e.g., 📊, 🧠, 📈). 
- Never use unessecary special characters like *, # or symbols other than emojis.
- Organize your responses with clear sections: Key Insights, Supporting Data, and Recommendations (when appropriate).
- Avoid repetition or generic phrases.
- When the question is not relevant to audience analysis respond with either a "Please specify what type of analysis you need." or "Could not determine the audience or product from your question. Please rephrase.".

'''


def call_llm(prompt: str) -> str:
    """
    Calls the configured LLM chat completion endpoint with a prompt.
    Args:
        prompt: The fully formatted prompt string (including any context).
    Returns:
        The content of the LLM's response message, or an error string.
    Raises:
        ConnectionError: If the client wasn't initialized.
        RateLimitError, APITimeoutError, APIError: Can be raised by the OpenAI client.
    """
    if not llm_client:
        logger.error("OpenAI client is not initialized. Cannot call LLM.")
        raise ConnectionError("OpenAI client not initialized.")

    logger.debug(f"Calling LLM model {settings.default_llm_model} with prompt (first 100 chars): {prompt[:100]}...")
    
    try:
        response = llm_client.chat.completions.create(
            model=settings.default_llm_model,
            messages=[
                # You can customize the system message if needed
                {"role": "system", "content": RESPONSE_PROMPT },
                {"role": "user", "content": prompt}
            ],
            temperature=settings.llm_temperature,
            max_tokens=settings.llm_max_tokens
            # Add other parameters like top_p if needed
        )
        
        content = response.choices[0].message.content
        logger.debug(f"LLM call successful. Response (first 100 chars): {content[:100]}...")
        return content

    # Catch specific OpenAI errors for potentially different handling or logging
    except RateLimitError as e:
        logger.error(f"OpenAI API rate limit exceeded: {e}")
        raise # Re-raise for potential handling upstream (e.g., backoff)
    except APITimeoutError as e:
        logger.error(f"OpenAI API request timed out: {e}")
        raise
    except APIError as e: # Catch other API-related errors
        logger.error(f"OpenAI API returned an error: {e}", exc_info=True)
        raise
    except Exception as e: # Catch unexpected errors
        logger.error(f"An unexpected error occurred during LLM call: {e}", exc_info=True)
        # Return an error message string or raise a custom exception
        # For now, re-raising the original exception might be best for debugging
        raise

# --- Embedding Function ---

def count_tokens(text: str) -> int:
    if tokenizer:
        return len(tokenizer.encode(text))
    else:
        return max(1, len(text) // 4)

def get_ollama_embeddings(texts: List[str], model: str = "nomic-embed-text") -> List[List[float]]:
    """
    Calls the Ollama API to get embeddings for a list of texts, chunking requests so that no chunk exceeds 2048 tokens.
    Shows a tqdm loading bar for progress based on total tokens sent.
    """
    api_base = getattr(settings, "ollama_api_base", None)
    if not api_base:
        logger.error("OLLAMA_API_BASE is not set in config.")
        raise ValueError("OLLAMA_API_BASE is not set in config.")
    url = f"{api_base}/api/embeddings"
    logger.info(f"[Ollama] Using endpoint: {url}")
    embeddings = []
    # Token chunking
    max_tokens_per_request = 2048
    # Precompute token counts for all texts
    text_token_counts = [count_tokens(text) for text in texts]
    total_tokens = sum(text_token_counts)
    tokens_sent = 0
    idx = 0
    with tqdm.tqdm(total=total_tokens, desc="Embedding tokens", unit="tok") as pbar:
        while idx < len(texts):
            chunk = []
            chunk_token_count = 0
            start_idx = idx
            while idx < len(texts) and chunk_token_count + text_token_counts[idx] <= max_tokens_per_request:
                chunk.append(texts[idx])
                chunk_token_count += text_token_counts[idx]
                idx += 1
            if not chunk:
                # Single text exceeds max_tokens_per_request, force send it alone
                chunk = [texts[idx]]
                chunk_token_count = text_token_counts[idx]
                idx += 1
            logger.info(f"[Ollama] Requesting embedding for chunk {start_idx+1}-{idx} ({len(chunk)} texts, {chunk_token_count} tokens)")
            for i, text in enumerate(chunk):
                payload = {"model": model, "prompt": text}
                try:
                    resp = requests.post(url, json=payload, timeout=60)
                    print("resp:", resp)
                    logger.info(f"[Ollama] Response status code: {resp.status_code}")
                    resp.raise_for_status()
                    data = resp.json()
                    logger.info(f"[Ollama] Response JSON keys: {list(data.keys())}")
                    if "embedding" in data:
                        logger.info(f"[Ollama] Successfully received embedding for item {start_idx + i + 1} (dim: {len(data['embedding'])})")
                        embeddings.append(data["embedding"])
                    else:
                        logger.error(f"[Ollama] No 'embedding' in response for item {start_idx + i + 1}. Response: {data}")
                        raise ValueError(f"Ollama API did not return embedding for text: {text[:80]}...")
                except Exception as e:
                    logger.error(f"[Ollama] Embedding error for item {start_idx + i + 1}: {e}", exc_info=True)
                    raise
                pbar.update(text_token_counts[start_idx + i])
    logger.info(f"[Ollama] Finished embedding {len(texts)} items.")
    return embeddings

def get_embeddings(texts: List[str], model: str = "text-embedding-3-small", batch_size: int = 100) -> List[List[float]]:
    """
    Generates embeddings for a list of texts using the local Ollama API (nomic-embed-text model) only. Ignores OpenAI embedding logic.
    """
    logger.info(f"[FORCED OLLAMA] Using Ollama for embeddings at {getattr(settings, 'ollama_api_base', 'NOT SET')}")
    return get_ollama_embeddings(texts, model="nomic-embed-text")

# --- Utility Functions for Response Handling ---

def extract_json(response: str) -> Dict[str, Any]:
    """
    Extracts the first valid JSON object found within the LLM response string.
    Handles potential markdown code fences.
    """
    if not response:
        return {}
        
    # Try finding JSON within markdown code blocks ```json ... ```
    json_block_match = re.search(r'```json\s*(\{[\s\S]*?\})\s*```', response, re.DOTALL)
    if json_block_match:
        json_str = json_block_match.group(1)
        try:
            logger.debug("Found JSON within markdown code block.")
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON from markdown block: {e}. Content: '{json_str[:100]}...'")
            # Fall through to try other methods if parsing fails

    # Try finding JSON starting with { and ending with } (greedy match)
    simple_json_match = re.search(r'(\{[\s\S]*\})', response)
    if simple_json_match:
        json_str = simple_json_match.group(1)
        try:
            logger.debug("Found JSON using simple {.*} regex match.")
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON from simple regex match: {e}. Content: '{json_str[:100]}...'")
            # Fall through if parsing fails

    # Final attempt: try parsing the whole string if it looks like JSON
    if response.strip().startswith('{') and response.strip().endswith('}'):
        try:
            logger.debug("Attempting to parse entire response as JSON.")
            return json.loads(response)
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse entire response as JSON: {e}.")
            
    logger.warning(f"Could not find or parse valid JSON in the response. Response start: '{response[:100]}...'")
    return {} # Return empty dict if no JSON found or parsing failed

def format_output(response: str) -> str:
    """
    Formats the LLM response for display, attempting to remove JSON blocks.
    """
    if not response:
        return ""
        
    formatted_response = response
    
    # Remove markdown JSON blocks first
    json_block_pattern = r'```json\s*(\{[\s\S]*?\})\s*```'
    formatted_response = re.sub(json_block_pattern, '', formatted_response)
    
    # Remove potential remaining raw JSON objects (more basic)
    # This is less precise and might remove valid text if it resembles JSON.
    # Consider if this step is truly necessary or if relying on markdown removal is better.
    # json_pattern = r'(\{[\s\S]*\})'
    # formatted_response = re.sub(json_pattern, '', formatted_response)

    return formatted_response.strip()

# Example Usage (for testing this module directly)
if __name__ == '__main__':
    print("Testing LLM Interface...")

    # Ensure OPENAI_API_KEY is set in environment or .env file for tests to run
    if not config.OPENAI_API_KEY:
        print("Skipping tests: OPENAI_API_KEY not found.")
    else:
        # --- Test Embedding ---
        print("\n--- Testing get_embeddings ---")
        try:
            texts_to_embed = ["This is a test sentence.", "Another sentence for embedding."]
            embeddings = get_embeddings(texts_to_embed)
            print(f"Successfully got {len(embeddings)} embeddings.")
            if embeddings:
                print(f"Dimension of first embedding: {len(embeddings[0])}")
                # print(f"First embedding (first 5 dims): {embeddings[0][:5]}")
            print("-" * 20)
        except Exception as e:
            print(f"Error during embedding test: {e}")

        # --- Test Chat Completion ---
        print("\n--- Testing call_llm ---")
        try:
            test_prompt = "Explain the concept of vector embeddings in simple terms."
            llm_response = call_llm(test_prompt)
            print(f"LLM Response for '{test_prompt}':\n{llm_response}")
            print("-" * 20)
        except Exception as e:
            print(f"Error during chat completion test: {e}")

        # --- Test JSON Extraction ---
        print("\n--- Testing extract_json ---")
        test_response_1 = 'Some text before.\n```json\n{\n  "key": "value",\n  "number": 123\n}\n```\nSome text after.'
        test_response_2 = 'Here is the JSON: {"name": "Test", "valid": true}'
        test_response_3 = 'No JSON here.'
        test_response_4 = '{"incomplete": "json"' # Invalid JSON
        
        print(f"Test 1 (Markdown JSON): {extract_json(test_response_1)}")
        print(f"Test 2 (Simple JSON): {extract_json(test_response_2)}")
        print(f"Test 3 (No JSON): {extract_json(test_response_3)}")
        print(f"Test 4 (Invalid JSON): {extract_json(test_response_4)}")
        print("-" * 20)

        # --- Test Output Formatting ---
        print("\n--- Testing format_output ---")
        print(f"Test 1 Formatted:\n'{format_output(test_response_1)}'")
        # Note: format_output currently only removes markdown JSON blocks reliably.
        # print(f"Test 2 Formatted:\n'{format_output(test_response_2)}'")
        print(f"Test 3 Formatted:\n'{format_output(test_response_3)}'")
        print("-" * 20)

    print("LLM Interface testing finished.")

