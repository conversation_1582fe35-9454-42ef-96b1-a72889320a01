# config.py
from pydantic_settings import BaseSettings
from pathlib import Path
from typing import Dict, List, Optional
from pydantic import BaseModel
import os
import logging
class JSONProcessingConfig(BaseModel):
    key_fields: List[str]
    search_fields: List[str]


class Settings(BaseSettings):
    openai_api_key: str
    rag_base_url: str
    rag_api_key: str
    luma_auth_token: str
    elevenlabs_api_key: str  # 添加 ElevenLabs API key
    rag_chat_id: str
    rag_chat_id_gpt_4: str
    #configuration from adtarget_assistant here
    default_llm_model: str = "gpt-4o-mini"
    llm_temperature: float = 0.2
    llm_max_tokens: int = 2000
    max_text_length: int = 8000
    llm_enable_cache: bool = True
    max_retrieved_tokens: int = 100000
    vector_db_host: str = "localhost"
    vector_db_port: int = 19530
    milvus_collection_name: str = "audience_analysis_v2"
    default_embedding_dim: Optional[int] =  None
    dev_mode: bool = True
    default_milvus_metric_type: str = "IP"
    default_milvus_index_type: str = "HNSW"    
    default_milvus_index_params: Dict[str, int] = {"M": 16, "efConstruction": 200}
    default_milvus_search_params: Dict[str, int] = {"ef": 100}
    ddg_search_enabled: bool = True
    ddg_num_results: int = 3
    ddg_search_instructions: str = ("Focus on recent market research and product insights and feedback. Use search results to enhance your knowledge base with up-to-date information. Prioritize authoritative sources that complement existing data.")
    csv_data_directory: str = "data/csv/"
    csv_processing_config_path: str = "csv_processing_config.yaml"
    csv_reader_batch_size: int = 100000
    json_data_directory: str = "data/json/"
    jsonl_data_directory: str = "data/jsonl"
    jsonl_processing_config_path: str = "jsonl_processing_config.yaml"
    jsonl_reader_batch_size: int = 100000
    json_max_file_size_mb: int = 800
    json_max_processing_time_seconds: int = 900
    
    json_processing_config: JSONProcessingConfig = JSONProcessingConfig(
        key_fields=["asin", "parent_asin", "user_id", "rating", "helpful_vote", "verified_purchase"],
        search_fields=["title", "text", "text_chunks", "images"]
    )
    
    default_output_path: str = "output/audience_analysis.json"
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    max_retries_llm: int = 3
    retry_delay_llm: int = 5
    log_level: str = "INFO"
    ollama_api_base: str = "http://localhost:11434"
    # Email settings for Zoho SMTP
    SMTP_SERVER: str = "smtp.zoho.com"
    SMTP_PORT: int = 587
    SMTP_SENDER_EMAIL: str  # The Zoho email address
    SMTP_PASSWORD: str      # Zoho email password or app-specific password
    FEEDBACK_RECEIVER_EMAIL: str  # Email address to receive feedback
    
    class Config:
        env_file = ".env"
        extra = "ignore"

settings = Settings()


# --- Agent Configuration ---
_agent_types_list = [
    "demographics", "interests", "keywords", "usage",
    "satisfaction", "purchase", "personality", "lifestyle", "values"
]
AGENT_TYPES = {agent_type: f"Analyzes audience {agent_type.replace('_', ' ')}." for agent_type in _agent_types_list}
DEFAULT_AGENT_TYPE = "demographics"


if not os.path.exists(settings.csv_processing_config_path):
    raise FileNotFoundError(
        f"CRITICAL: CSV_PROCESSING_CONFIG_PATH '{settings.csv_processing_config_path}' not found. "
        "This YAML configuration file is required to define how CSVs are parsed and processed. "
        "Please create it based on the examples provided."
    )

if not os.path.isdir(settings.csv_data_directory):
    logger = logging.getLogger(__name__) 
    logger.warning(
        f"Warning: CSV_DATA_DIRECTORY '{settings.csv_data_directory}' does not exist. "
        "Attempting to create it. Ensure it contains your CSV files for processing."
    )
    try:
        os.makedirs(settings.csv_data_directory, exist_ok=True)
    except OSError as e:
        raise OSError(
            f"CRITICAL: Could not create CSV_DATA_DIRECTORY '{settings.csv_data_directory}'. "
            f"Please create this directory and place your CSV files in it. Error: {e}"
        )
elif not os.listdir(settings.csv_data_directory): 
    logger = logging.getLogger(__name__)
    logger.warning(
        f"Warning: CSV_DATA_DIRECTORY '{settings.csv_data_directory}' exists but is empty. "
        "No CSV data will be processed."
    )
# 目录配置
BASE_DIR = Path(__file__).resolve().parent.parent
AUDIO_DIR = BASE_DIR / "audio"
AUDIO_DIR.mkdir(parents=True, exist_ok=True)
os.makedirs(settings.csv_data_directory, exist_ok=True)
os.makedirs(os.path.dirname(settings.default_output_path), exist_ok=True)
# API Keys
OPENAI_KEY = settings.openai_api_key
LUMA_AUTH_TOKEN = settings.luma_auth_token
ELEVENLABS_API_KEY = settings.elevenlabs_api_key