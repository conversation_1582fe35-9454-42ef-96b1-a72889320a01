import logging
from fastapi import APIRouter,HTTPException, <PERSON>ie, Response,UploadFile, File, Form
from pydantic import BaseModel
from ...config import settings
import json
import os
import re
from openai import OpenAI, AsyncOpenAI
import openai
import requests
from typing import Any, Dict, List, Optional
from datetime import datetime
from uuid import uuid4, UUID 
from ...milvus_connector import MilvusDBConnector, CSVConnector, JSONConnector
from ...helpers.agent_framework import get_async_workflow
from ...helpers.llm_interface import call_llm
from pathlib import Path
from fastmcp.resources import FileResource
from mcp.server.fastmcp import FastMCP
import asyncio
import uuid
app_state: Dict[str, Any] = {}
router = APIRouter()
#OPENAI_KEY = settings.openai_api_key

#client = OpenAI(api_key=settings.openai_api_key)


# Define the chat request body
class ChatRequest(BaseModel):
    user_message: str
    conversation_id: Optional[str] 

#define the chat reponse body
class ChatResponse(BaseModel):
    response:str
    conversation_id: str
    timestamp: datetime
    conversation_title: Optional[str] = None

#to store messages
conversation_history = {}
#to store chat titles
conversation_titles = {}
def is_valid_uuid(val: str) -> bool:
    try:
        UUID(val)
        return True
    except (ValueError, TypeError):
        return False


vector_db = MilvusDBConnector()
csv_data = CSVConnector()
json_data = JSONConnector()
process_question_async = get_async_workflow(vector_db, csv_data, json_data)
@router.post("/chat", response_model = ChatResponse)
async def chat_endpoint(file: UploadFile = File(...), question: str = Form(...), response: Response = None,conversation_id: Optional[str] = Cookie(None)):
    #create a folder where uploaded files will be saved 
    customer_id = f"customer_{uuid.uuid4().hex[:6]}"
    upload_dir = Path("data/uploads") / customer_id
    upload_dir.mkdir(parents=True, exist_ok=True)
    file_path = upload_dir / file.filename
    absolute_file_path = file_path.resolve()
    with open(file_path, "wb") as f:
        content = await file.read()
        f.write(content)
    #create the mcp server
    mcp = FastMCP("MCP Demo Server", port=8050, stateless_http=True)
    
    # Register as MCP resource based on the uploaded data
    resource = FileResource(
        uri=f"resource://{customer_id}/csv/{file.filename}",
        path=absolute_file_path,
        name=f"{customer_id.upper()} - CSV",
        mime_type="text/csv",
        is_binary=True,
    )
    mcp.add_resource(resource)
    #run the mcp server as a background task so that it doesn't block the agent from providing responses
    async def run_mcp():
      await asyncio.to_thread(mcp.run, transport="streamable-http")

    asyncio.create_task(run_mcp()) 
    print("MCP server started. Running analysis pipeline.")
    try:
        if not conversation_id:
         chat_id = str(uuid4())
         history = conversation_history.setdefault(chat_id, [])
         title = conversation_titles.setdefault(chat_id, None)
         response.set_cookie(key="conversation_id", value=chat_id)
        else:
          chat_id = conversation_id
          history = conversation_history.setdefault(chat_id, [])
          title = conversation_titles.setdefault(chat_id, None)
        print(f"chat id: {conversation_id}")
        #save the user's message in history
        history.append({"role":"user", "content": question})
        analysis_result = await process_question_async(question)
        print("analysis result: ", analysis_result)
        #get the agent's response
        bot_response = analysis_result.get("formatted_output") 
        bot_response = '\n'.join(line for line in bot_response.splitlines() if line.strip()) #to remove any extra spaces or extra new lines from the response
        print("formatted output:" ,analysis_result.get("formatted_output") )
        print("raw response:" ,analysis_result.get("raw_response") )
        #save the agent's response in history
        history.append({"role":"assistant", "content": bot_response})

        chat_title = conversation_titles.get(chat_id)

       # Generate conversation's title based on the topic
        if chat_title is None and len(history) == 2:
           try:
              #directly call call_llm from llm_interface.py
              chat_title = call_llm(
              "Generate a short title for this conversation based on the first 2 messages. The length should be from 3 to 8 words." f"User: {history[0]['content']}\n"
            f"Assistant: {history[1]['content']}"
              )
              chat_title = chat_title.strip('""')
              conversation_titles[chat_id] = chat_title
              print("Generated title:", chat_title)
           except Exception as title_err:
             print("Title generation failed:", title_err)
             chat_title = None

        print("conversation history:", history)

        print("Generated title:",title)
        print("Conversation titles:",conversation_titles)
        return ChatResponse(
            response = bot_response,
            conversation_id = chat_id,
            timestamp = datetime.utcnow(),
            conversation_title = chat_title

        )

    except Exception as e:
        bot_response = analysis_result.get("message")
        raise HTTPException(status_code=500, detail=str(e))





"""@router.post("/chat", response_model = ChatResponse)
async def chat_endpoint(request: ChatRequest, response: Response = None,conversation_id: Optional[str] = Cookie(None)):
    try:
        if not conversation_id:
         chat_id = str(uuid4())
         history = conversation_history.setdefault(chat_id, [])
         title = conversation_titles.setdefault(chat_id, None)
         response.set_cookie(key="conversation_id", value=chat_id)
        else:
          chat_id = conversation_id
          history = conversation_history.setdefault(chat_id, [])
          title = conversation_titles.setdefault(chat_id, None)
        print(f"chat id: {conversation_id}")
        #save the user's message in history
        history.append({"role":"user", "content": request.user_message})
        analysis_result = await process_question_async(request.user_message)
        print("analysis result: ", analysis_result)
        #get the agent's response
        bot_response = analysis_result.get("formatted_output") 
        bot_response = '\n'.join(line for line in bot_response.splitlines() if line.strip()) #to remove any extra spaces or extra new lines from the response
        print("formatted output:" ,analysis_result.get("formatted_output") )
        print("raw response:" ,analysis_result.get("raw_response") )
        #save the agent's response in history
        history.append({"role":"assistant", "content": bot_response})

        chat_title = conversation_titles.get(chat_id)

       # Generate conversation's title based on the topic
        if chat_title is None and len(history) == 2:
           try:
              #directly call call_llm from llm_interface.py
              chat_title = call_llm(
              "Generate a short title for this conversation based on the first 2 messages. The length should be from 3 to 8 words." f"User: {history[0]['content']}\n"
            f"Assistant: {history[1]['content']}"
              )
              chat_title = chat_title.strip('""')
              conversation_titles[chat_id] = chat_title
              print("Generated title:", chat_title)
           except Exception as title_err:
             print("Title generation failed:", title_err)
             chat_title = None

        print("conversation history:", history)

        print("Generated title:",title)
        print("Conversation titles:",conversation_titles)
        return ChatResponse(
            response = bot_response,
            conversation_id = chat_id,
            timestamp = datetime.utcnow(),
            conversation_title = chat_title

        )

    except Exception as e:
        bot_response = analysis_result.get("message")
        raise HTTPException(status_code=500, detail=str(e))"""