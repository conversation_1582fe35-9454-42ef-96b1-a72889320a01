# app/route/video_editing/controllers.py
import asyncio
from concurrent.futures import Thr<PERSON>PoolExecutor
import sys
import traceback
from uuid import uuid4
from fastapi import APIRouter, Form, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.responses import StreamingResponse, FileResponse, JSONResponse
import ffmpeg
from openai import OpenAI, AsyncOpenAI
import openai
import requests
from ...helpers.video_helpers import *
from typing import Any, Dict, List
import time
import json
import os
from pathlib import Path
from datetime import datetime
from ...models.video import *
from ...models.script import *
from ...config import settings, AUDIO_DIR
from lumaai import LumaAI
import aiohttp
import logging
from pydantic import BaseModel
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
from ...helpers.voice_clone import clone_voice
from elevenlabs import VoiceSettings
from elevenlabs.client import ElevenLabs
from elevenlabs import ElevenLabs  # Added import

import os

# 确保使用绝对路径
LOG_FILE = os.path.abspath(os.path.join(os.getcwd(), '..', '..', 'video_export.log'))

# 强制刷新打印缓冲
import functools
print = functools.partial(print, flush=True)

# 确保日志目录存在
os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,  # 将级别改为 INFO
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(LOG_FILE, mode='a', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 添加直接打印到控制台的处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

router = APIRouter()

OPENAI_KEY = settings.openai_api_key
LUMA_AUTH_TOKEN = settings.luma_auth_token
ELEVENLABS_API_KEY = settings.elevenlabs_api_key

DB_DIR = Path("database/video_campaigns")
DB_DIR.mkdir(parents=True, exist_ok=True)

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.abspath(os.path.join(CURRENT_DIR, '../../'))
VIDEO_DIR = os.path.abspath(os.path.join(PROJECT_DIR, 'videos'))

#AUDIO_DIR = Path(os.path.join(PROJECT_DIR, 'audio'))
AUDIO_DIR.mkdir(parents=True, exist_ok=True)

# Create audio directory if it doesn't exist
AUDIO_UPLOAD_DIR = Path("uploads/audio")
AUDIO_UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

class CampaignManager:
    def __init__(self, base_dir: Path):
        self.base_dir = base_dir
        self.lock_file = base_dir / "generation_lock.json"
        
    def _get_campaign_path(self, campaign_id: str) -> Path:
        return self.base_dir / f"{campaign_id}.json"
        
    def save_campaign(self, campaign_data: dict):
        """Save campaign data to file with proper datetime handling"""
        file_path = self._get_campaign_path(campaign_data['id'])
        # Create a copy for serialization
        json_data = campaign_data.copy()
        # Convert datetime to string if present
        if isinstance(json_data.get('createdAt'), datetime):
            json_data['createdAt'] = json_data['createdAt'].isoformat()
            
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2)
            
    def load_campaign(self, campaign_id: str) -> dict:
        """Load campaign data from file"""
        file_path = self._get_campaign_path(campaign_id)
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"Campaign not found: {campaign_id}")
            
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # Convert string back to datetime
            if 'createdAt' in data:
                data['createdAt'] = datetime.fromisoformat(data['createdAt'])
            return data
            
    def list_campaigns(self) -> List[dict]:
        """List all campaigns"""
        campaigns = []
        for file_path in self.base_dir.glob("*.json"):
            if file_path != self.lock_file:  # Skip lock file
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'createdAt' in data:
                        data['createdAt'] = datetime.fromisoformat(data['createdAt'])
                    campaigns.append(data)
        return campaigns
        
    def update_generation_status(self, campaign_id: str, script_id: str, status: dict):
        """Update generation status for a campaign"""
        campaign_data = self.load_campaign(campaign_id)
        if "generations" not in campaign_data:
            campaign_data["generations"] = {}
        
        campaign_data["generations"][script_id] = {
            **status,
            "updated_at": datetime.now().isoformat()
        }
        
        self.save_campaign(campaign_data)
        return campaign_data
        
    async def acquire_lock(self, campaign_id: str, script_id: str) -> bool:
        """Try to acquire generation lock"""
        if self.lock_file.exists():
            try:
                with open(self.lock_file, 'r') as f:
                    lock_data = json.load(f)
                lock_time = datetime.fromisoformat(lock_data['locked_at'])
                # Check if lock is stale (older than 1 hour)
                if (datetime.now() - lock_time).total_seconds() <= 3600:
                    return False
            except:
                pass  # If lock file is corrupted, we'll overwrite it
                
        lock_data = {
            'campaign_id': campaign_id,
            'script_id': script_id,
            'locked_at': datetime.now().isoformat()
        }
        with open(self.lock_file, 'w') as f:
            json.dump(lock_data, f)
        return True
        
    async def release_lock(self):
        """Release generation lock"""
        if self.lock_file.exists():
            os.remove(self.lock_file)

# Initialize campaign manager
campaign_manager = CampaignManager(DB_DIR)

executor = ThreadPoolExecutor(max_workers=3)  # Limit concurrent video generations

async def generate_video_for_segment(client: LumaAI, script: str, segment_id: str) -> dict:
    """Generate video for a single script segment using Luma AI."""
    try:
        print(f"[Lumma] Attempting to generate video for segment {segment_id}")
        print(f"[Lumma] Script content: {script[:100]}...")  # Only print first 100 chars for brevity
        
        generation = client.generations.create(
            prompt=script
        )
        
        print(f"[Lumma] Successfully created generation with ID: {generation.id}")
        
        status = {
            "segment_id": segment_id,
            "generation_id": generation.id,
            "status": "pending",
            "video_url": None,
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 设置最大等待时间为10分钟
        max_wait_time = 600  # 10分钟
        start_time = time.time()
        
        while True:
            # 检查是否超时
            if time.time() - start_time > max_wait_time:
                error_msg = "Generation timeout after 10 minutes"
                print(f"[Lumma] Generation {generation.id} timed out")
                status.update({
                    "status": "failed",
                    "error": error_msg,
                    "failed_at": time.strftime("%Y-%m-%d %H:%M:%S")
                })
                break
                
            if generation.id is None:
                error_msg = "Generation ID is None"
                status.update({
                    "status": "failed",
                    "error": error_msg,
                    "failed_at": time.strftime("%Y-%m-%d %H:%M:%S")
                })
                break
                
            generation = client.generations.get(id=generation.id)
            print(f"[Lumma] Generation {generation.id} state: {generation.state}")
            
            if generation.state == "completed":
                if not hasattr(generation, 'assets') or not generation.assets or not hasattr(generation.assets, 'video'):
                    error_msg = "Generation completed but no video asset found"
                    status.update({
                        "status": "failed",
                        "error": error_msg,
                        "failed_at": time.strftime("%Y-%m-%d %H:%M:%S")
                    })
                    break
                    
                status.update({
                    "status": "completed",
                    "video_url": generation.assets.video,
                    "completed_at": time.strftime("%Y-%m-%d %H:%M:%S")
                })
                print(f"[Lumma] Generation {generation.id} completed successfully")
                break
                
            elif generation.state == "failed":
                error_msg = generation.failure_reason
                print(f"[Lumma] Generation {generation.id} failed with error: {error_msg}")
                status.update({
                    "status": "failed",
                    "error": error_msg,
                    "failed_at": time.strftime("%Y-%m-%d %H:%M:%S")
                })
                break
                
            await asyncio.sleep(3)
            
        return status
        
    except Exception as e:
        error_msg = str(e)
        print(f"[Lumma] Unexpected error for segment {segment_id}: {error_msg}")
        print(f"[Lumma] Full traceback: {traceback.format_exc()}")
        return {
            "segment_id": segment_id,
            "generation_id": None,
            "status": "error",
            "error": error_msg,
            "failed_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }

@router.get("/hello")
def hello_video_editing():
    return {"message": "Hello from Video Editing!"}

@router.get("/videos", response_model=List[str])
async def list_videos():
    """List all video files in the video directory"""
    try:
        files = [f for f in os.listdir(VIDEO_DIR) if f.endswith(".mp4")]
        return files
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Video directory not found")

@router.get("/video/{video_name}")
async def get_video(video_name: str):
    """Returns the video file stream with the specified name"""
    file_path = os.path.join(VIDEO_DIR, video_name+".mp4")
    
    time.sleep(0.5)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Video file not found")
    
    return StreamingResponse(
        video_streamer(file_path),
        media_type="video/mp4"
    )
    
    
def save_campaign_to_file(campaign_data: dict):
    """Save campaign data to a JSON file"""
    file_path = DB_DIR / f"{campaign_data['id']}.json"
    
    # Create a copy of the data for serialization
    json_data = campaign_data.copy()
    # Convert datetime to string
    json_data['createdAt'] = json_data['createdAt'].isoformat()

    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2)

def generate_campaign_id() -> str:
    """Generate a unique campaign ID"""
    from uuid import uuid4
    return str(uuid4())

@router.post("/create_video_campaign", response_model=VideoAdCampaignResponse)
async def create_video_campaign(
    campaign: VideoAdCampaign, 
    background_tasks: BackgroundTasks
):
    """Create a new video campaign with automatic video generation"""
    
    # Generate campaign ID first
    campaign.id = str(uuid4())
    
    # Try to acquire lock first
    if not await campaign_manager.acquire_lock(campaign.id, str(uuid4())):
        raise HTTPException(
            status_code=429,
            detail="Another video generation is in progress. Please try again later."
        )
    
    try:
        # Delete all cloned voices first
        try:
            cloned_voices = get_cloned_voices()
            for voice_id in cloned_voices:
                delete_voice(voice_id)
        except Exception as e:
            logger.error(f"Failed to delete cloned voices: {str(e)}")
            # Continue with campaign creation even if voice deletion fails
            
        # 生成广告标题
        title = await generate_ad_title(
            campaign.brandName,
            campaign.summary,
            campaign.description
        )
        
        # Create campaign data
        campaign_data = {
            "id": campaign.id,
            "title": title,
            "brandName": campaign.brandName,
            "summary": campaign.summary,
            "description": campaign.description,
            "adLength": campaign.adLength,
            "createdAt": datetime.now(),
            "status": "pending",
            "generations": {}
        }
    
        # Generate scripts
        generated_scripts = await generate_ad_scripts(
            campaign_data,
            openai_key=OPENAI_KEY
        )
        
        script_id = str(uuid4())
        segments = [
            {
                "id": segment.id,
                "script": segment.script,
                "narrator": segment.narrator
            }
            for segment in generated_scripts.segments
        ]
        
        campaign_data["scripts"] = [{
            "id": script_id,
            "scripts": {
                "segments": segments
            }
        }]

        # Save initial campaign data
        campaign_manager.save_campaign(campaign_data)
        
        # Add video generation to background tasks
        background_tasks.add_task(
            process_video_generation,
            campaign_data,
            script_id
        )

        return VideoAdCampaignResponse(**campaign_data)

    except Exception as e:
        # Clear lock on failure
        await campaign_manager.release_lock()
        logger.error(f"Failed to create video campaign: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create video campaign: {str(e)}"
        )

async def process_video_generation(campaign_data: dict, script_id: str):
    """Process video generation in background"""
    try:
        client = LumaAI(auth_token=LUMA_AUTH_TOKEN)
        script = next(s for s in campaign_data["scripts"] if s["id"] == script_id)
        
        # Initialize generation status
        campaign_manager.update_generation_status(
            campaign_data["id"], 
            script_id,
            {
                "status": "in_progress",
                "segments": []
            }
        )
        
        # Process each segment
        generation_results = []
        for segment in script["scripts"]["segments"]:
            try:
                result = await generate_video_for_segment(
                    client,
                    segment["script"],
                    segment["id"]
                )
                
                # Ensure result has required fields
                if isinstance(result, dict):
                    if "segment_id" not in result:
                        result["segment_id"] = segment["id"]
                    generation_results.append(result)
                else:
                    generation_results.append({
                        "segment_id": segment["id"],
                        "status": "completed",
                        "result": result
                    })
                
                # Update generation status after each segment
                campaign_manager.update_generation_status(
                    campaign_data["id"],
                    script_id,
                    {
                        "status": "in_progress",
                        "segments": generation_results
                    }
                )
                    
            except Exception as segment_error:
                logger.error(f"Error generating video for segment {segment['id']}: {str(segment_error)}")
                generation_results.append({
                    "segment_id": segment["id"],
                    "status": "error",
                    "error": str(segment_error)
                })
        
        # 视频生成完成后，自动生成默认语音 (nova)
        try:
            # 设置默认语音为nova
            script["default_voice"] = "nova"
            
            voice_request = VoiceUpdateRequest(
                voice="nova",
                segments=[VoiceSegment(id=s["id"], text=s["script"]) 
                         for s in script["scripts"]["segments"]],
                service="openai",
                voice_id=None
            )
            await update_script_voice(
                campaign_data["id"],
                script_id,
                voice_request
            )
        except Exception as voice_error:
            logger.error(f"Error generating default voice: {str(voice_error)}")
        
        # Update final status
        campaign_manager.update_generation_status(
            campaign_data["id"],
            script_id,
            {
                "status": "completed",
                "segments": generation_results
            }
        )
            
    except Exception as e:
        logger.error(f"Error in video generation process: {str(e)}")
        campaign_manager.update_generation_status(
            campaign_data["id"],
            script_id,
            {
                "status": "error",
                "error": str(e)
            }
        )
    
    finally:
        await campaign_manager.release_lock()

@router.get("/video_campaigns/{campaign_id}", response_model=VideoAdCampaignResponse)
async def get_video_campaign(campaign_id: str):
    try:
        campaign_data = campaign_manager.load_campaign(campaign_id)
        return VideoAdCampaignResponse(**campaign_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving campaign: {str(e)}"
        )
        
@router.get("/video_campaigns")
async def list_video_campaigns():
    try:
        return campaign_manager.list_campaigns()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing campaigns: {str(e)}"
        )

async def generate_ad_scripts(campaign_data: dict, openai_key: str) -> AdScripts:
    """
    Generate video ad scripts using OpenAI based on campaign data.
    Returns multiple script-narrator pairs based on ad length.
    """
    client = OpenAI(api_key=openai_key)
    
    # Calculate number of segments based on ad length (5s per segment)
    num_segments = int(campaign_data['adLength']) // 5
    
    system_prompt = f"""
    Generate {num_segments} detailed script segments for a video advertisement. Each segment should be EXACTLY 5 seconds long.

    Content Strategy (Based on number of segments):
    - For 1 segment (5s): Focus ONLY on the core product value proposition. Be direct and impactful.
    - For 2-3 segments (10-15s): Start with product highlight, then add 1-2 key benefits or features.
    - For 4+ segments (20s+): Develop a mini-story arc that builds up to the product reveal.

    For each segment, provide:
    1. A detailed visual description that includes:
    - Camera angle and movement (e.g., close-up, pan, zoom, tracking shot)
    - Lighting and atmosphere
    - Color scheme and visual tone
    - Specific object placement and movement
    - Actor expressions and movements (if present)

    2. A VERY concise narrator's voiceover text that:
    - Must be SHORT enough to be spoken clearly within 5 seconds
    - Should be around 8-12 words maximum
    - For single segments: Focus on core product value only
    - For multiple segments: Build narrative progressively
    - Uses clear, impactful language
    - Maintains a professional tone

    Key requirements for each script:
    - Focus on a SINGLE scene or view per 5-second segment
    - Include SPECIFIC camera directions
    - Describe the exact visual elements and their placement
    - Keep scene transitions minimal
    - Ensure the scene can be feasibly rendered in 5 seconds
    - Keep narrator text extremely concise and speakable within 5 seconds

    Advertisement Details:
    - Brand: {campaign_data['brandName']}
    - Summary: {campaign_data['summary']}
    - Description: {campaign_data['description']}

    Example format for single segment (5s):
    {{
    "id": 1,
    "script": "Dynamic close-up shot of a sleek smartwatch on a minimalist white surface. Sharp product lighting emphasizes the curved glass and premium metal finish. Camera smoothly rotates to reveal the vibrant display coming to life.",
    "narrator": "Elevate your fitness journey with SmartFit Pro."
    }}

    Example format for multi-segment story:
    {{
    "id": 1,
    "script": "Early morning light streams through a window. Close-up of a person's determined expression as they prepare for their workout. Soft, inspiring lighting creates a motivational atmosphere.",
    "narrator": "Every journey begins with a choice..."
    }},
    {{
    "id": 2,
    "script": "Tracking shot following the smartwatch as it monitors heart rate, steps, and workout intensity. Clean, modern fitness environment. Camera moves smoothly, highlighting the real-time health tracking.",
    "narrator": "SmartFit Pro guides every step of your transformation."
    }}
    """


    completion = client.beta.chat.completions.parse(
        model="gpt-4o-2024-08-06",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Create {num_segments} engaging 5-second video segments"}
        ],
        response_format=AdScripts,
    )

    parsed = completion.choices[0].message.parsed
    if parsed is None:
        raise ValueError("Failed to generate ad scripts: OpenAI returned None")
        
    return parsed

@router.post("/regenerate_script/{campaign_id}")
async def regenerate_script(
    campaign_id: str, 
    background_tasks: BackgroundTasks
):
    """Regenerate script for an existing campaign"""
    
    # Try to acquire lock first
    if not await campaign_manager.acquire_lock(campaign_id, str(uuid4())):
        raise HTTPException(
            status_code=429,
            detail="Another video generation is in progress. Please try again later."
        )
    
    try:
        # Get existing campaign data
        campaign_data = campaign_manager.load_campaign(campaign_id)

        # Generate new script
        generated_scripts = await generate_ad_scripts(
            campaign_data,
            openai_key=OPENAI_KEY
        )
        
        # Add new script
        script_id = str(uuid4())
        new_script = {
            "id": script_id,
            "scripts": {
                "segments": [
                    {
                        "id": segment.id,
                        "script": segment.script,
                        "narrator": segment.narrator
                    }
                    for segment in generated_scripts.segments
                ]
            }
        }
        
        campaign_data["scripts"].append(new_script)

        # Save updated campaign data
        campaign_manager.save_campaign(campaign_data)

        # Add video generation to background tasks
        background_tasks.add_task(
            process_video_generation,
            campaign_data,
            script_id
        )

        return VideoAdCampaignResponse(**campaign_data)

    except Exception as e:
        # Clear lock on failure
        await campaign_manager.release_lock()
        logger.error(f"Failed to regenerate script: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to regenerate script: {str(e)}"
        )

async def generate_videos_for_campaign(campaign_data: dict, script_id: str):
    """Generate videos for all segments in a campaign script"""
    try:
        client = LumaAI(auth_token=LUMA_AUTH_TOKEN)
        script = next(s for s in campaign_data["scripts"] if s["id"] == script_id)
        
        video_tasks = []
        for segment in script["scripts"]["segments"]:
           # generation_id = str(uuid4())
            task = generate_video_for_segment(
                client,
                segment["script"],
                segment["id"]
            #    generation_id
            )
            video_tasks.append(task)

        generation_results = await asyncio.gather(*video_tasks)
        
        if "generations" not in campaign_data:
            campaign_data["generations"] = {}
            
        campaign_data["generations"][script_id] = {
            "status": "completed",
            "segments": generation_results,
            "updated_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Save updated campaign data
        campaign_id = campaign_data["id"]
        file_path = DB_DIR / f"{campaign_id}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(campaign_data, f, indent=2, default=str)
            
        # Clear the lock after successful generation
        await campaign_manager.release_lock()
        
        return generation_results
        
    except Exception as e:
        # Clear lock on failure
        await campaign_manager.release_lock()
        raise e

async def get_campaign_videos(campaign_id: str, script_id: str) -> List[str]:
    """
    Get the list of generated video files for a specific campaign script.
    """
    try:
        campaign_data = campaign_manager.load_campaign(campaign_id)

        if "videos" not in campaign_data or script_id not in campaign_data["videos"]:
            return []

        return [
            segment["video_file"]
            for segment in campaign_data["videos"][script_id]["segments"]
        ]

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get video list: {str(e)}"
        )
        
@router.get("/generation_status/{campaign_id}")
async def get_generation_status(campaign_id: str):
    """Get the current generation status"""
    try:
        # Check lock file first
        if campaign_manager.lock_file.exists():
            with open(campaign_manager.lock_file, 'r') as f:
                lock_data = json.load(f)
                
            if lock_data['campaign_id'] == campaign_id:
                return {
                    "status": "generating",
                    "script_id": lock_data['script_id'],
                    "locked_at": lock_data['locked_at']
                }
        
        # Check campaign file for completed generations
        campaign_data = campaign_manager.load_campaign(campaign_id)

        if "generations" not in campaign_data:
            return {
                "status": "no_generations",
                "message": "No generations found for this campaign"
            }

        return {
            "status": "completed",
            "generations": campaign_data["generations"]
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get generation status: {str(e)}"
        )

from pydantic import BaseModel
from typing import List, Optional

class AudioUpdateRequest(BaseModel):
    audio_id: Optional[str] = None

class VoiceSegment(BaseModel):
    id: int
    text: str

class VoiceUpdateRequest(BaseModel):
    voice: str
    segments: List[VoiceSegment]
    service: str = "openai"
    voice_id: Optional[str] = None

@router.post("/scripts/{campaign_id}/{script_id}/audio")
async def update_script_audio(
    campaign_id: str,
    script_id: str,
    request: AudioUpdateRequest,
):
    try:
        logger.info("=" * 50)
        logger.info("Updating script audio with params:")
        logger.info(f"campaign_id: {campaign_id}")
        logger.info(f"script_id: {script_id}")
        logger.info(f"request: {request.model_dump()}")
        logger.info("=" * 50)
        
        campaign_data = campaign_manager.load_campaign(campaign_id)
        
        # 找到对应的脚本并更新音频ID
        script = next(
            (s for s in campaign_data["scripts"] if s["id"] == script_id),
            None
        )
        
        if not script:
            raise HTTPException(
                status_code=404,
                detail=f"Script not found: {script_id}"
            )

        script["audio_id"] = request.audio_id
        
        # 保存更新后的数据
        campaign_manager.save_campaign(campaign_data)

        return {"message": "Script audio updated successfully"}

    except Exception as e:
        logger.error(f"Failed to update script audio: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update script audio: {str(e)}"
        )

@router.post("/scripts/{campaign_id}/{script_id}/voice")
async def update_script_voice(
    campaign_id: str,
    script_id: str,
    request: VoiceUpdateRequest,
):
    try:
        campaign_data = campaign_manager.load_campaign(campaign_id)
        scripts = campaign_data.get("scripts", [])
        
        # Find the script in the list
        script = next((s for s in scripts if s.get("id") == script_id), None)
        
        if not script:
            logger.error(f"Script not found. campaign_id: {campaign_id}, script_id: {script_id}")
            logger.error(f"Available scripts: {[s.get('id') for s in scripts]}")
            raise HTTPException(status_code=404, detail="Script not found")
            
        logger.info(f"Found script: {script.get('id')}")
        logger.info(f"Processing voice update request: {request.model_dump()}")
            
        # 直接使用请求中的 segments，因为前端已经提供了正确的 narrator 文本
        has_generated = await generate_voice_audio(
            campaign_id=campaign_id,
            script_id=script_id,
            voice=request.voice,
            segments=request.segments,  # 直接使用前端传来的 segments
            campaign_data=campaign_data,
            script=script,
            service=request.service,
            voice_id=request.voice_id
        )
        
        if has_generated:
            # 更新default_voice
            script["default_voice"] = request.voice
            if request.service == "elevenlabs":
                script["elevenlabs_voice_id"] = request.voice_id
            # 保存更新后的 campaign 数据
            campaign_manager.save_campaign(campaign_data)
            
        return {"status": "success"}
        
    except Exception as e:
        logger.error(f"Failed to update voice: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update voice: {str(e)}"
        )

async def generate_voice_audio(
    campaign_id: str,
    script_id: str,
    voice: str,
    segments: List[VoiceSegment],
    campaign_data: dict,
    script: dict,
    service: str = "openai",
    voice_id: Optional[str] = None
) -> bool:
    try:
        # Log input parameters
        logger.info(f"Generating voice audio with params:")
        logger.info(f"campaign_id: {campaign_id}")
        logger.info(f"script_id: {script_id}")
        logger.info(f"voice: {voice}")
        logger.info(f"service: {service}")
        logger.info(f"voice_id: {voice_id}")
        
        # Verify OpenAI key is set
        if service == "openai" and not OPENAI_KEY:
            raise ValueError("OpenAI API key is not configured")
            
        # Create voice directory
        voice_dir = voice if service == "openai" else f"elevenlabs_{voice_id}"
        script_audio_dir = AUDIO_DIR / campaign_id / script_id / voice_dir
        
        # Verify directory can be created
        try:
            script_audio_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created audio directory: {script_audio_dir}")
        except Exception as e:
            raise ValueError(f"Failed to create audio directory: {str(e)}")
        
        has_generated = False
        
        # 获取 script 中的所有 segments
        script_segments = script["scripts"]["segments"]
        
        # 为每个需要生成语音的 segment 生成音频
        for segment in segments:
            try:
                # 从 script 中找到对应的 segment
                script_segment = next(
                    (s for s in script_segments if s["id"] == int(segment.id)),
                    None
                )
                
                if not script_segment:
                    logger.error(f"Could not find script segment {segment.id}")
                    continue
                
                # 使用 script 中的 narrator 文本
                narrator_text = script_segment["narrator"]
                
                audio_filename = f"narrator_{segment.id}.mp3"
                audio_path = script_audio_dir / audio_filename
                
                logger.info(f"Processing segment {segment.id}")
                logger.info(f"------------------------narrator text----------------------: {narrator_text}")
                
                if not audio_path.exists():
                    has_generated = True
                    
                    if service == "openai":
                        client = openai.OpenAI(api_key=OPENAI_KEY)
                        try:
                            logger.info(f"------------------------Using OpenAI TTS----------------------")
                            logger.info(f"Voice: {voice}")
                            logger.info(f"Model: tts-1")
                            logger.info(f"Text: {narrator_text}")
                            response = client.audio.speech.create(
                                model="tts-1",
                                voice=voice,  
                                input=narrator_text,  # 使用 script 中的 narrator 文本
                            )
                            
                            with open(audio_path, 'wb') as f:
                                for chunk in response.iter_bytes():
                                    f.write(chunk)
                            logger.info(f"Successfully generated audio for segment {segment.id}")
                            logger.info(f"Audio file saved to: {audio_path}")
                                
                        except Exception as e:
                            logger.error(f"OpenAI API error: {str(e)}")
                            logger.error(f"Voice: {voice}")
                            logger.error(f"Text: {narrator_text}")
                            raise ValueError(f"Failed to generate audio via OpenAI: {str(e)}")
                                
                    elif service == "elevenlabs":
                        if voice_id is None:
                            raise ValueError("voice_id is required for ElevenLabs TTS service")
                            
                        client = ElevenLabs(api_key=ELEVENLABS_API_KEY)
                        response = client.text_to_speech.convert(
                            voice_id=voice_id,  
                            optimize_streaming_latency="0",
                            output_format="mp3_22050_32",
                            text=narrator_text,  # 使用 script 中的 narrator 文本
                            model_id="eleven_turbo_v2",
                            voice_settings=VoiceSettings(
                                stability=0.0,
                                similarity_boost=1.0,
                                style=0.0,
                                use_speaker_boost=True,
                            ),
                        )
                        
                        with open(audio_path, "wb") as f:
                            for chunk in response:
                                if chunk:
                                    f.write(chunk)
                    else:
                        raise ValueError(f"Unsupported TTS service: {service}")
                
                # Update paths
                audio_url = f"/audio/{campaign_id}/{script_id}/{voice_dir}/{audio_filename}"
                
                # Update script segments
                if not isinstance(script, dict) or "scripts" not in script or not isinstance(script["scripts"], dict) or "segments" not in script["scripts"]:
                    logger.error(f"Invalid script structure: {script}")
                    raise ValueError(f"Invalid script structure. Expected dict with 'scripts.segments', got: {type(script)}")
                
                script_segments = script["scripts"]["segments"]
                if not isinstance(script_segments, list):
                    raise ValueError(f"Invalid segments structure. Expected list, got: {type(script_segments)}")
                
                target_segment = next(
                    (s for s in script_segments if s["id"] == int(segment.id)),
                    None
                )
                
                if target_segment:
                    if target_segment.get("narrator_audio") != audio_url:
                        target_segment["narrator_audio"] = audio_url
                        logger.info(f"Updated script segment {segment.id} narrator_audio to: {audio_url}")
                        has_generated = True
                else:
                    logger.warning(f"Could not find script segment {segment.id} to update narrator_audio")
                
                # Update generation segments
                generation = campaign_data.setdefault("generations", {}).setdefault(script_id, {
                    "status": "completed",
                    "segments": [],
                    "updated_at": datetime.now().isoformat()
                })

                # Find or create segment with proper error handling
                try:
                    gen_segment = next(
                        (s for s in generation["segments"] if s.get("segment_id") == int(segment.id)),
                        None
                    )
                except (KeyError, TypeError) as e:
                    logger.error(f"Error accessing segment data: {str(e)}")
                    gen_segment = None

                if not gen_segment:
                    gen_segment = {
                        "segment_id": int(segment.id),
                        "generation_id": str(uuid4()),
                        "status": "completed",
                        "created_at": datetime.now().isoformat(),
                        "completed_at": datetime.now().isoformat(),
                    }
                    generation["segments"].append(gen_segment)
                    has_generated = True

                if gen_segment.get("narrator_audio") != audio_url:
                    gen_segment["narrator_audio"] = audio_url
                    logger.info(f"Updated generation segment {segment.id} narrator_audio to: {audio_url}")
                    has_generated = True
                    
            except Exception as e:
                logger.error(f"Failed processing segment {segment.id}: {str(e)}")
                raise
                    
        return has_generated

    except Exception as e:
        logger.error(f"Failed to generate voice audio: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise
  
async def generate_ad_title(brand_name: str, summary: str, description: str) -> str:
    """
    Generate a short and powerful advertising title based on brand info
    Returns a title with no more than 5 words
    """
    try:
        client = AsyncOpenAI(api_key=OPENAI_KEY)
        
        prompt = f"""Generate a short, powerful advertising title based on the following information:

                Brand Name: {brand_name}
                Summary: {summary}
                Description: {description}

                Requirements:
                1. Title must be in English
                2. Maximum 5 words
                3. End with "AD" naturally
                4. Highlight brand's unique value proposition or core selling point
                5. Easy to remember
                6. Should be creative and modern
                7. Can use capitalization for emphasis

                Only return the title itself, no explanation needed. 
                For example:
                - "Dream Big, Live Bold AD"
                - "Your Style, Your Lead AD"
                - "Pure Luxury Inside AD"
                - "Life Changes Today AD"
                - "Next Level Experience AD"
                """

        response = await client.chat.completions.create(
            model="gpt-4-1106-preview",
            messages=[{
                "role": "system",
                "content": """You are a professional advertising copywriter specializing in creating short, 
                impactful English titles. Your titles are modern, memorable, and resonate with global audiences."""
            }, {
                "role": "user",
                "content": prompt
            }],
            temperature=0.7,
            max_tokens=20
        )

        content = response.choices[0].message.content
        title = content.strip() if content is not None else f"{brand_name} AD"
        
        # Count words (split by whitespace)
        words = title.split()
        if len(words) > 5:
            # Keep only first 5 words
            title = ' '.join(words[:5])
            
        # Remove any punctuation at the end
        title = title.rstrip('.!?')
        
        # Ensure title ends with AD
        if not title.endswith('AD'):
            if title.endswith(' AD'):  # If it has a space before AD
                title = title[:-3] + ' AD'
            else:
                title = title + ' AD'
            
        return title

    except Exception as e:
        logger.error(f"Failed to generate ad title: {str(e)}")
        # Default title using brand name with AD suffix
        return f"{brand_name} Innovation AD" if len(brand_name.split()) < 4 else f"{brand_name} AD"
  
@router.post("/upload-audio/{campaign_id}")
async def upload_audio_file(campaign_id: str, audio_file: UploadFile = File(...)):
    """
    Upload an audio file for a specific campaign
    """
    try:
        # Create campaign-specific directory if it doesn't exist
        campaign_audio_dir = AUDIO_UPLOAD_DIR / campaign_id
        campaign_audio_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate a unique filename with original extension
        if audio_file.filename is None:
            file_extension = ".mp3"  # Default extension for audio files
        else:
            file_extension = os.path.splitext(audio_file.filename)[1]
        unique_filename = f"{uuid4()}{file_extension}"
        file_path = campaign_audio_dir / unique_filename
        
        # Save the uploaded file
        with open(file_path, "wb") as buffer:
            content = await audio_file.read()
            buffer.write(content)

        # Clone voice
        voice_id = clone_voice(
            name=f"Voice_{campaign_id}_{unique_filename}",
            description=f"Cloned voice for campaign {campaign_id}",
            files=[str(file_path)],
            remove_background_noise=True
        )

        if voice_id:
            # Load and update campaign data
            campaign_data = campaign_manager.load_campaign(campaign_id)
            for script in campaign_data["scripts"]:
                if "elevenlabs_voice_id" not in script:
                    script["elevenlabs_voice_id"] = []
                if voice_id not in script["elevenlabs_voice_id"]:
                    script["elevenlabs_voice_id"].append(voice_id)
            campaign_manager.save_campaign(campaign_data)
            
        return {
            "status": "success",
            "message": "Audio file uploaded successfully",
            "file_path": str(file_path.relative_to(AUDIO_UPLOAD_DIR)),
            "campaign_id": campaign_id,
            "voice_id": voice_id if voice_id else None
        }
        
    except Exception as e:
        logger.error(f"Error uploading audio file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error uploading audio file: {str(e)}"
        )

@router.put("/scripts/{campaign_id}/{script_id}/text_overlays")
async def update_text_overlays(
    campaign_id: str,
    script_id: str,
    overlays: List[TextOverlay]
):
    """Update text overlays for a script"""
    try:
        campaign_data = campaign_manager.load_campaign(campaign_id)
        
        script = next(
            (s for s in campaign_data["scripts"] if s["id"] == script_id),
            None
        )
        if not script:
            raise HTTPException(
                status_code=404,
                detail=f"Script not found: {script_id}"
            )
            
        # Update text overlays
        script["textOverlays"] = [overlay.dict() for overlay in overlays]
        
        # Save updated campaign data
        campaign_manager.save_campaign(campaign_data)
        
        return {"message": "Text overlays updated successfully"}
        
    except Exception as e:
        logger.error(f"Failed to update text overlays: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update text overlays: {str(e)}"
        )

class FeedbackRequest(BaseModel):
    message: str
    user_email: Optional[str] = None

@router.post("/feedback")
async def send_feedback(feedback: FeedbackRequest):
    if not settings.SMTP_SENDER_EMAIL or not settings.SMTP_PASSWORD or not settings.FEEDBACK_RECEIVER_EMAIL:
        raise HTTPException(
            status_code=500,
            detail="Email settings not configured"
        )

    # Create email message
    message = MIMEMultipart()
    message["From"] = settings.SMTP_SENDER_EMAIL
    message["To"] = settings.FEEDBACK_RECEIVER_EMAIL
    message["Subject"] = "New Feedback from AdSynthetica Video Editor"

    # Email body
    body = f"""
    New feedback received from AdSynthetica Video Editor:
    
    Message:
    {feedback.message}
    
    From: {feedback.user_email if feedback.user_email else 'Anonymous'}
    """
    
    message.attach(MIMEText(body, "plain"))

    try:
        # Connect to Zoho SMTP server and send email
        with smtplib.SMTP(settings.SMTP_SERVER, settings.SMTP_PORT) as server:
            server.starttls()
            # For Zoho, the login username is the full email address
            server.login(settings.SMTP_SENDER_EMAIL, settings.SMTP_PASSWORD)
            server.send_message(message)
            
        logger.info(f"Feedback email sent successfully to {settings.FEEDBACK_RECEIVER_EMAIL}")
        return {"status": "success", "message": "Feedback sent successfully"}
    except Exception as e:
        logger.error(f"Failed to send feedback email: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to send feedback: {str(e)}"
        )

import requests

def get_cloned_voices():
    """Get all cloned voices from ElevenLabs"""
    client = ElevenLabs(
        api_key=ELEVENLABS_API_KEY,
    )
    cloned_voices = []
    voices = client.voices.get_all()
    voices_json = json.loads(voices.json())
    voices_list = voices_json['voices']
    for i in voices_list:
        if i['category'] == 'cloned':
            cloned_voices.append(i['voice_id'])
            
    return cloned_voices


def delete_voice(voice_id):
    """Delete a voice from ElevenLabs"""
    url = f"https://api.elevenlabs.io/v1/voices/{voice_id}"
    headers = {
        "xi-api-key": ELEVENLABS_API_KEY
    }
    response = requests.delete(url, headers=headers)
    return response.status_code == 200



@router.post("/regenerate_video/{campaign_id}/{script_id}")
async def regenerate_video(
    campaign_id: str,
    script_id: str,
    background_tasks: BackgroundTasks
):
    """Regenerate failed videos while keeping existing audio"""
    
    try:
        # 尝试获取锁
        if not await campaign_manager.acquire_lock(campaign_id, script_id):
            raise HTTPException(
                status_code=429,
                detail="Another video generation is in progress. Please try again later."
            )
        
        # 获取现有的 campaign 数据
        campaign_data = campaign_manager.load_campaign(campaign_id)
        
        # 验证脚本存在
        script = next((s for s in campaign_data["scripts"] if s["id"] == script_id), None)
        if not script:
            await campaign_manager.release_lock()  # 记得释放锁
            raise HTTPException(status_code=404, detail="Script not found")
        
        # 添加视频重新生成到后台任务
        background_tasks.add_task(
            process_video_regeneration,
            campaign_data,
            script_id
        )
        
        return {"status": "processing"}
        
    except Exception as e:
        # 出错时释放锁
        await campaign_manager.release_lock()  # 修复：添加 await
        logger.error(f"Failed to start video regeneration: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start video regeneration: {str(e)}"
        )

async def process_video_regeneration(campaign_data: dict, script_id: str):
    """Process video regeneration while keeping existing audio and other generation data"""
    generation_results = []  # Initialize at the start
    try:
        client = LumaAI(auth_token=LUMA_AUTH_TOKEN)
        
        # 获取脚本和现有生成状态
        script = next(s for s in campaign_data["scripts"] if s["id"] == script_id)
        current_generation = campaign_data.get("generations", {}).get(script_id, {})
        
        # 初始化生成结果，保留现有的生成信息
        for segment in script["scripts"]["segments"]:  
            # 查找现有状态中的信息
            current_segment = next(
                (s for s in current_generation.get("segments", []) 
                 if s["segment_id"] == segment["id"]), 
                {}
            )
            
            # 保留所有现有的生成信息
            generation_result = {
                "segment_id": segment["id"],
                "generation_id": None,
                "status": "pending",
                # 保留现有的生成信息
                "narrator_audio": current_segment.get("narrator_audio") or segment.get("narrator_audio"),  
                "background_music": current_segment.get("background_music"),
                "text_overlay": current_segment.get("text_overlay"),
                "video_settings": current_segment.get("video_settings")
            }
            
            generation_results.append(generation_result)
        
        # 更新初始状态
        campaign_manager.update_generation_status(
            campaign_data["id"], 
            script_id,
            {
                "status": "in_progress",
                "segments": generation_results,
                "started_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        )
        
        # 生成视频
        for segment in script["scripts"]["segments"]:  
            try:
                segment_index = next(
                    i for i, r in enumerate(generation_results) 
                    if r["segment_id"] == segment["id"]
                )
                
                try:
                    # 生成视频
                    result = await generate_video_for_segment(
                        client,
                        segment["script"],
                        segment["id"]
                    )
                    
                    # 更新片段状态
                    generation_results[segment_index].update({
                        "status": "completed",
                        "video_url": result if isinstance(result, str) else result.get("video_url"),
                        "completed_at": datetime.now().isoformat()
                    })
                    
                except Exception as e:
                    logger.error(f"Error regenerating video for segment {segment['id']}: {str(e)}")
                    # 更新错误状态
                    generation_results[segment_index].update({
                        "status": "error",
                        "error": str(e),
                        "failed_at": datetime.now().isoformat()
                    })
                
            except Exception as e:
                logger.error(f"Failed to process segment {segment['id']}: {str(e)}")
                continue
            
            # 更新生成状态
            campaign_manager.update_generation_status(
                campaign_data["id"],
                script_id,
                {
                    "status": "in_progress",
                    "segments": generation_results,
                    "updated_at": datetime.now().isoformat()
                }
            )
        
        # 更新最终状态
        has_errors = any(s["status"] == "error" for s in generation_results)
        all_completed = all(s["status"] == "completed" for s in generation_results)
        
        final_status = "completed" if all_completed else "error" if has_errors else "partial"
        
        campaign_manager.update_generation_status(
            campaign_data["id"],
            script_id,
            {
                "status": final_status,
                "segments": generation_results,
                "completed_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        )
            
    except Exception as e:
        logger.error(f"Error in video regeneration process: {str(e)}")
        campaign_manager.update_generation_status(
            campaign_data["id"],
            script_id,
            {
                "status": "error",
                "error": str(e),
                "segments": generation_results if 'generation_results' in locals() else [],
                "failed_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        )
    
    finally:
        await campaign_manager.release_lock()
