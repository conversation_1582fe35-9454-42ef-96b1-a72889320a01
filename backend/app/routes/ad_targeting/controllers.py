# app/routes/ad_targeting/controllers.py
from fastapi import APIRouter, HTTPException, Query, Body
from typing import List, Dict, Any
from ...config import settings
from ...helpers.rag_helpers import *
from app.prompts.prompts import PROMPTS
from app.models.targeting_models import TotalSummary
from contextlib import contextmanager
import logging
import time
import aiohttp

rag_base_url = settings.rag_base_url
rag_api_key = settings.rag_api_key

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@contextmanager
def timer(name: str):
    """Context manager for timing code blocks and logging progress"""
    start_time = time.time()
    logger.info(f"Starting {name}...")
    yield
    elapsed_time = time.time() - start_time
    logger.info(f"Finished {name} in {elapsed_time:.2f} seconds")

# Initialize OpenAI client
client = OpenAI(api_key=settings.openai_api_key)

@router.get("/hello")
def hello_ad_targeting():
    logger.info("Received request to /hello endpoint")
    response = {"message": "Hello from Ad Targeting!"}
    logger.info(f"Sending response: {response}")
    return response


@router.get("/test_prompt")
def get_prompts(audience: str = Query(..., description="Target audience for the prompts")):
    return get_section_prompts(audience)

@router.get("/create_session")
async def create_session():
    try:
        rag_base_url = settings.rag_base_url
        rag_api_key = settings.rag_api_key
        rag_chat_id = settings.rag_chat_id
        session_data = await create_new_session(rag_api_key, chat_id=rag_chat_id, ragflow_base_url=rag_base_url)
        return session_data
    except aiohttp.ClientConnectorError as e:
        logger.error(f"Connection error while creating session: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="Unable to connect to the RAG service. Please check if the service is running and accessible."
        )
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create session: {str(e)}"
        )


@router.delete("/delete_sessions")
def delete_sessions_route(request: List[str]):
    """
    Deletes sessions of a chat assistant by ID.
    
    Parameters:
    -----------
    request : List[str]
        
    Returns:
    --------
    dict
    """
    try:
        rag_base_url = settings.rag_base_url
        rag_api_key = settings.rag_api_key
        rag_chat_id = settings.rag_chat_id
        
        result = delete_sessions(
            api_key=rag_api_key,
            chat_id=rag_chat_id,
            session_ids=request.session_ids,
            ragflow_base_url=rag_base_url
        )
        
        return {
            "status": "success",
            "message": "Sessions deleted successfully",
            "data": result
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete sessions: {str(e)}"
        )




@router.post("/chat_completion")
def chat_completion(request: ChatCompletionRequest):
    """
    Route handler for chat completion requests
    """
    try:
        rag_base_url = settings.rag_base_url
        rag_api_key = settings.rag_api_key
        rag_chat_id = settings.rag_chat_id
        
        response = handle_chat_completion(
            api_key=rag_api_key,
            chat_id=rag_chat_id,
            question=request.question,
            stream=request.stream,
            ragflow_base_url=rag_base_url
        )
        
        if request.stream:
            # Return streaming response
            return response
        else:
            # Return complete response
            return {
                "status": "success",
                "data": response
            }
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
        
        

@router.get("/behavior_analysis")
async def get_behavior_analysis(audience: str = Query(..., description="Target audience to analyze")):
    """
    Get behavioral analysis results for the target audience with execution timing.
    """
    try:
        with timer("behavior analysis"):
            # 记录开始处理的时间
            start_time = time.time()
            
            # 使用 analyze_behavior 函数获取行为分析
            logger.info(f"Starting behavior analysis for audience: {audience}")
            behavior_sections = await analyze_behavior(audience, client)
            
            # 记录结束时间和总耗时
            execution_time = time.time() - start_time
            logger.info(f"Analysis completed in {execution_time:.2f} seconds")
            
            if not behavior_sections:
                raise HTTPException(
                    status_code=404,
                    detail="No behavior analysis results found for the specified audience"
                )
            
            return behavior_sections
            
    except Exception as e:
        logger.error(f"Error in behavior analysis endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze behavior patterns: {str(e)}"
        )


@router.get("/psychographic_analysis")
async def get_psychographic_analysis(audience: str = Query(..., description="Target audience to analyze")):
    """
    Get psychographic analysis results for the target audience with execution timing.
    """
    try:
        with timer("psychographic analysis"):
            # 记录开始处理的时间
            start_time = time.time()
            
            # 使用 analyze_psychographic 函数获取行为分析
            logger.info(f"Starting psychographic analysis for audience: {audience}")
            psychographic_sections = await analyze_psychographic(audience, client)
            
            # 记录结束时间和总耗时
            execution_time = time.time() - start_time
            logger.info(f"Analysis completed in {execution_time:.2f} seconds")
            
            if not psychographic_sections:
                raise HTTPException(
                    status_code=404,
                    detail="No psychographic analysis results found for the specified audience"
                )
            
            return psychographic_sections
            
    except Exception as e:
        logger.error(f"Error in psychographic analysis endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze psychographic patterns: {str(e)}"
        )


@router.get("/reviewer-link")
async def get_reviewer_link_route(reviewer_name: str = Query(..., description="Name of the reviewer to search for")):
    """
    Get the reviewer name and associated product review link.
    
    Args:
        reviewer_name (str): Name of the reviewer to search for
        
    Returns:
        dict: A dictionary containing reviewer name and link
    """
    try:
        reviewer, link = get_reviewer_link(reviewer_name)
        return {
            "reviewer_name": reviewer,
            "review_link": link
        }
    except Exception as e:
        logger.error(f"Error getting reviewer link: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting reviewer link: {str(e)}"
        )
        
        

@router.get("/complete-analysis")
async def get_complete_analysis(audience: str = Query(..., description="Target audience for complete analysis")):
    """
    Get complete audience analysis including all sections.
    
    Args:
        audience (str): Target audience to analyze
        
    Returns:
        dict: Complete analysis results including all sections
    """
    try:
        logger.info(f"Starting complete analysis for audience: {audience}")
        start_time = time.time()
        with timer("Complete audience analysis"):
            result = await get_complete_audience_analysis(audience, ANALYSIS_CONFIGS)
            execution_time = time.time() - start_time
            logger.info(f"Complete analysis finished in {execution_time:.2f} seconds")
            return result
            
    except Exception as e:
        logger.error(f"Error in complete analysis endpoint: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to complete audience analysis: {str(e)}"
        )
        


@router.post("/targeting-summary")
async def get_targeting_summary(targeting_results: Any = Body(...)):
    """
    Generate a summary of targeting results data.
    
    Args:
        targeting_results (Any): Any JSON data
        
    Returns:
        TotalSummary str: Generated summary of the targeting data
    """
    try:
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-2024-08-06",
            messages=[
                {"role": "system", "content": "Summarize the key targeting results, highlighting relevant demographics, interests, behaviors, and psychographics, within 100 words."},
                {"role": "user", "content": "Please summarize the following targeting results, focusing on the main demographic, interests,behavioral, and psychographic trends:\n\n{targetingResults}"}

            ],
            response_format=TotalSummary,
            )
        
        event = completion.choices[0].message.parsed
        logger.info(f"Generated targeting summary: {event.totalSummary}")
        
        return event.totalSummary
        
    except Exception as e:
        logger.error(f"Error in targeting summary endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate targeting summary: {str(e)}"
        )
