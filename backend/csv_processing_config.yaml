# Adtargeting_AI-1/csv_processing_config.yaml

csv_configurations:
  - name: "MainProductData" 
    file_pattern: "data*.csv" 

    parser_options:
      separator: ";"
      encoding: "utf-8" 
      has_header: true
      truncate_ragged_lines: true
      Polars specific options can be added here if needed:
      ignore_errors: true 
      infer_schema_length: 10000 
      dtypes: # Optional: specify dtypes to ensure correct parsing for this pattern
      Price: "Float64"
      Rating: "Float64"
      Reviews: "Int64" 
      number_bought_last_month: "Int64"
      review_rating: "Float64"
      helpful_count: "Int64"
    text_unification:
      columns_for_searchable_document:
        - "name"
        - "categories"
        - "brand"
        - "text"
      format_string: "Product: {name}. Brand: {brand}. Categories: {categories}. Description: {text}"
    metadata_extraction:
      original_row_identifier_column: "review_id" 
      columns_for_key_original_fields:
        - "search_query" 
        - "search_link"
        - "category"
        - "subcategory"
        - "Name"
        - "Price"
        - "Rating"
        - "Reviews"
        - "ItemURL"
        - "number_bought_last_month"
        - "customer_says"
        - "reviewer_name"
        - "review_title" 
        - "review_rating"
        - "review_country"
        - "review_date"
        - "review_body" 
        - "helpful_count"

  - name: "CustomerFeedbackGeneric" 
    file_pattern: "sourceB_data.csv" 

    parser_options:
      separator: "," 
      encoding: "utf-8"
      has_header: true
      dtypes: 
      HelpfulnessNumerator: "Int64"
      HelpfulnessDenominator: "Int64"
      Score: "Float64" 
      Time: "Int64" 
    text_unification:
      columns_for_searchable_document: 
        - "ProfileName"
        - "Summary"
        - "Text"
      format_string: "Feedback from {ProfileName}: {Summary}. Details: {Text}"
    metadata_extraction:
      original_row_identifier_column: "Id" 
      columns_for_key_original_fields: 
        - "ProductId"
        - "UserId"
        - "ProfileName" 
        - "HelpfulnessNumerator"
        - "HelpfulnessDenominator"
        - "Score"
        - "Time"
        - "Summary" 
        - "Text"    

  - name: "ProductData2"
    file_pattern: "data2.csv"
    parser_options:
      separator: ","
      encoding: "utf-8"
      has_header: true
      dtypes:
        prices: "Float64"
        reviews.rating: "Float64"
        dateAdded: "Int64"
        dateUpdated: "Int64"
    text_unification:
      columns_for_searchable_document:
        - "name"
        - "categories"
        - "brand"
        - "text"
      format_string: "Product: {name}. Brand: {brand}. Categories: {categories}. Description: {text}"
    metadata_extraction:
      original_row_identifier_column: "id"
      columns_for_key_original_fields:
        - "id"
        - "asins"
        - "brand"
        - "categories"
        - "colors"
        - "dateAdded"
        - "dateUpdated"
        - "dimension"
        - "ean"
        - "keys"
        - "manufacturer"
        - "manufacturerNumber"
        - "name"
        - "prices"
        - "reviews.date"
        - "reviews.doRecommend"
        - "reviews.numHelpful"
        - "reviews.rating"
        - "reviews.sourceURLs"
        - "reviews.text"
        - "reviews.title"
        - "reviews.userCity"
        - "reviews.userProvince"
        - "reviews.username"
        - "sizes"
        - "upc"
        - "weight"
    data_cleaning:
      date_columns:
        - "dateAdded"
        - "dateUpdated"
        - "reviews.date"
      list_columns:
        - "asins"
        - "categories"
        - "colors"
        - "sizes"
      numeric_columns:
        - "reviews.rating"
        - "prices"

  # --- Add more configurations below if I add more databases, remember to come look verify extraction formats ---
  # Example:
  # - name: "SupplierData"
  #   file_pattern: "supplier_info_*.csv" # Matches supplier_info_Q1.csv, supplier_info_europe.csv
  #   parser_options:
  #     separator: "\t" # Tab separated
  #     encoding: "latin1"
  #     has_header: true
  #   text_unification:
  #     columns_for_searchable_document:
  #       - "SupplierName"
  #       - "ProductRangeDescription"
  #       - "Notes"
  #   metadata_extraction:
  #     original_row_identifier_column: "SupplierID"
  #     columns_for_key_original_fields:
  #       - "SupplierID"
  #       - "ContactPerson"
  #       - "Email"
  #       - "Region"
  #       - "Tier"
