# AdTarget Backend

This is the backend application for the AdTarget project, built with FastAPI. It provides API endpoints to support ad targeting and video editing functionalities for the frontend Next.js application.

## Prerequisites

- Python 3.10 or higher
- pip
- Docker and Docker Compose (for containerized development)

## Project Structure

```
backend/
│
├── app/
│   │
│   ├── config.py             # Configuration settings
│   ├── main.py               # Entry point for the FastAPI app
│   ├── routes/               # API routes
│   │   ├── ad_targeting/
│   │   │   └── ad_targeting.py
│   │   └── video_editing/
│   │       └── video_editing.py
│   │
│   ├── helpers/              # Helper modules
│   │   ├── rag_helpers.py
│   │   └── video_helpers.py
│   │
│   ├── models/               # Database models
│   │   └── adTargeting.py
│   │
│   ├── prompts/              # Prompt templates
│       └── prompts.py
│
├── run.ps1                   # Powershell script for running the app
├── run.sh                    # Bash script for running the app on Unix systems
├── requirements.txt          # Python dependencies
├── Dockerfile                # Docker configuration
├── docker-compose.yml        # Docker Compose configuration
└── README.md                 # Project documentation
```

## Setup and Installation

### 1. Clone the Repository

```bash
git clone https://github.com/adsynthetica/adcreate.git
cd adcreate/end
```

### 2. Run the Application Locally with run.sh
For Unix systems, you can use the run.sh script to set up and run the application automatically. Ensure the script is executable:



```bash
chmod +x run.sh
./run.sh
```

The API will be accessible at http://localhost:8000.

### 3. Manual Setup (Alternative to run.sh)
If you prefer manual setup:

1. Create a Virtual Environment and Install Dependencies:

```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```
2. Run the Application:

```bash
uvicorn app.main:app --reload
```

### 4. Using Docker
To build and run the application with Docker Compose:

```bash
docker-compose up --build
```
The API will be accessible at http://localhost:8000.

## API Documentation
FastAPI automatically generates interactive API documentation. Once the app is running, you can access it at:

* Swagger UI: http://localhost:8000/docs
* ReDoc: http://localhost:8000/redoc






