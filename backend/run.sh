#!/bin/bash

# Create and activate virtual environment if not exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Run FastAPI app with Uvicorn
echo "Running FastAPI app..."
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Deactivate virtual environment after the app is stopped
deactivate
