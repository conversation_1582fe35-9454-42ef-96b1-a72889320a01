FROM python:3.10-slim

WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire app directory
COPY app/ ./app/

# Copy other necessary files
COPY .env .env
COPY csv_processing_config.yaml ./csv_processing_config.yaml

# Copy data directory if needed
COPY data/ ./data/

# Create necessary directories
RUN mkdir -p audio uploads exports

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]