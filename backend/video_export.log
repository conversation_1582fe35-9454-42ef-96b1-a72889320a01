2024-11-10 18:10:02,212 - app.routes.video_editing.controllers - ERROR - Error processing video: 404: Campaign not found
2024-11-10 18:10:02,213 - app.routes.video_editing.controllers - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 981, in save_export
    campaign_data = await get_campaign_data(campaignId)
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 964, in get_campaign_data
    raise HTTPException(status_code=404, detail="Campaign not found")
fastapi.exceptions.HTTPException: 404: Campaign not found

2024-11-10 18:14:30,604 - app.routes.video_editing.controllers - ERROR - Error processing video: ffmpeg error (see stderr output for detail)
2024-11-10 18:14:30,606 - app.routes.video_editing.controllers - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 996, in save_export
    ffmpeg.input(str(watermark_svg)).filter(
  File "/Users/<USER>/Desktop/adcreate/backend/venv/lib/python3.9/site-packages/ffmpeg/_run.py", line 325, in run
    raise Error('ffmpeg', out, err)
ffmpeg._run.Error: ffmpeg error (see stderr output for detail)

2024-11-10 18:16:31,808 - app.routes.video_editing.controllers - INFO - Processing segment 1
2024-11-10 18:16:31,809 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/2cc71597-c606-41b1-9d90-94c71972960f/1cc72ce4-21b1-4b50-b2a3-20aacefa7c58_video015256f7a91074582a22cb39cc4de7065.mp4
2024-11-10 18:16:32,811 - app.routes.video_editing.controllers - ERROR - Error processing video: local variable 'audio_url' referenced before assignment
2024-11-10 18:16:32,811 - app.routes.video_editing.controllers - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 1033, in save_export
    logger.info(f"Audio URL: {audio_url}")
UnboundLocalError: local variable 'audio_url' referenced before assignment

2024-11-10 18:18:06,569 - app.routes.video_editing.controllers - INFO - Processing segment 1
2024-11-10 18:18:06,570 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/2cc71597-c606-41b1-9d90-94c71972960f/1cc72ce4-21b1-4b50-b2a3-20aacefa7c58_video015256f7a91074582a22cb39cc4de7065.mp4
2024-11-10 18:18:06,570 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_1.mp3
2024-11-10 18:18:07,583 - app.routes.video_editing.controllers - ERROR - Error processing video: local variable 'stream' referenced before assignment
2024-11-10 18:18:07,583 - app.routes.video_editing.controllers - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 1048, in save_export
    stream,
UnboundLocalError: local variable 'stream' referenced before assignment

2024-11-10 18:24:22,131 - app.routes.video_editing.controllers - INFO - Processing segment 1
2024-11-10 18:24:22,131 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/2cc71597-c606-41b1-9d90-94c71972960f/1cc72ce4-21b1-4b50-b2a3-20aacefa7c58_video015256f7a91074582a22cb39cc4de7065.mp4
2024-11-10 18:24:22,131 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_1.mp3
2024-11-10 18:24:27,407 - app.routes.video_editing.controllers - INFO - Processing segment 2
2024-11-10 18:24:27,407 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/7b892ce5-dfa1-404a-9d12-38be0258f0db/b25dcf26-306d-400d-83b4-865277b37748_video069d8490416334f3fa1d27ba83a6aaba7.mp4
2024-11-10 18:24:27,407 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_2.mp3
2024-11-10 18:24:34,810 - app.routes.video_editing.controllers - INFO - Processing segment 3
2024-11-10 18:24:34,811 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/b5e12a82-24a4-44d8-9fd7-3ec82dbe4ed8/1ff07a1f-75a1-4fc9-a226-55a658f8705a_video03dc9c6f5d4be47b3a89ee2e4c6a0d7fa.mp4
2024-11-10 18:24:34,811 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_3.mp3
2024-11-10 18:24:36,887 - app.routes.video_editing.controllers - ERROR - Error processing video: ffmpeg error (see stderr output for detail)
2024-11-10 18:24:36,890 - app.routes.video_editing.controllers - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 1082, in save_export
    ffmpeg.output(
  File "/Users/<USER>/Desktop/adcreate/backend/venv/lib/python3.9/site-packages/ffmpeg/_run.py", line 325, in run
    raise Error('ffmpeg', out, err)
ffmpeg._run.Error: ffmpeg error (see stderr output for detail)

2024-11-10 18:26:19,763 - app.routes.video_editing.controllers - INFO - Processing segment 1
2024-11-10 18:26:19,763 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/2cc71597-c606-41b1-9d90-94c71972960f/1cc72ce4-21b1-4b50-b2a3-20aacefa7c58_video015256f7a91074582a22cb39cc4de7065.mp4
2024-11-10 18:26:19,763 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_1.mp3
2024-11-10 18:26:24,864 - app.routes.video_editing.controllers - INFO - Processing segment 2
2024-11-10 18:26:24,864 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/7b892ce5-dfa1-404a-9d12-38be0258f0db/b25dcf26-306d-400d-83b4-865277b37748_video069d8490416334f3fa1d27ba83a6aaba7.mp4
2024-11-10 18:26:24,864 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_2.mp3
2024-11-10 18:26:30,907 - app.routes.video_editing.controllers - INFO - Processing segment 3
2024-11-10 18:26:30,907 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/b5e12a82-24a4-44d8-9fd7-3ec82dbe4ed8/1ff07a1f-75a1-4fc9-a226-55a658f8705a_video03dc9c6f5d4be47b3a89ee2e4c6a0d7fa.mp4
2024-11-10 18:26:30,907 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_3.mp3
2024-11-10 18:26:32,827 - app.routes.video_editing.controllers - ERROR - Error processing video: ffmpeg error (see stderr output for detail)
2024-11-10 18:26:32,827 - app.routes.video_editing.controllers - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 1082, in save_export
    ffmpeg.output(
  File "/Users/<USER>/Desktop/adcreate/backend/venv/lib/python3.9/site-packages/ffmpeg/_run.py", line 325, in run
    raise Error('ffmpeg', out, err)
ffmpeg._run.Error: ffmpeg error (see stderr output for detail)

2024-11-10 18:29:49,154 - app.routes.video_editing.controllers - INFO - Processing segment 1
2024-11-10 18:29:49,154 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/2cc71597-c606-41b1-9d90-94c71972960f/1cc72ce4-21b1-4b50-b2a3-20aacefa7c58_video015256f7a91074582a22cb39cc4de7065.mp4
2024-11-10 18:29:49,154 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_1.mp3
2024-11-10 18:29:53,240 - app.routes.video_editing.controllers - INFO - Processing segment 2
2024-11-10 18:29:53,240 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/7b892ce5-dfa1-404a-9d12-38be0258f0db/b25dcf26-306d-400d-83b4-865277b37748_video069d8490416334f3fa1d27ba83a6aaba7.mp4
2024-11-10 18:29:53,240 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_2.mp3
2024-11-10 18:30:06,755 - app.routes.video_editing.controllers - INFO - Processing segment 3
2024-11-10 18:30:06,756 - app.routes.video_editing.controllers - INFO - Video URL: https://storage.cdn-luma.com/lit_lite_inference_v1.6-xl/b5e12a82-24a4-44d8-9fd7-3ec82dbe4ed8/1ff07a1f-75a1-4fc9-a226-55a658f8705a_video03dc9c6f5d4be47b3a89ee2e4c6a0d7fa.mp4
2024-11-10 18:30:06,756 - app.routes.video_editing.controllers - INFO - Audio URL: http://127.0.0.1:8000/audio/9533c84b-a86b-4100-97a3-eea0bc299780/8327190a-b707-4a3d-b1e1-0a1d6a867bf4/alloy/narrator_3.mp3
2024-11-10 18:30:07,594 - app.routes.video_editing.controllers - INFO - Final FFmpeg command: ffmpeg -f concat -safe 0 -i temp/9533c84b-a86b-4100-97a3-eea0bc299780/concat.txt exports/9533c84b-a86b-4100-97a3-eea0bc299780/export_8327190a-b707-4a3d-b1e1-0a1d6a867bf4_20241110_182942.mp4
2024-11-10 18:30:07,670 - app.routes.video_editing.controllers - ERROR - FFmpeg error during final merge:
2024-11-10 18:30:07,671 - app.routes.video_editing.controllers - ERROR - stdout: None
2024-11-10 18:30:07,671 - app.routes.video_editing.controllers - ERROR - stderr: ffmpeg version 7.1 Copyright (c) 2000-2024 the FFmpeg developers
  built with Apple clang version 16.0.0 (clang-1600.0.26.4)
  configuration: --prefix=/opt/homebrew/Cellar/ffmpeg/7.1_3 --enable-shared --enable-pthreads --enable-version3 --cc=clang --host-cflags= --host-ldflags='-Wl,-ld_classic' --enable-ffplay --enable-gnutls --enable-gpl --enable-libaom --enable-libaribb24 --enable-libbluray --enable-libdav1d --enable-libharfbuzz --enable-libjxl --enable-libmp3lame --enable-libopus --enable-librav1e --enable-librist --enable-librubberband --enable-libsnappy --enable-libsrt --enable-libssh --enable-libsvtav1 --enable-libtesseract --enable-libtheora --enable-libvidstab --enable-libvmaf --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libxvid --enable-lzma --enable-libfontconfig --enable-libfreetype --enable-frei0r --enable-libass --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-libspeex --enable-libsoxr --enable-libzmq --enable-libzimg --disable-libjack --disable-indev=jack --enable-videotoolbox --enable-audiotoolbox --enable-neon
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.100 / 61. 19.100
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
[concat @ 0x1369054e0] Impossible to open 'temp/9533c84b-a86b-4100-97a3-eea0bc299780/temp/9533c84b-a86b-4100-97a3-eea0bc299780/merged_1.mp4'
[in#0 @ 0x1369044b0] Error opening input: No such file or directory
Error opening input file temp/9533c84b-a86b-4100-97a3-eea0bc299780/concat.txt.
Error opening input files: No such file or directory

2024-11-10 18:30:08,675 - app.routes.video_editing.controllers - ERROR - Error processing video: 500: Failed to merge video segments
2024-11-10 18:30:08,677 - app.routes.video_editing.controllers - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 1098, in save_export
    stdout, stderr = final_output.overwrite_output().run(
  File "/Users/<USER>/Desktop/adcreate/backend/venv/lib/python3.9/site-packages/ffmpeg/_run.py", line 325, in run
    raise Error('ffmpeg', out, err)
ffmpeg._run.Error: ffmpeg error (see stderr output for detail)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/adcreate/backend/app/routes/video_editing/controllers.py", line 1109, in save_export
    raise HTTPException(
fastapi.exceptions.HTTPException: 500: Failed to merge video segments

2024-11-10 18:36:19,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-10 18:36:19,361 - app.routes.video_editing.controllers - ERROR - Failed to generate ad title: object ChatCompletion can't be used in 'await' expression
2024-11-10 18:36:36,534 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-10 18:36:38,519 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-10 18:36:39,122 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:36:43,046 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:36:46,641 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:36:50,260 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:36:53,858 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:36:57,494 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:37:00,837 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:37:04,211 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:37:07,596 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:37:10,959 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:37:14,339 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:37:17,678 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/8948e54c-6385-4d77-921b-7713efd55070 "HTTP/1.1 200 OK"
2024-11-10 18:37:18,891 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-10 18:37:19,228 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:22,829 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:26,466 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:29,807 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:33,410 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:36,750 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:40,355 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:43,695 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:47,049 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:50,398 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:53,742 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:57,185 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/308f06d5-7d7c-4cf2-b80d-e393fa2385e6 "HTTP/1.1 200 OK"
2024-11-10 18:37:58,355 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-10 18:37:58,690 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:02,122 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:05,481 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:08,827 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:12,469 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:15,840 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:19,175 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:22,575 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:25,915 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:29,522 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:32,861 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:36,462 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9a7dc7ac-d6bc-4b1b-ad29-e2311318fcfe "HTTP/1.1 200 OK"
2024-11-10 18:38:38,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-10 18:38:39,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-10 18:38:41,040 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-10 18:42:06,129 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-10 18:42:07,859 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-10 18:42:09,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 15:47:38,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 15:47:42,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 15:47:47,266 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 17:55:19,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 17:55:19,771 - app.routes.video_editing.controllers - ERROR - Failed to generate ad title: object ChatCompletion can't be used in 'await' expression
2024-11-11 17:55:33,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 17:55:36,071 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-11 17:55:36,440 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:55:39,778 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:55:43,143 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:55:47,380 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:55:50,790 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:55:54,686 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:55:58,052 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:56:01,665 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:56:05,015 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:56:08,432 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:56:11,829 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:56:15,284 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/d2d97900-ddb9-4f1c-9659-b80ab55d5637 "HTTP/1.1 200 OK"
2024-11-11 17:56:17,137 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-11 17:56:17,523 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:21,183 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:24,799 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:28,239 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:31,618 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:35,287 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:38,638 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:43,236 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:46,587 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:50,285 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:54,768 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:58,188 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/1c31ea10-3df2-4f7f-a0c7-93cfce755f6e "HTTP/1.1 200 OK"
2024-11-11 17:56:59,337 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-11 17:56:59,749 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:03,410 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:06,756 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:10,510 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:13,856 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:17,486 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:20,838 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:25,963 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:29,808 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:33,925 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:37,413 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:40,813 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/9267ea11-fba3-4451-8516-5ce9549720ac "HTTP/1.1 200 OK"
2024-11-11 17:57:43,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 17:57:45,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 17:57:47,049 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 17:58:15,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 17:58:18,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 17:58:20,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 18:19:02,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 18:19:05,132 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-11 18:19:05,445 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:08,854 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:12,194 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:15,514 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:19,057 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:22,381 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:25,712 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:30,010 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:33,328 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:36,642 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:40,250 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:43,629 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f9933e0a-0d75-4c49-807b-3f565ee21b72 "HTTP/1.1 200 OK"
2024-11-11 18:19:45,199 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-11 18:19:45,509 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:19:48,832 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:19:53,050 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:19:56,373 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:19:59,924 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:03,267 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:07,027 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:10,347 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:13,659 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:16,988 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:20,525 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:24,705 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/c110ec94-af0a-4fd6-99f7-473c3f40fd2c "HTTP/1.1 200 OK"
2024-11-11 18:20:25,702 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-11 18:20:26,241 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:29,563 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:32,881 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:36,275 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:39,591 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:43,172 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:46,484 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:49,797 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:53,120 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:56,443 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:20:59,759 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:21:03,517 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:21:06,827 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/f01f6257-8661-44b6-b298-0be9df2e80dc "HTTP/1.1 200 OK"
2024-11-11 18:21:08,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 18:21:11,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 18:21:14,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 19:06:21,648 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 19:06:21,652 - app.routes.video_editing.controllers - ERROR - Failed to generate ad title: object ChatCompletion can't be used in 'await' expression
2024-11-11 19:06:25,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 19:06:28,096 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-11 19:06:29,067 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/5e02dbbc-d3cc-4714-9d4d-3d47e1705e7c "HTTP/1.1 200 OK"
2024-11-11 19:08:03,511 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 19:08:03,739 - app.routes.video_editing.controllers - ERROR - Failed to generate ad title: object ChatCompletion can't be used in 'await' expression
2024-11-11 19:08:08,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 19:08:11,632 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 400 Bad Request"
2024-11-11 19:08:11,633 - app.routes.video_editing.controllers - ERROR - Error generating video for segment 1: local variable 'generation' referenced before assignment
2024-11-11 19:08:12,909 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 400 Bad Request"
2024-11-11 19:08:12,910 - app.routes.video_editing.controllers - ERROR - Error generating video for segment 2: local variable 'generation' referenced before assignment
2024-11-11 19:08:13,566 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 400 Bad Request"
2024-11-11 19:08:13,567 - app.routes.video_editing.controllers - ERROR - Error generating video for segment 3: local variable 'generation' referenced before assignment
2024-11-11 19:08:16,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 19:08:18,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 19:08:20,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 20:02:13,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 20:02:13,980 - app.routes.video_editing.controllers - ERROR - Failed to generate ad title: object ChatCompletion can't be used in 'await' expression
2024-11-11 20:02:22,387 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-11 20:02:26,562 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 400 Bad Request"
2024-11-11 20:02:26,563 - app.routes.video_editing.controllers - ERROR - Error generating video for segment 1: local variable 'generation' referenced before assignment
2024-11-11 20:02:27,287 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 400 Bad Request"
2024-11-11 20:02:28,204 - app.routes.video_editing.controllers - ERROR - Error generating video for segment 2: local variable 'generation' referenced before assignment
2024-11-11 20:02:28,933 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 400 Bad Request"
2024-11-11 20:02:28,933 - app.routes.video_editing.controllers - ERROR - Error generating video for segment 3: local variable 'generation' referenced before assignment
2024-11-11 20:02:30,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 20:02:35,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-11 20:02:37,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-12 11:29:03,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-12 11:29:05,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-12 11:29:08,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-15 17:50:48,924 - app.routes.video_editing.controllers - ERROR - Failed to send feedback email: [Errno 60] Operation timed out
2024-11-15 17:53:42,420 - app.routes.video_editing.controllers - INFO - Feedback email sent <NAME_EMAIL>
2024-11-15 17:59:31,394 - app.routes.video_editing.controllers - INFO - Feedback email sent <NAME_EMAIL>
2024-11-15 23:00:44,513 - openai._base_client - INFO - Retrying request to /audio/speech in 0.380126 seconds
2024-11-15 23:00:49,903 - openai._base_client - INFO - Retrying request to /audio/speech in 0.896403 seconds
2024-11-15 23:00:55,807 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: Request timed out.
2024-11-15 23:00:55,808 - app.routes.video_editing.controllers - ERROR - Failed to update voice: Request timed out.
2024-11-15 23:01:00,858 - openai._base_client - INFO - Retrying request to /audio/speech in 0.445338 seconds
2024-11-15 23:01:06,313 - openai._base_client - INFO - Retrying request to /audio/speech in 0.850202 seconds
2024-11-15 23:01:12,171 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: Request timed out.
2024-11-15 23:01:12,172 - app.routes.video_editing.controllers - ERROR - Failed to update voice: Request timed out.
2024-11-15 23:01:17,211 - openai._base_client - INFO - Retrying request to /audio/speech in 0.389957 seconds
2024-11-15 23:01:20,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-15 23:01:22,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-15 23:01:24,630 - openai._base_client - INFO - Retrying request to /audio/speech in 0.429328 seconds
2024-11-15 23:01:29,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-15 23:01:31,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-15 23:37:22,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 403 Forbidden"
2024-11-15 23:37:22,112 - app.routes.video_editing.controllers - ERROR - Failed to generate ad title: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-15 23:37:23,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 403 Forbidden"
2024-11-15 23:37:23,452 - app.routes.video_editing.controllers - ERROR - Failed to create video campaign: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-15 23:38:31,302 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-15 23:38:31,305 - app.routes.video_editing.controllers - ERROR - Failed to generate ad title: object ChatCompletion can't be used in 'await' expression
2024-11-15 23:38:35,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2024-11-15 23:38:40,732 - httpx - INFO - HTTP Request: POST https://api.lumalabs.ai/dream-machine/v1/generations "HTTP/1.1 201 Created"
2024-11-15 23:38:41,167 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:38:45,370 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:38:49,235 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:38:52,692 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:38:58,097 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:39:01,524 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:39:05,224 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:39:08,573 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:39:12,304 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:39:16,103 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:39:19,992 - httpx - INFO - HTTP Request: GET https://api.lumalabs.ai/dream-machine/v1/generations/b2009954-7bcf-4a79-a4fa-4664f08d3112 "HTTP/1.1 200 OK"
2024-11-15 23:39:23,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-15 23:40:24,403 - openai._base_client - INFO - Retrying request to /audio/speech in 0.442801 seconds
2024-11-15 23:40:29,855 - openai._base_client - INFO - Retrying request to /audio/speech in 0.916027 seconds
2024-11-15 23:40:35,776 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: Request timed out.
2024-11-15 23:40:35,776 - app.routes.video_editing.controllers - ERROR - Failed to update voice: Request timed out.
2024-11-15 23:41:07,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-17 21:05:08,190 - app.routes.video_editing.controllers - ERROR - Error uploading audio file: 'async for' requires an object with __aiter__ method, got SpooledTemporaryFile
2024-11-17 22:28:26,719 - app.routes.video_editing.controllers - ERROR - HTTP error in upload_audio_file: Invalid file type. Allowed types: .m4a, .mp3, .wav
2024-11-17 22:29:11,820 - app.routes.video_editing.controllers - ERROR - HTTP error in upload_audio_file: Invalid file type. Allowed types: .m4a, .mp3, .wav
2024-11-17 22:38:33,406 - app.routes.video_editing.controllers - ERROR - Error uploading audio file: name 'clone_voice' is not defined
2024-11-18 14:08:44,624 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: name 'ElevenLabsAPI' is not defined
2024-11-18 14:08:44,625 - app.routes.video_editing.controllers - ERROR - Failed to update voice: name 'ElevenLabsAPI' is not defined
2024-11-18 14:21:52,546 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: name 'ElevenLabsAPI' is not defined
2024-11-18 14:21:52,546 - app.routes.video_editing.controllers - ERROR - Failed to update voice: name 'ElevenLabsAPI' is not defined
2024-11-18 14:34:05,551 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: local variable 'client' referenced before assignment
2024-11-18 14:34:05,553 - app.routes.video_editing.controllers - ERROR - Failed to update voice: local variable 'client' referenced before assignment
2024-11-18 14:35:49,228 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: name 'VoiceSettings' is not defined
2024-11-18 14:35:49,228 - app.routes.video_editing.controllers - ERROR - Failed to update voice: name 'VoiceSettings' is not defined
2024-11-18 14:45:08,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 403 Forbidden"
2024-11-18 14:45:08,551 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-18 14:45:08,551 - app.routes.video_editing.controllers - ERROR - Failed to update voice: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-18 14:45:09,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 403 Forbidden"
2024-11-18 14:45:09,768 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-18 14:45:09,769 - app.routes.video_editing.controllers - ERROR - Failed to update voice: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-18 14:54:05,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 403 Forbidden"
2024-11-18 14:54:05,643 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-18 14:54:05,644 - app.routes.video_editing.controllers - ERROR - Failed to update voice: Error code: 403 - {'error': {'code': 'unsupported_country_region_territory', 'message': 'Country, region, or territory not supported', 'param': None, 'type': 'request_forbidden'}}
2024-11-18 14:54:33,563 - openai._base_client - INFO - Retrying request to /audio/speech in 0.402404 seconds
2024-11-18 14:54:38,978 - openai._base_client - INFO - Retrying request to /audio/speech in 0.817842 seconds
2024-11-18 14:54:44,804 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: Request timed out.
2024-11-18 14:54:44,804 - app.routes.video_editing.controllers - ERROR - Failed to update voice: Request timed out.
2024-11-18 14:59:43,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-18 15:05:08,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-18 15:55:03,311 - app.routes.video_editing.controllers - ERROR - Failed to update voice: 'list' object has no attribute 'get'
2024-11-18 15:55:03,315 - app.routes.video_editing.controllers - ERROR - Failed to update voice: 'list' object has no attribute 'get'
2024-11-18 15:55:12,666 - app.routes.video_editing.controllers - ERROR - Failed to update voice: 'list' object has no attribute 'get'
2024-11-18 15:56:20,838 - app.routes.video_editing.controllers - ERROR - Failed to update voice: 'list' object has no attribute 'get'
2024-11-18 15:57:54,239 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/7QtySukumgpaxOrvReEj?optimize_streaming_latency=0&output_format=mp3_22050_32 "HTTP/1.1 200 OK"
2024-11-18 16:04:44,664 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:04:44,664 - app.routes.video_editing.controllers - ERROR - Failed to update voice: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:04:50,773 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/7QtySukumgpaxOrvReEj?optimize_streaming_latency=0&output_format=mp3_22050_32 "HTTP/1.1 200 OK"
2024-11-18 16:04:58,346 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:04:58,346 - app.routes.video_editing.controllers - ERROR - Failed to update voice: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:06:13,467 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-18 16:06:20,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-18 16:13:08,622 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:13:08,622 - app.routes.video_editing.controllers - ERROR - Failed to update voice: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:13:15,505 - app.routes.video_editing.controllers - ERROR - Failed to generate voice audio: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:13:15,505 - app.routes.video_editing.controllers - ERROR - Failed to update voice: name 'VOICE_ID_MAPPING' is not defined
2024-11-18 16:13:18,518 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/7QtySukumgpaxOrvReEj?optimize_streaming_latency=0&output_format=mp3_22050_32 "HTTP/1.1 200 OK"
2024-11-18 16:14:18,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
2024-11-18 16:14:28,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/audio/speech "HTTP/1.1 200 OK"
