# Stripe Integration Documentation

This document details the Stripe integration in the Next.js SaaS Starter project.

## Stripe Integration Overview

The Stripe integration in this Next.js SaaS Starter project is primarily used for handling subscription payments and includes the following core components:

### 1. Stripe Configuration and Initialization

In the `/lib/payments/stripe.ts` file, the project initializes the Stripe client:

```typescript
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-01-27.acacia'
});
```

### 2. Subscription Process Implementation

#### Creating Checkout Session
When a user selects a subscription plan, the system creates a Stripe checkout session:

```typescript
export async function createCheckoutSession({
  team,
  priceId
}: {
  team: Team | null;
  priceId: string;
}) {
  // Create Stripe checkout session
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [{ price: priceId, quantity: 1 }],
    mode: 'subscription',
    success_url: `${process.env.BASE_URL}/api/stripe/checkout?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.BASE_URL}/pricing`,
    customer: team.stripeCustomerId || undefined,
    client_reference_id: user.id.toString(),
    allow_promotion_codes: true,
    subscription_data: {
      trial_period_days: 14
    }
  });
}
```

#### Customer Portal Management
Users can manage their subscriptions through the Stripe Customer Portal:

```typescript
export async function createCustomerPortalSession(team: Team) {
  // Create customer portal session to allow users to manage subscriptions
  return stripe.billingPortal.sessions.create({
    customer: team.stripeCustomerId,
    return_url: `${process.env.BASE_URL}/dashboard`,
    configuration: configuration.id
  });
}
```

### 3. Subscription Status Management

When subscription status changes, the system updates the team's subscription information:

```typescript
export async function handleSubscriptionChange(
  subscription: Stripe.Subscription
) {
  // Handle subscription status changes
  if (status === 'active' || status === 'trialing') {
    // Update to active subscription
    await updateTeamSubscription(team.id, {
      stripeSubscriptionId: subscriptionId,
      stripeProductId: plan?.product as string,
      planName: (plan?.product as Stripe.Product).name,
      subscriptionStatus: status
    });
  } else if (status === 'canceled' || status === 'unpaid') {
    // Cancel subscription
    await updateTeamSubscription(team.id, {
      stripeSubscriptionId: null,
      stripeProductId: null,
      planName: null,
      subscriptionStatus: status
    });
  }
}
```

### 4. Webhook Handling

In `/app/api/stripe/webhook/route.ts`, the system handles webhook events from Stripe:

```typescript
export async function POST(request: NextRequest) {
  // Verify webhook signature
  event = stripe.webhooks.constructEvent(payload, signature, webhookSecret);
  
  // Handle different types of events
  switch (event.type) {
    case 'customer.subscription.updated':
    case 'customer.subscription.deleted':
      const subscription = event.data.object as Stripe.Subscription;
      await handleSubscriptionChange(subscription);
      break;
  }
}
```

### 5. Checkout Success Handling

In `/app/api/stripe/checkout/route.ts`, the system handles the logic after successful checkout:

```typescript
export async function GET(request: NextRequest) {
  // Get session ID
  const sessionId = searchParams.get('session_id');
  
  // Get session information
  const session = await stripe.checkout.sessions.retrieve(sessionId, {
    expand: ['customer', 'subscription'],
  });
  
  // Update team subscription information in database
  await db
    .update(teams)
    .set({
      stripeCustomerId: customerId,
      stripeSubscriptionId: subscriptionId,
      stripeProductId: productId,
      planName: (plan.product as Stripe.Product).name,
      subscriptionStatus: subscription.status,
      updatedAt: new Date(),
    })
    .where(eq(teams.id, userTeam[0].teamId));
}
```

### 6. Frontend Subscription Management

On the frontend, the system provides subscription management interfaces:

- Subscription plans are displayed in `/app/(dashboard)/pricing/page.tsx`
- Current subscription status and management options are shown in `/app/(dashboard)/dashboard/settings.tsx`

```typescript
<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
  <div className="mb-4 sm:mb-0">
    <p className="font-medium">
      Current Plan: {teamData.planName || 'Free'}
    </p>
    <p className="text-sm text-muted-foreground">
      {teamData.subscriptionStatus === 'active'
        ? 'Billed monthly'
        : teamData.subscriptionStatus === 'trialing'
          ? 'Trial period'
          : 'No active subscription'}
    </p>
  </div>
  <form action={customerPortalAction}>
    <Button type="submit" variant="outline">
      Manage Subscription
    </Button>
  </form>
</div>
```

### 7. Database Storage

Subscription information is stored in the `teams` table with the following fields:
- `stripeCustomerId`: Stripe customer ID
- `stripeSubscriptionId`: Stripe subscription ID
- `stripeProductId`: Stripe product ID
- `planName`: Subscription plan name
- `subscriptionStatus`: Subscription status (active, trialing, etc.)

## Summary

The Stripe integration in this project is a complete subscription payment solution that includes:

1. Creating and managing subscriptions
2. Handling payment processes
3. Providing customer portal for subscription management
4. Processing subscription status changes through webhooks
5. Displaying and managing subscription status in the frontend

The entire integration process is well-designed, covering all aspects of SaaS application subscription management, including trial periods, subscription status tracking, and customer portal functionality.
