'use client';

import { useState, useRef, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { PlayerRef } from '@remotion/player';
import { ToolSideBar } from './ToolSideBar/ToolSideBar';
import { ToolType } from '../../models/editorType';
import Preview from './Preview/Preview';
import Control, { ControlRef } from './Control/Control';
import { useVideoEditor } from '@/lib/hooks/useVideoEditor';
import { useVideoPlayerStore } from '@/lib/store/videoPlayerStore';
import { useTextOverlayStore } from '@/lib/store/textOverlayStore';
import { useVideoProjectStore } from '@/lib/store/videoProjectStore';
import { useMusicStore } from '@/lib/store/musicStore';

// Module-level Map to track which projects have already had text overlays created
// This persists across component re-mounts when Next.js renders components twice in development mode
const processedTextOverlayProjectsMap = new Map<string, boolean>();

// Constants for polling configuration
const POLLING_INTERVAL_MS = 5000; // Poll every 5 seconds
const MAX_POLLING_DURATION_MS = 180000; // Max polling time: 3 minutes

/**
 * VideoEditor component: handles video editing functionality
 * 
 * This component has been refactored to use the useVideoEditor hook
 * instead of directly accessing multiple stores, simplifying the code
 * and improving maintainability.
 */
const VideoEditor = () => {
  // Get project ID from search parameters
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');
  
  // Use the project store to manage project details
  const { currentProject, fetchProject, updateProject } = useVideoProjectStore();
  
  // Use the video editor hook to handle store coordination
  const {
    currentScriptId,
    currentSegmentId,
    currentVersionSegments,
    isGeneratingSegments,
    isSegmentPolling,
    handleScriptSelect,
    handleSegmentSelect,
    fetchAllCurrentVersionSegments,
    createTestSegment,
    generateAllSegments,
    scripts,
    isInitialized,
  } = useVideoEditor(projectId);
  
  // Get text overlay store methods for automatic creation
  const { 
    textOverlays, 
    addTextOverlay, 
    projectId: textOverlayProjectId,
    setProjectId: setTextOverlayProjectId,
    saveToServer 
  } = useTextOverlayStore();
  
  // Get music store methods and state
  const { 
    fetchAudioResources, 
    isLoading: isMusicLoading 
  } = useMusicStore();
  
  // State to track if music resources have been loaded
  const [musicLoaded, setMusicLoaded] = useState(false);
  
  // State to track if voice audio has been auto-generated
  const [voicesGenerated, setVoicesGenerated] = useState(false);
  
  // State to track if text overlays have been auto-created
  const [textOverlaysCreated, setTextOverlaysCreated] = useState(false);
  
  // State to track if video segments have been auto-generated
  const [segmentsGenerated, setSegmentsGenerated] = useState(false);
  
  // Add a state to track when all generation processes are complete
  const [allGenerationComplete, setAllGenerationComplete] = useState(false);
  
  // NOTE: Removed polling state variables as they're no longer needed
  // Polling is now handled exclusively by videoSegmentStore
  
  // Initialize player reference and active tool state
  // const playerRef = useRef<PlayerRef>(null); // Unused variable
  const [activeTool, setActiveTool] = useState<ToolType>(null);
  
  // Get selected voice from store instead of using local state
  const { selectedVoice, setSelectedVoice } = useVideoPlayerStore();

  // State to track timeline collapse state
  const [isTimelineCollapsed, setIsTimelineCollapsed] = useState(true);
  
  // Toggle timeline visibility
  const handleToggleTimeline = () => {
    setIsTimelineCollapsed(!isTimelineCollapsed);
  };
  
  // Handle voice change from Control component
  const handleVoiceChange = (voice: 'blue' | 'pink') => {
    setSelectedVoice(voice);
  };
  
  // Wrapper function that returns void to satisfy Control component's prop type
  const handleCreateSegment = async () => {
    await createTestSegment();
  };
  
  // Handle project name change
  const handleProjectNameChange = async (name: string) => {
    if (projectId && name.trim() !== '') {
      try {
        // Update project title in the store/database
        // This is only called when editing is complete (on blur or Enter key press)
        // The Preview component handles intermediate states locally
        await updateProject(projectId, { title: name });
        console.log(`Project name updated to: ${name}`);
      } catch (error) {
        console.error('Failed to update project name:', error);
      }
    }
  };
  
  // Fetch project details when component mounts
  useEffect(() => {
    if (projectId) {
      fetchProject(projectId);
    }
  }, [projectId, fetchProject]);
  
  // Reference to Control component methods
  const controlRef = useRef<ControlRef>(null);
  
  // Function to create text overlays from scripts
  const createTextOverlaysFromScripts = async () => {
    // Skip if no projectId or no scripts
    if (!projectId || !scripts || scripts.length === 0) {
      console.log('[VideoEditor] Cannot create text overlays: missing projectId or scripts');
      return;
    }
    
    // Check if we've already processed this project at module level
    if (processedTextOverlayProjectsMap.has(projectId)) {
      console.log(`[VideoEditor] Skipping text overlay creation for project ${projectId}: already processed`);
      return;
    }
    
    // Mark this project as processed immediately to prevent duplicate creation
    console.log(`[VideoEditor] Marking project ${projectId} as processed for text overlays BEFORE creation`);
    processedTextOverlayProjectsMap.set(projectId, true);
    
    // Ensure text overlay store has the correct projectId
    if (textOverlayProjectId !== projectId) {
      console.log(`[VideoEditor] Setting text overlay projectId to ${projectId}`);
      setTextOverlayProjectId(projectId);
    }
    
    // If we already have text overlays, don't create new ones
    if (textOverlays.length > 0) {
      console.log(`[VideoEditor] Text overlays already exist (${textOverlays.length}), skipping creation`);
      setTextOverlaysCreated(true);
      return;
    }
    
    try {
      console.log(`[VideoEditor] Creating text overlays for project ${projectId} with ${scripts.length} scripts`);
      
      // Sort scripts by segment number to ensure correct order
      const sortedScripts = [...scripts].sort((a, b) => a.segmentNumber - b.segmentNumber);
      let overlaysCreated = 0;
      
      // Create a text overlay for each script
      sortedScripts.forEach((script, index) => {
        // Determine text content (use phrase or fallback to segment number)
        const textContent = script.phrase || `Segment ${script.segmentNumber}`;
        
        // Calculate timing:
        // First text starts at 2s with 3s duration
        // Subsequent texts start 2s after the previous one ends
        const duration = 3; // Duration for all text overlays
        
        // First text: starts at 2s
        // Second text: starts at 2 + 3 + 2 = 7s (first start + first duration + gap)
        // Third text: starts at 7 + 3 + 2 = 12s
        // Formula: 2 + (duration + 2) * index
        const startTime = index === 0 ? 2 : 2 + (index * (duration + 2));
        
        console.log(`[VideoEditor] Creating text overlay for script ${script.id} (Segment ${script.segmentNumber}): "${textContent}" at ${startTime}s`);
        
        // Add new text overlay
        addTextOverlay({
          text: textContent,
          fontSize: 40,
          color: '#ffffff',
          position: { x: 0.05, y: 0.9 }, // Position at bottom left
          alignment: 'left',
          startTime: startTime,
          duration: duration
        });
        
        overlaysCreated++;
      });
      
      // Save created overlays to the server
      if (overlaysCreated > 0) {
        console.log(`[VideoEditor] Saving ${overlaysCreated} new text overlays to server for project ${projectId}`);
        await saveToServer();
        console.log('[VideoEditor] Text overlays saved successfully');
      }
      
      setTextOverlaysCreated(true);
    } catch (error) {
      console.error('[VideoEditor] Error creating text overlays:', error);
    }
  };
  
  // Auto-create text overlays when scripts are loaded
  useEffect(() => {
    // Only run if:
    // 1. Editor is initialized
    // 2. We have scripts
    // 3. Text overlays haven't been created yet
    if (isInitialized && scripts && scripts.length > 0 && !textOverlaysCreated) {
      console.log('[VideoEditor] Auto-creating text overlays for scripts...');
      
      // Create text overlays immediately when conditions are met
        createTextOverlaysFromScripts();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, scripts, textOverlaysCreated, projectId, textOverlays.length]);
  
  // Auto-generate voice audio when editor is initialized and scripts are available
  useEffect(() => {
    // Only run if:
    // 1. Editor is initialized
    // 2. We have scripts
    // 3. Control component is mounted
    if (isInitialized && scripts && scripts.length > 0 && controlRef.current) {
      // Check if we've already generated voices for this project
      const projectKey = `voice_generation_${projectId}`;
      const voiceGenerationStatus = localStorage.getItem(projectKey);
      const currentTime = new Date().getTime();
      
      // If we have a stored status, check its timestamp and count
      if (voiceGenerationStatus) {
        try {
          const status = JSON.parse(voiceGenerationStatus);
          const timestamp = status.timestamp || 0;
          const scriptCount = status.scriptCount || 0;
          const hoursSinceLastGeneration = (currentTime - timestamp) / (1000 * 60 * 60);
          
          // Skip generation if:
          // 1. Less than 24 hours have passed since the last generation AND
          // 2. We have the same number of scripts or fewer (suggesting no new scripts have been added)
          if (hoursSinceLastGeneration < 24 && scriptCount >= scripts.length) {
            console.log(`Auto voice generation skipped - last generated ${hoursSinceLastGeneration.toFixed(2)} hours ago for ${scriptCount} scripts`);
            setVoicesGenerated(true);
            return;
          }
        } catch (error) {
          console.error('Error parsing voice generation status:', error);
        }
      }
      
      console.log('Auto-generating male and female voice audio for all scripts...');
      
      // Generate voice audio immediately when conditions are met
      const generateVoices = async () => {
        try {
          // Generate both male and female audio for all scripts
          await controlRef.current?.generateAllBothGendersAudio();
          setVoicesGenerated(true);
          
          // Save the generation status to localStorage
          localStorage.setItem(projectKey, JSON.stringify({
            timestamp: currentTime,
            scriptCount: scripts.length
          }));
          
          console.log('Successfully auto-generated voice audio for all scripts');
        } catch (error) {
          console.error('Error auto-generating voice audio:', error);
        }
      };
      
      generateVoices();
    }
  }, [isInitialized, scripts, voicesGenerated, projectId]);
  
  // Load all segments when the editor is initialized
  useEffect(() => {
    if (isInitialized && scripts && scripts.length > 0) {
      console.log('auto load all segments...');
      
      // Load segments immediately when conditions are met
      const loadSegments = async () => {
        try {
          await fetchAllCurrentVersionSegments();
          
          // select the first script and its corresponding video segment
          if (scripts.length > 0 && !currentScriptId) {
            const firstScript = scripts[0];
            handleScriptSelect(firstScript.id);
          }
        } catch (error) {
          console.error('auto load segments error:', error);
        }
      };
      
      loadSegments();
    }
  }, [isInitialized, scripts, currentVersionSegments.length, fetchAllCurrentVersionSegments, currentScriptId, handleScriptSelect]);
  
  // Auto-generate video segments for all scripts when needed (after voices are generated)
  useEffect(() => {
    // Only run if:
    // 1. Editor is initialized
    // 2. We have scripts
    // 3. Video segments haven't been generated yet
    // 4. Voices have been generated (dependency chain)
    if (isInitialized && scripts && scripts.length > 0 && !segmentsGenerated && voicesGenerated) {
      // Check if all scripts have corresponding video segments
      const allScriptsHaveSegments = currentVersionSegments.length >= scripts.length;
      
      if (!allScriptsHaveSegments) {
        console.log('[VideoEditor] Auto-generating video segments for all scripts...');
        
        // Check if we've already generated segments for this project recently
        const projectKey = `segment_generation_${projectId}`;
        const segmentGenerationStatus = localStorage.getItem(projectKey);
        const currentTime = new Date().getTime();
        
        // If we have a stored status, check its timestamp and count
        if (segmentGenerationStatus) {
          try {
            const status = JSON.parse(segmentGenerationStatus);
            const timestamp = status.timestamp || 0;
            const scriptCount = status.scriptCount || 0;
            const hoursSinceLastGeneration = (currentTime - timestamp) / (1000 * 60 * 60);
            
            // Skip generation if:
            // 1. Less than 24 hours have passed since the last generation AND
            // 2. We have the same number of scripts or fewer (suggesting no new scripts have been added)
            if (hoursSinceLastGeneration < 24 && scriptCount >= scripts.length) {
              console.log(`Auto video segment generation skipped - last generated ${hoursSinceLastGeneration.toFixed(2)} hours ago for ${scriptCount} scripts`);
              setSegmentsGenerated(true);
              return;
            }
          } catch (error) {
            console.error('Error parsing segment generation status:', error);
          }
        }
        
        // Generate video segments immediately after voices are ready
        const generateSegments = async () => {
          try {
            // Generate video segments for all scripts
            console.log(`Starting auto-generation of video segments for all ${scripts.length} scripts`);
            await generateAllSegments();
            setSegmentsGenerated(true);
            
            // Save the generation status to localStorage
            localStorage.setItem(projectKey, JSON.stringify({
              timestamp: currentTime,
              scriptCount: scripts.length
            }));
            
            console.log('Successfully auto-generated video segments for all scripts');
          } catch (error) {
            console.error('Error auto-generating video segments:', error);
          }
        };
        
        generateSegments();
      } else {
        // If all scripts already have video segments, mark as generated
        setSegmentsGenerated(true);
      }
    }
  }, [isInitialized, scripts, currentVersionSegments, segmentsGenerated, projectId, generateAllSegments, voicesGenerated]);
  
  // Fetch music resources when component mounts
  useEffect(() => {
    const loadMusicResources = async () => {
      try {
        await fetchAudioResources();
        setMusicLoaded(true);
        console.log('[VideoEditor] Background music resources loaded successfully');
      } catch (error) {
        console.error('[VideoEditor] Error loading background music resources:', error);
        // Even if there's an error, we'll mark as loaded to not block the UI
        setMusicLoaded(true);
      }
    };
    
    loadMusicResources();
  }, [fetchAudioResources]);
  
  // Add effect to update the allGenerationComplete state
  useEffect(() => {
    // Only set to true when all generation steps are completed
    // AND no segments are currently being generated
    if (voicesGenerated && textOverlaysCreated && segmentsGenerated && musicLoaded && 
        !isGeneratingSegments && !isSegmentPolling) {
      setAllGenerationComplete(true);
      console.log('[VideoEditor] All generation processes complete!');
    } else if (allGenerationComplete && (isGeneratingSegments || isSegmentPolling)) {
      // Reset the complete state if we were complete but generation has started again
      setAllGenerationComplete(false);
      console.log('[VideoEditor] Generation in progress, resetting complete state');
    }
  }, [voicesGenerated, textOverlaysCreated, segmentsGenerated, musicLoaded, 
      isGeneratingSegments, isSegmentPolling, allGenerationComplete]);
  
  // NOTE: Removed duplicate polling mechanism to avoid multiple concurrent polling
  // The videoSegmentStore already handles polling, so we rely on that instead
  // This reduces redundant API calls and prevents race conditions
  
  return (
    <div className="flex h-full w-full">
      <div className="h-full">
        <ToolSideBar
          activeTool={activeTool}
          onToolClick={setActiveTool}
          currentScriptId={currentScriptId}
          onScriptSelect={handleScriptSelect}
          projectId={projectId ? projectId : undefined}
        />
      </div>

      <div className="flex flex-1 z-20">
        <div className="flex-grow">
          <Preview 
            projectName={currentProject?.title || "Untitled Project"}
            onProjectNameChange={handleProjectNameChange}
            currentScriptId={currentScriptId}
            currentSegmentId={currentSegmentId}
            onSegmentSelect={handleSegmentSelect}
            fetchAllCurrentVersionSegments={fetchAllCurrentVersionSegments}
            currentVersionSegments={currentVersionSegments}
            selectedVoice={selectedVoice}
            isGeneratingSegments={isGeneratingSegments || isSegmentPolling}
            onGenerateSegment={generateAllSegments}
            scripts={scripts}
            voicesGenerated={voicesGenerated}
            textOverlaysCreated={textOverlaysCreated}
            segmentsGenerated={segmentsGenerated}
            allGenerationComplete={allGenerationComplete}
            isTimelineCollapsed={isTimelineCollapsed}
          />
        </div>
        <Control 
          ref={controlRef}
          currentScriptId={currentScriptId}
          currentSegmentId={currentSegmentId}
          onCreateSegment={handleCreateSegment}
          onVoiceChange={handleVoiceChange}
          onToggleTimeline={handleToggleTimeline}
        />
      </div>
    </div>
  );
};

export default VideoEditor;