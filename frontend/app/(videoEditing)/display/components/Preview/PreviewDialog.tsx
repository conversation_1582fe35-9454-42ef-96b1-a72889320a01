import { FC, useState } from 'react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Player } from '@remotion/player';
import { VideoComposition } from './VideoComposition';
import { useVideoPlayerStore } from '../../../../../lib/store/videoPlayerStore';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { useTextOverlayStore } from '../../../../../lib/store/textOverlayStore';
import { useDisplaySettings } from '../../../../../lib/hooks/useDisplaySettings';
import { useVideoProjectStore } from '../../../../../lib/store/videoProjectStore';

interface PreviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PreviewDialog: FC<PreviewDialogProps> = ({ isOpen, onClose }) => {
  const { 
    totalFrames, 
    fps, 
    segments, 
    framesPerSegment, 
    introBlackScreenFrames, 
    endScreenFrames, 
    backgroundMusicUrl, 
    introEnabled, 
    endEnabled,
    backgroundMusicVolume,
    isBackgroundMusicEnabled
  } = useVideoPlayerStore();
  const { textOverlays, projectId } = useTextOverlayStore();
  const [showWatermark, setShowWatermark] = useState(true);
  const [loading, setLoading] = useState(false);
  const useSettings = useDisplaySettings(projectId ?? undefined);
  const { currentProject } = useVideoProjectStore();

  const { 
    getVoiceAudioFile: storeGetVoiceAudioFile, 
    scripts: storeScripts, 
    selectedVoice: storeSelectedVoice 
  } = useVideoPlayerStore();
  
  const handleDownload = async (withWatermark: boolean) => {
    setLoading(true);
    setShowWatermark(withWatermark);
    console.log('Downloading video', withWatermark ? 'with' : 'without', 'watermark');
    
    try {
      // Get all necessary data from the video player store
      const videoState = {
        segments: segments,
        fps: fps,
        totalFrames: totalFrames,
        framesPerSegment: framesPerSegment,
        introBlackScreenFrames: introBlackScreenFrames,
        endScreenFrames: endScreenFrames,
        backgroundMusicUrl: backgroundMusicUrl,
        introEnabled: introEnabled,
        endEnabled: endEnabled,
        backgroundMusicVolume: backgroundMusicVolume,
        isBackgroundMusicEnabled: isBackgroundMusicEnabled,
        textOverlays: textOverlays,
        useSettings: useSettings,
        brandName: currentProject?.brandName ?? 'Brand',
        getVoiceAudioFile: storeGetVoiceAudioFile,
        scripts: storeScripts,
        selectedVoice: storeSelectedVoice
      };
      
      const response = await fetch('/api/download-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          showWatermark: withWatermark,
          videoState: videoState
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to export Video');
      }
      
      const data = await response.json();
      
      if (!data.exportFile) {
        throw new Error('No export file URL returned');
      }
      
      // Open the video URL in a new window
      window.open(data.exportFile, '_blank');
    } catch (err) {
      console.error('Error exporting the video:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[70vw] min-w-[50vw] p-6">
        <DialogHeader className="mb-4">
          <DialogTitle className="text-xl font-semibold">Video Preview</DialogTitle>
        </DialogHeader>
        <div className="relative w-full" style={{ 
          paddingTop: '56.25%', // 16:9 aspect ratio
        }}>
          <div className="absolute top-0 left-0 w-full h-full">
            {totalFrames > 0 ? (
              <Player
                component={VideoComposition}
                inputProps={{ 
                  showWatermark: showWatermark,
                  segments: segments,
                  framesPerSegment: framesPerSegment,
                  introBlackScreenFrames: introBlackScreenFrames,
                  endScreenFrames: endScreenFrames,
                  backgroundMusicUrl: backgroundMusicUrl,
                  introEnabled: introEnabled,
                  endEnabled: endEnabled,
                  fps: fps,
                  backgroundMusicVolume: backgroundMusicVolume,
                  isBackgroundMusicEnabled: isBackgroundMusicEnabled,
                  durationInFrames: totalFrames,
                  textOverlays: textOverlays,
                  useSettings: useSettings,
                  brandName: currentProject?.brandName ?? 'Brand',
                  getVoiceAudioFile: storeGetVoiceAudioFile,
                  scripts: storeScripts,
                  selectedVoice: storeSelectedVoice
                }}
                durationInFrames={totalFrames}
                fps={fps}
                compositionWidth={1920}
                compositionHeight={1080}
                style={{ 
                  width: '100%', 
                  height: '100%',
                  borderRadius: '0.5rem',
                  overflow: 'hidden'
                }}
                controls
                autoPlay={false}
                loop={false}
                showVolumeControls
                spaceKeyToPlayOrPause
                clickToPlay
                allowFullscreen
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
                <div className="text-center">
                  <div className="w-12 h-12 border-4 border-t-cyan-700 border-gray-200 rounded-full animate-spin mb-4 mx-auto"></div>
                  <p className="text-gray-600">Loading preview...</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex justify-end space-x-4 mt-6">
          <Button
            variant="outline"
            onClick={() => handleDownload(true)}
            className="flex items-center"
            disabled={loading || totalFrames === 0}
          >
            {loading ? (
              <div className="w-4 h-4 border-2 border-t-cyan-700 border-gray-200 rounded-full animate-spin mr-2"></div>
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            Download with Watermark
          </Button>
          <Button
            variant="default"
            onClick={() => handleDownload(false)}
            className="flex items-center bg-cyan-700 hover:bg-cyan-800"
            disabled={loading || totalFrames === 0}
          >
            {loading ? (
              <div className="w-4 h-4 border-2 border-t-white border-gray-200 rounded-full animate-spin mr-2"></div>
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            Download
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 