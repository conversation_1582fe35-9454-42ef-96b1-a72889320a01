import { FC, useMemo } from 'react';
import { AbsoluteFill, useCurrentFrame, Audio } from 'remotion';
import { useDisplaySettings } from '../../../../../lib/hooks/useDisplaySettings';
import { useVideoProjectStore } from '../../../../../lib/store/videoProjectStore';
import { TextLogo } from './TextLogo';

// Interface for EndScreen props (used in Lambda rendering)
interface EndScreenProps {
  useSettings?: {
    endSettings?: {
      backgroundColor?: string;
      logoUrl?: string;
      logoX?: number;
      logoY?: number;
      logoWidth?: number;
      logoHeight?: number;
      centerTextX?: number;
      centerTextY?: number;
      centerTextSize?: number;
      centerTextColor?: string;
      centerText?: string;
      bottomTextX?: number;
      bottomTextY?: number;
      bottomTextSize?: number;
      bottomTextColor?: string;
      bottomText?: string;
      audioUrl?: string | null;
      audioVolume?: number;
      isAudioEnabled?: boolean;
    };
    introSettings?: any; // Add introSettings for completeness
  };
  brandName?: string;
}

/**
 * End screen component with logo and text elements
 * This is displayed at the end of the video if enabled in settings
 * Works both with local state (browser) and static props (Lambda)
 */
export const EndScreen: FC<EndScreenProps> = ({
  useSettings: propUseSettings,
  brandName: propBrandName
}) => {
  const currentFrame = useCurrentFrame();
  
  // Get project ID from URL in browser environment
  const projectIdFromUrl = useMemo(() => {
    if (typeof window === 'undefined') return undefined;
    const searchParams = new URLSearchParams(window.location.search);
    const projectIdParam = searchParams.get('projectId');
    return projectIdParam ? projectIdParam : undefined;
  }, []);
  
  // Get settings from hook with projectId (in browser only, skip if props provided)
  const useSettings = propUseSettings ? null : useDisplaySettings(projectIdFromUrl);
  
  // Get current project for brand name (skip if props provided)
  const { currentProject } = useVideoProjectStore();
  
  // Fallback end settings if the hook doesn't work (like in Lambda)
  const defaultEndSettings = {
    backgroundColor: '#000000',
    logoUrl: '/Fylow.png',
    logoX: 50,
    logoY: 30,
    logoWidth: 30,
    logoHeight: 40,
    centerTextX: 50,
    centerTextY: 60,
    centerTextSize: 48,
    centerTextColor: '#FFFFFF',
    centerText: 'Thank You for Watching',
    bottomTextX: 50,
    bottomTextY: 80,
    bottomTextSize: 24,
    bottomTextColor: '#CCCCCC',
    bottomText: 'Created with Adsynthetica',
    audioUrl: null,
    audioVolume: 1,
    isAudioEnabled: true,
  };
  
  // Use props if provided (Lambda), otherwise use settings from hook or fallback to defaults
  const endSettings = {
    ...defaultEndSettings,
    ...(useSettings?.endSettings || {}),
    ...(propUseSettings?.endSettings || {})
  };
  
  // Calculate opacity for fade in effect
  let opacity = 0;
  if (currentFrame < 15) {
    // Fade in during first 15 frames
    opacity = currentFrame / 15;
  } else {
    // Full opacity after fade in
    opacity = 1;
  }
  
  // Calculate logo position based on settings
  const logoStyle = {
    position: 'absolute' as const,
    top: `${endSettings.logoY}%`,
    left: `${endSettings.logoX}%`,
    transform: 'translate(-50%, -50%)',
    width: `${endSettings.logoWidth}%`,
    height: `${endSettings.logoHeight}%`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    opacity
  } as React.CSSProperties;
  
  // Calculate center text position
  const centerTextStyle = {
    position: 'absolute' as const,
    top: `${endSettings.centerTextY}%`,
    left: `${endSettings.centerTextX}%`,
    transform: 'translate(-50%, -50%)',
    color: endSettings.centerTextColor,
    fontSize: `${endSettings.centerTextSize}px`,
    fontWeight: 'bold' as const,
    textAlign: 'center' as const,
    width: '80%',
    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
    opacity,
    whiteSpace: 'pre-wrap' as const,
  } as React.CSSProperties;
  
  // Calculate bottom text position
  const bottomTextStyle = {
    position: 'absolute' as const,
    top: `${endSettings.bottomTextY}%`,
    left: `${endSettings.bottomTextX}%`,
    transform: 'translate(-50%, -50%)',
    color: endSettings.bottomTextColor,
    fontSize: `${endSettings.bottomTextSize}px`,
    textAlign: 'center' as const,
    width: '80%',
    opacity,
    whiteSpace: 'pre-wrap' as const,
  } as React.CSSProperties;
  
  // Determine if we should use text logo
  // Check for default logo in different formats (local and S3 paths)
  const isDefaultLogo = (url: string) => {
    // Log the URL for debugging
    console.log('EndScreen - Checking logo URL:', url);
    
    // Check for empty URL
    if (!url) {
      console.log('EndScreen - Empty URL, using text logo');
      return true;
    }
    
    // Check for local default logo
    if (url === '/Fylow.png') {
      console.log('EndScreen - Local default logo detected, using text logo');
      return true;
    }
    
    // Check for S3 default logo - strict check to make sure it's the default logo
    if (url.includes('/logo/default/Fylow.png') || 
        url.includes('Fylow.png') ||
        url.includes('fylow.png')) {
      console.log('EndScreen - S3 default logo detected, using text logo');
      return true;
    }
    
    // If URL exists and is not a default logo, use the actual logo
    console.log('EndScreen - Custom logo detected, using provided image');
    return false;
  };
  
  // FORCE_TEXT_LOGO: Set this to true to always use text logo instead of image logo
  const FORCE_TEXT_LOGO = false;
  
  // Determine if we should use text logo
  const useTextLogo = FORCE_TEXT_LOGO || isDefaultLogo(endSettings.logoUrl);
  
  console.log('EndScreen - Using text logo:', useTextLogo);
  console.log('EndScreen - Logo URL:', endSettings.logoUrl);
  console.log('EndScreen - Brand name:', propBrandName ?? currentProject?.brandName ?? 'Brand');
  
  const brandName = propBrandName ?? currentProject?.brandName ?? 'Brand';
  
  // Determine text color based on background color
  const textColor = endSettings.backgroundColor === '#000000' ? '#ffffff' : '#000000';
  
  return (
    <AbsoluteFill style={{ backgroundColor: endSettings.backgroundColor }}>
      {/* Optional Audio for End Screen */}
      {endSettings.isAudioEnabled && endSettings.audioUrl && (
        <Audio 
          src={endSettings.audioUrl} 
          volume={endSettings.audioVolume} 
        />
      )}

      {/* Logo */}
      <div style={logoStyle}>
        {useTextLogo ? (
          <TextLogo
            brandName={brandName}
            width={endSettings.logoWidth}
            height={endSettings.logoHeight}
            textColor={textColor}
          />
        ) : (
          <img 
            src={endSettings.logoUrl} 
            alt="Logo" 
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain'
            }}
          />
        )}
      </div>
      
      {/* Center text */}
      <div style={centerTextStyle}>
        {endSettings.centerText}
      </div>
      
      {/* Bottom text */}
      <div style={bottomTextStyle}>
        {endSettings.bottomText}
      </div>
    </AbsoluteFill>
  );
}; 