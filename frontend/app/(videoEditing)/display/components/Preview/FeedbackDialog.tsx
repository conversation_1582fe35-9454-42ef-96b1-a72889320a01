import { FC, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, AlertCircle } from 'lucide-react';

interface FeedbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ErrorResponse {
  message: string;
  details?: string;
  error?: string;
}

export const FeedbackDialog: FC<FeedbackDialogProps> = ({ isOpen, onClose }) => {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<ErrorResponse | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Reset form when dialog is opened
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
      // Only reset if not currently submitting
      if (!isSubmitting) {
        resetForm();
      }
    }
  };

  const resetForm = () => {
    setSubject('');
    setMessage('');
    setSubmitError(null);
    setSubmitSuccess(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Form validation
    if (!subject.trim() || !message.trim()) {
      setSubmitError({ message: 'Please fill in all fields' });
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);
    
    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject,
          message
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to send feedback', { 
          cause: data 
        });
      }
      
      setSubmitSuccess(true);
      // Reset form after successful submission (after 2 seconds)
      setTimeout(() => {
        resetForm();
        onClose();
      }, 2000);
      
    } catch (error) {
      console.error('Error sending feedback:', error);
      
      // Extract detailed error information if available
      if (error instanceof Error && error.cause && typeof error.cause === 'object') {
        setSubmitError(error.cause as ErrorResponse);
      } else {
        setSubmitError({ 
          message: error instanceof Error ? error.message : 'Failed to send feedback',
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-cyan-700">Send Feedback</DialogTitle>
        </DialogHeader>
        
        {submitSuccess ? (
          <div className="py-6 text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">Thank You!</h3>
            <p className="text-gray-600">Your feedback has been submitted successfully.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            {submitError && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                <div className="flex items-center mb-1">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span className="font-medium">{submitError.message}</span>
                </div>
                {submitError.details && (
                  <p className="text-sm mt-1 ml-6">{submitError.details}</p>
                )}
                {submitError.error && process.env.NODE_ENV === 'development' && (
                  <div className="mt-2 ml-6 text-xs font-mono p-2 bg-red-100 rounded overflow-x-auto">
                    {submitError.error}
                  </div>
                )}
              </div>
            )}
            
            <div className="space-y-2">
              <label htmlFor="subject" className="text-sm font-medium text-gray-700">
                Subject
              </label>
              <Input
                id="subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Brief description of your feedback"
                className="w-full"
                disabled={isSubmitting}
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="message" className="text-sm font-medium text-gray-700">
                Message
              </label>
              <Textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Tell us your thoughts, suggestions, or issues..."
                className="w-full min-h-[120px]"
                disabled={isSubmitting}
              />
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-cyan-600 hover:bg-cyan-700 text-white"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Send Feedback'
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default FeedbackDialog; 