import { FC, useRef, useState, useEffect, ReactNode } from 'react';
import { useDrag } from 'react-dnd';
import DraggableItem from './DraggableItem';

interface ResizableItemProps {
  id: number | string;
  itemType: string;
  startPercent: number;
  widthPercent: number;
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  handleColor?: string;
  onResizeStart?: () => void;
  onResizeEnd?: (newWidthPercent: number) => void;
  onDragStart?: () => void;
  onDragEnd?: () => void;
}

/**
 * A reusable resizable item component that can be dragged and resized on a timeline
 * Extends the functionality of DraggableItem with resize capabilities
 */
const ResizableItem: FC<ResizableItemProps> = ({
  id,
  itemType,
  startPercent,
  widthPercent,
  children,
  className = '',
  style = {},
  handleColor = 'bg-blue-400',
  onResizeStart,
  onResizeEnd,
  onDragStart,
  onDragEnd
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeHandleRef = useRef<HTMLDivElement>(null);
  
  // Drag for resizing
  const [{ isDraggingResize }, resizeDrag] = useDrag(() => ({
    type: itemType,
    item: () => {
      if (onResizeStart) {
        onResizeStart();
      }
      setIsResizing(true);
      return { 
        id,
        type: itemType,
        startPercent,
        width: containerRef.current?.offsetWidth || 0,
        isResizing: true
      };
    },
    end: (item, monitor) => {
      setIsResizing(false);
    },
    collect: (monitor) => ({
      isDraggingResize: !!monitor.isDragging()
    })
  }), [id, itemType, startPercent, onResizeStart]);
  
  // Combine styles for main container
  const combinedStyle = {
    width: `${widthPercent}%`,
    transition: isResizing ? 'none' : 'width 0.1s ease-out',
    ...style
  };
  
  return (
    <DraggableItem
      id={id}
      itemType={itemType}
      startPercent={startPercent}
      className={`${className}`}
      style={combinedStyle}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
    >
      <div
        ref={containerRef}
        className="relative w-full h-full"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {children}
        
        {/* Resize handle */}
        <div
          ref={(node) => {
            resizeHandleRef.current = node;
            resizeDrag(node);
          }}
          className={`absolute right-0 top-0 bottom-0 w-2 cursor-ew-resize z-10
            ${isHovered || isDraggingResize ? handleColor : 'bg-transparent'}
            transition-colors duration-150 ease-in-out rounded-r-sm
          `}
          onClick={(e) => e.stopPropagation()}
          title="Drag to resize"
        />
      </div>
    </DraggableItem>
  );
};

export default ResizableItem; 