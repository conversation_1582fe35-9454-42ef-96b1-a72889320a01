import { FC } from 'react';

interface TextLogoProps {
  brandName: string;
  width: number;
  height: number;
  textColor?: string;
}

/**
 * TextLogo component that displays brand name as text
 * Used as a fallback when no logo image is provided
 */
export const TextLogo: FC<TextLogoProps> = ({
  brandName,
  width,
  height,
  textColor = '#000000'
}) => {
  // Use larger font size for better visibility
  const fontSize = 72;

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'transparent',
      }}
    >
      <div
        style={{
          color: textColor,
          fontSize: `${fontSize}px`,
          fontFamily: 'Arial, sans-serif',
          textAlign: 'center',
          lineHeight: 1.2,
          padding: '5%',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontWeight: 700, // Bold font weight for better visibility
          letterSpacing: '0.5px', // Add letter spacing for better readability
        }}
      >
        {brandName || 'No Brand Name'}
      </div>
    </div>
  );
}; 