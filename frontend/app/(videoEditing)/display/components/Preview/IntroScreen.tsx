import { FC, useMemo } from 'react';
import { AbsoluteFill, useCurrentFrame } from 'remotion';
import { useDisplaySettings } from '../../../../../lib/hooks/useDisplaySettings';
import { useVideoProjectStore } from '../../../../../lib/store/videoProjectStore';
import { TextLogo } from './TextLogo';

// Interface for IntroScreen props (used in Lambda rendering)
interface IntroScreenProps {
  useSettings?: {
    introSettings?: {
      backgroundColor?: string;
      logoUrl?: string;
      logoX?: number;
      logoY?: number;
      logoWidth?: number;
      logoHeight?: number;
    };
    endSettings?: any; // Add endSettings for completeness
  };
  brandName?: string;
}

/**
 * Intro screen component with logo and fade in/out effects
 * This is displayed at the beginning of the video if enabled in settings
 * Works both with local state (browser) and static props (Lambda)
 */
export const IntroScreen: FC<IntroScreenProps> = ({
  useSettings: propUseSettings,
  brandName: propBrandName
}) => {
  const currentFrame = useCurrentFrame();
  
  // Get project ID from URL in browser environment
  const projectIdFromUrl = useMemo(() => {
    if (typeof window === 'undefined') return undefined;
    const searchParams = new URLSearchParams(window.location.search);
    const projectIdParam = searchParams.get('projectId');
    return projectIdParam || undefined;
  }, []);
  
  // Get settings from hook with projectId (in browser only, skip if props provided)
  const useSettings = propUseSettings ? null : useDisplaySettings(projectIdFromUrl);
  
  // Get current project for brand name (skip if props provided)
  const { currentProject } = useVideoProjectStore();
  
  // Fallback intro settings if the hook doesn't work (like in Lambda)
  const defaultIntroSettings = {
    backgroundColor: '#000000',
    logoUrl: '/Fylow.png',
    logoX: 50,
    logoY: 50,
    logoWidth: 30,
    logoHeight: 40
  };
  
  // Use props if provided (Lambda), otherwise use settings from hook or fallback to defaults
  const introSettings = {
    ...defaultIntroSettings,
    ...(useSettings?.introSettings || {}),
    ...(propUseSettings?.introSettings || {})
  };
  
  // Calculate logo opacity for fade in/out effect
  let logoOpacity = 0;
  if (currentFrame < 15) {
    // Fade in during first 15 frames
    logoOpacity = currentFrame / 15;
  } else if (currentFrame > 60 - 15) {
    // Fade out during last 15 frames
    logoOpacity = (60 - currentFrame) / 15;
  } else {
    // Full opacity in the middle
    logoOpacity = 1;
  }
  
  // Calculate logo position based on settings
  const logoStyle = {
    position: 'absolute' as const,
    top: `${introSettings.logoY}%`,
    left: `${introSettings.logoX}%`,
    transform: 'translate(-50%, -50%)',
    width: `${introSettings.logoWidth}%`,
    height: `${introSettings.logoHeight}%`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    opacity: logoOpacity,
    minWidth: '200px',
    minHeight: '100px',
  } as React.CSSProperties;
  
  // Determine if we should use text logo
  // Check for default logo in different formats (local and S3 paths)
  const isDefaultLogo = (url: string) => {
    // Log the URL for debugging
    console.log('Checking logo URL:', url);
    
    // Check for empty URL
    if (!url) {
      console.log('Empty URL, using text logo');
      return true;
    }
    
    // Check for local default logo
    if (url === '/Fylow.png') {
      console.log('Local default logo detected, using text logo');
      return true;
    }
    
    // Check for S3 default logo - strict check to make sure it's the default logo
    if (url.includes('/logo/default/Fylow.png') || 
        url.includes('Fylow.png') ||
        url.includes('fylow.png')) {
      console.log('S3 default logo detected, using text logo');
      return true;
    }
    
    // If URL exists and is not a default logo, use the actual logo
    console.log('Custom logo detected, using provided image');
    return false;
  };
  
  // FORCE_TEXT_LOGO: Set this to true to always use text logo instead of image logo
  const FORCE_TEXT_LOGO = false;
  
  // Determine if we should use text logo
  const useTextLogo = FORCE_TEXT_LOGO || isDefaultLogo(introSettings.logoUrl);
  
  console.log('Using text logo:', useTextLogo);
  console.log('Logo URL:', introSettings.logoUrl);
  
  const brandName = propBrandName ?? currentProject?.brandName ?? 'Brand';
  
  // Determine text color based on background color
  const textColor = introSettings.backgroundColor === '#000000' ? '#ffffff' : '#000000';
  
  // 确保logo容器有足够的大小
  const logoWidth = Math.max(introSettings.logoWidth, 30); // 最小30%
  const logoHeight = Math.max(introSettings.logoHeight, 20); // 最小20%
  
  return (
    <AbsoluteFill style={{ backgroundColor: introSettings.backgroundColor }}>
      {/* Logo with fade in/out effect */}
      <div style={logoStyle}>
        {useTextLogo ? (
          <TextLogo
            brandName={brandName}
            width={logoWidth}
            height={logoHeight}
            textColor={textColor}
          />
        ) : (
          <img 
            src={introSettings.logoUrl} 
            alt="Logo" 
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain'
            }}
          />
        )}
      </div>
    </AbsoluteFill>
  );
}; 