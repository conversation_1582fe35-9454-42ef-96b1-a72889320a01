import { FC, useCallback, useEffect, useState, useRef } from 'react';
import { AbsoluteFill, Video, Audio, continueRender, delayRender } from 'remotion';
import { VideoSegment } from '@/lib/store/videoSegmentStore';
import { useVideoPlayerStore } from '../../../../../lib/store/videoPlayerStore';

interface SegmentContentProps {
  segment: VideoSegment;
  index: number;
  // Props for Lambda rendering
  scripts?: Record<string, any>;
  selectedVoice?: string;
  getVoiceAudioFile?: (segment: VideoSegment, index: number) => string;
}

/**
 * Component that renders a single video segment with its content
 * This includes the video and audio narration
 * Works both with local state (browser) and static props (Lambda)
 * Handles media loading errors gracefully
 */
export const SegmentContent: FC<SegmentContentProps> = ({ 
  segment, 
  index,
  scripts: propScripts,
  selectedVoice: propSelectedVoice,
  getVoiceAudioFile: propGetVoiceAudioFile
}) => {
  // Get values from store (used in browser)
  const { 
    getVoiceAudioFile: storeGetVoiceAudioFile, 
    scripts: storeScripts, 
    selectedVoice: storeSelectedVoice 
  } = useVideoPlayerStore();
  
  // Use props if provided (Lambda), otherwise use store values (browser)
  const getVoiceAudioFile = propGetVoiceAudioFile || storeGetVoiceAudioFile;
  const scripts = propScripts || storeScripts;
  const selectedVoice = propSelectedVoice || storeSelectedVoice;
  
  const [videoError, setVideoError] = useState(false);
  const [audioError, setAudioError] = useState(false);
  const [audioSrc, setAudioSrc] = useState('');
  
  // Helper function to convert base64 to Data URL
  const createAudioDataUrl = (base64Data: string): string => {
    try {
      // Ensure it's a proper data URL format
      if (base64Data.startsWith('data:')) {
        return base64Data;
      }
      
      // Convert base64 to data URL
      const dataUrl = `data:audio/mpeg;base64,${base64Data}`;
      console.log('Created data URL for audio');
      return dataUrl;
    } catch (error) {
      console.error('Error creating data URL from base64:', error);
      return '';
    }
  };
  
  // get voice audio file
  useEffect(() => {
    let src = '';
    try {
      // first try to get audio from getVoiceAudioFile
      if (getVoiceAudioFile) {
        const audioData = getVoiceAudioFile(segment, index);
        console.log('Got audio source via getVoiceAudioFile');
        
        // Convert base64 data to data URL
        if (audioData && audioData.startsWith) {
          if (audioData.startsWith('data:audio')) {
            // Already a data URL, use as is
            src = audioData;
            console.log('Using existing data URL for audio');
          } else {
            // Convert base64 to data URL
            src = createAudioDataUrl(audioData);
            console.log('Converted base64 to data URL');
          }
        }
      }
      
      // if failed to get audio, try to get audio from script
      if (!src && segment.scriptId && scripts && scripts[segment.scriptId]) {
        const script = scripts[segment.scriptId];
        console.log('Trying to get audio from script:', script.id);
        
        // get audio according to the selected voice type
        let audioBase64 = '';
        if (selectedVoice === 'blue' && script.narratorAudioMale) {
          audioBase64 = script.narratorAudioMale;
          console.log('Using male narrator audio');
        } else if (selectedVoice === 'pink' && script.narratorAudioFemale) {
          audioBase64 = script.narratorAudioFemale;
          console.log('Using female narrator audio');
        } else if (script.narratorAudio) {
          audioBase64 = script.narratorAudio;
          console.log('Using general narrator audio');
        }
        
        // Convert to data URL
        if (audioBase64) {
          src = createAudioDataUrl(audioBase64);
          console.log('Converted script audio to data URL');
        }
      }
      
      setAudioSrc(src);
    } catch (error) {
      console.error('Error getting voice audio file:', error);
      setAudioError(true);
    }
  }, [segment, index, getVoiceAudioFile, scripts, selectedVoice]);
  
  // handle video loading with proper timeout handling
  const [handle] = useState(() => delayRender("Loading video"));
  
  const onVideoError = useCallback(() => {
    console.error('Error loading video:', segment.videoUrl);
    setVideoError(true);
    continueRender(handle);
  }, [segment.videoUrl, handle]);
  
  const onVideoLoad = useCallback(() => {
    continueRender(handle);
  }, [handle]);
  
  // Set a timeout to force continue rendering even if video fails to load
  useEffect(() => {
    const timeout = setTimeout(() => {
      continueRender(handle);
    }, 10000); // 10 second timeout
    
    return () => clearTimeout(timeout);
  }, [handle]);
  
  return (
    <AbsoluteFill>
      {/* Video content if available */}
      {segment.videoUrl && !videoError ? (
        <Video
          src={segment.videoUrl}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
          }}
          onError={onVideoError}
          onLoad={onVideoLoad}
        />
      ) : (
        <div style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#000',
          color: '#fff',
          fontSize: '32px'
        }}>
          {videoError ? 'Video could not be loaded' : 'Loading video...'}
        </div>
      )}
      
      {/* Voice narration audio - only render if we have an audio source and no errors */}
      {audioSrc && !audioError && (
        <Audio
          src={audioSrc}
          volume={0.8}
        />
      )}
    </AbsoluteFill>
  );
}; 