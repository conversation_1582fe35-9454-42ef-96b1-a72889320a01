import { FC, useEffect, useMemo } from 'react';
import { AbsoluteFill, Audio, Sequence } from 'remotion';
import Image from 'next/image';
import { useVideoPlayerStore } from '../../../../../lib/store/videoPlayerStore';
import { useDisplaySettings } from '../../../../../lib/hooks/useDisplaySettings';
import { useMusicStore } from '../../../../../lib/store/musicStore';
import { useTextOverlayStore, TextOverlay } from '../../../../../lib/store/textOverlayStore';
import { useVideoScriptStore } from '../../../../../lib/store/videoScriptStore';
import { SegmentContent } from './SegmentContent';
import { IntroScreen } from './IntroScreen';
import { EndScreen } from './EndScreen';
import { TextOverlayComponent } from './TextOverlayComponent';
import { VideoSegment } from '../../../../../lib/store/videoSegmentStore';

// Define a simplified segment type for Lambda rendering
// This helps prevent serialization issues
type SimplifiedSegment = {
  id: string | number;
  videoUrl: string;
  scriptId?: number;
  status?: string;
};

interface VideoCompositionProps {
  showWatermark?: boolean;
  // Add props for static rendering (Lambda)
  segments?: VideoSegment[] | SimplifiedSegment[];
  framesPerSegment?: number; 
  introBlackScreenFrames?: number;
  endScreenFrames?: number;
  backgroundMusicUrl?: string | null;
  introEnabled?: boolean;
  endEnabled?: boolean;
  fps?: number;
  backgroundMusicVolume?: number;
  isBackgroundMusicEnabled?: boolean;
  durationInFrames?: number;
  textOverlays?: TextOverlay[];
  // Display settings props
  useSettings?: {
    endSettings?: {
      backgroundColor?: string;
      logoUrl?: string;
      logoX?: number;
      logoY?: number;
      logoWidth?: number;
      logoHeight?: number;
      centerTextX?: number;
      centerTextY?: number;
      centerTextSize?: number;
      centerTextColor?: string;
      centerText?: string;
      bottomTextX?: number;
      bottomTextY?: number;
      bottomTextSize?: number;
      bottomTextColor?: string;
      bottomText?: string;
      audioUrl?: string | null;
      audioVolume?: number;
      isAudioEnabled?: boolean;
    };
    introSettings?: any;
  };
  brandName?: string;
  // SegmentContent props
  scripts?: Record<string, any>;
  selectedVoice?: string;
  getVoiceAudioFile?: (segment: VideoSegment, index: number) => string;
}

/**
 * Main video composition component that handles the layout and timing of all segments
 * Uses simple Sequence components instead of TransitionSeries for better performance
 * Responsible for synchronizing data from various stores into the videoPlayerStore
 * Can be used both with local state (via hooks) or with static props (via Lambda)
 */
export const VideoComposition: FC<VideoCompositionProps> = ({ 
  showWatermark = false,
  // Default values from props (used in Lambda)
  segments: propSegments,
  framesPerSegment: propFramesPerSegment,
  introBlackScreenFrames: propIntroBlackScreenFrames,
  endScreenFrames: propEndScreenFrames,
  backgroundMusicUrl: propBackgroundMusicUrl,
  introEnabled: propIntroEnabled,
  endEnabled: propEndEnabled,
  fps: propFps,
  backgroundMusicVolume: propBackgroundMusicVolume,
  isBackgroundMusicEnabled: propIsBackgroundMusicEnabled,
  durationInFrames: propDurationInFrames,
  textOverlays: propTextOverlays,
  // Display settings props
  useSettings: propUseSettings,
  brandName,
  // SegmentContent props
  scripts: propScripts,
  selectedVoice,
  getVoiceAudioFile
}) => {
  // Get values from store (used in browser)
  const { 
    segments: storeSegments, 
    framesPerSegment: storeFramesPerSegment, 
    introBlackScreenFrames: storeIntroBlackScreenFrames,
    endScreenFrames: storeEndScreenFrames,
    backgroundMusicUrl: storeBackgroundMusicUrl,
    introEnabled: storeIntroEnabled,
    endEnabled: storeEndEnabled,
    fps: storeFps,
    backgroundMusicVolume: storeBackgroundMusicVolume,
    isBackgroundMusicEnabled: storeIsBackgroundMusicEnabled
  } = useVideoPlayerStore();
  
  // Use props if provided (Lambda), otherwise use store values (browser)
  const segments = propSegments || storeSegments;
  const framesPerSegment = propFramesPerSegment || storeFramesPerSegment;
  const introBlackScreenFrames = propIntroBlackScreenFrames || storeIntroBlackScreenFrames;
  const endScreenFrames = propEndScreenFrames || storeEndScreenFrames;
  const backgroundMusicUrl = propBackgroundMusicUrl !== undefined ? propBackgroundMusicUrl : storeBackgroundMusicUrl;
  const introEnabled = propIntroEnabled !== undefined ? propIntroEnabled : storeIntroEnabled;
  const endEnabled = propEndEnabled !== undefined ? propEndEnabled : storeEndEnabled;
  const fps = propFps || storeFps;
  const backgroundMusicVolume = propBackgroundMusicVolume !== undefined ? propBackgroundMusicVolume : storeBackgroundMusicVolume;
  const isBackgroundMusicEnabled = propIsBackgroundMusicEnabled !== undefined ? propIsBackgroundMusicEnabled : storeIsBackgroundMusicEnabled;
  
  // Log information for debugging in Lambda environment
  useEffect(() => {
    if (propSegments) {
      console.log(`VideoComposition: Rendering with ${propSegments.length} segments from props`);
      console.log(`Intro enabled: ${introEnabled}, End enabled: ${endEnabled}`);
      console.log(`FPS: ${fps}, Frames per segment: ${framesPerSegment}`);
      console.log(`Duration in frames from props: ${propDurationInFrames}`);
    }
  }, [propSegments, introEnabled, endEnabled, fps, framesPerSegment, propDurationInFrames]);
  
  // Use hooks to get data from other stores
  // Get project ID from URL in browser context, null in Lambda context
  const projectIdFromUrl = useMemo(() => {
    if (typeof window === 'undefined' || propSegments) return undefined;
    const searchParams = new URLSearchParams(window.location.search);
    const projectIdParam = searchParams.get('projectId');
    return projectIdParam || undefined;
  }, [propSegments]);
  
  // Only use display settings when not using props (avoid infinite loops in PreviewDialog)
  const displaySettings = propSegments ? null : useDisplaySettings(projectIdFromUrl);
  const introSettings = displaySettings?.introSettings;
  const endSettings = displaySettings?.endSettings;
  
  const { getCurrentAudio, currentAudioId, audioResources } = useMusicStore();
  const { textOverlays, setProjectId, loadFromServer, projectId } = useTextOverlayStore();
  const { scripts } = useVideoScriptStore();

  const finalTextOverlays = propTextOverlays || textOverlays;
  
  // Load text overlays when in browser context
  useEffect(() => {
    // Skip effects when rendering with static props (Lambda environment)
    if (propSegments) return;
    
    // Get project ID from URL query parameter
    const searchParams = new URLSearchParams(window.location.search);
    const projectIdParam = searchParams.get('projectId');
    const projectIdFromUrl = projectIdParam || null;
    
    if (projectIdFromUrl && projectIdFromUrl !== projectId) {
      setProjectId(projectIdFromUrl);
      loadFromServer();
    }
  }, [propSegments, projectId, setProjectId, loadFromServer]);
  
  // Update videoPlayerStore with data from other stores (only in browser environment)
  useEffect(() => {
    // Skip effects when rendering with static props (Lambda environment)
    if (propSegments) return;

    const backgroundMusic = getCurrentAudio();
    const store = useVideoPlayerStore.getState();
    
    // Update dynamic settings
    store.updateDynamicSettings({
      introEnabled: introSettings?.isEnabled ?? true,
      endEnabled: endSettings?.isEnabled ?? true
    });
    
    // Update background music URL
    store.setBackgroundMusicUrl(backgroundMusic?.url || null);
  }, [
    introSettings?.isEnabled, 
    endSettings?.isEnabled, 
    getCurrentAudio,
    currentAudioId,
    audioResources,
    propSegments
  ]);
  
  // Update scripts data in store (only in browser environment)
  useEffect(() => {
    // Skip effects when rendering with static props (Lambda environment)
    if (propSegments) return;
    if (!scripts || !Array.isArray(scripts) || scripts.length === 0) return;
    
    const store = useVideoPlayerStore.getState();
    
    // Update scripts
    const scriptsMap = scripts.reduce((acc, script) => {
      acc[script.id] = script;
      return acc;
    }, {} as Record<string, any>);
    store.setScripts(scriptsMap);
  }, [scripts, propSegments]);

  return (
    <AbsoluteFill>
      {showWatermark && (
        <div className="absolute top-6 right-6 flex items-center bg-white/40 backdrop-blur-sm px-3 py-2 rounded-lg z-50">
          <Image
            src="https://adsynthetica-local-dev-bucket.s3.us-east-2.amazonaws.com/logo/default/Fylow.png"
            alt="Adsynthetica"
            width={120}
            height={30}
            className="w-32 h-8"
            priority
          />
        </div>
      )}
      
      {/* Background Music - only if URL is provided, valid, and enabled */}
      {backgroundMusicUrl && typeof backgroundMusicUrl === 'string' && isBackgroundMusicEnabled && (
        <Audio 
          src={backgroundMusicUrl} 
          volume={backgroundMusicVolume}
        />
      )}
      
      {/* Intro Screen */}
      {introEnabled && (
        <Sequence from={0} durationInFrames={introBlackScreenFrames}>
          <IntroScreen 
            useSettings={propUseSettings || (introSettings ? { introSettings } : undefined)}
            brandName={brandName}
          />
        </Sequence>
      )}
      
      {/* Main Content - handle safely with type check */}
      {segments && Array.isArray(segments) && segments.map((segment, index) => (
        <Sequence
          key={segment.id}
          from={
            (introEnabled ? introBlackScreenFrames : 0) +
            index * framesPerSegment
          }
          durationInFrames={framesPerSegment}
        >
          <SegmentContent 
            segment={segment as VideoSegment} 
            index={index} 
            scripts={propScripts}
            selectedVoice={selectedVoice}
            getVoiceAudioFile={getVoiceAudioFile}
          />
        </Sequence>
      ))}
      
      {/* End Screen */}
      {endEnabled && segments && Array.isArray(segments) && (
        <Sequence
          from={
            (introEnabled ? introBlackScreenFrames : 0) +
            segments.length * framesPerSegment
          }
          durationInFrames={endScreenFrames}
        >
          <EndScreen 
            useSettings={propUseSettings || (endSettings ? { endSettings } : undefined)}
            brandName={brandName}
          />
        </Sequence>
      )}

      {/* Text Overlays */}
      {finalTextOverlays && finalTextOverlays.map((overlay: TextOverlay) => (
        <TextOverlayComponent 
          key={overlay.id}
          overlay={overlay}
          fps={fps}
        />
      ))}
    </AbsoluteFill>
  );
}; 