import { FC, useEffect, useRef } from 'react';
import { Player, PlayerRef } from '@remotion/player';
import { VideoSegment } from '@/lib/store/videoSegmentStore';
import { useVideoPlayerStore } from '../../../../../lib/store/videoPlayerStore';
import { VideoComposition } from './VideoComposition';

interface ConcatenatedVideoPlayerProps {
  segments: VideoSegment[];
  selectedVoice?: 'blue' | 'pink';
  playerRef: React.RefObject<PlayerRef | null>;
  showWatermark?: boolean;
}

/**
 * Main video player component that renders the concatenated video segments
 * Uses a simplified architecture with individual components for better maintainability
 */
const ConcatenatedVideoPlayer: FC<ConcatenatedVideoPlayerProps> = ({
  segments,
  selectedVoice = 'blue',
  playerRef,
  showWatermark = false
}) => {
  const { 
    setSegments, 
    setSelectedVoice,
    setCurrentFrame,
    totalFrames,
    playerReady,
    fps,
    backgroundMusicVolume,
    isBackgroundMusicEnabled
  } = useVideoPlayerStore();
  
  // Update store when segments or voice changes
  useEffect(() => {
    console.log('Segments updated:', segments.length, 'Total frames:', totalFrames);
    setSegments(segments);
    setSelectedVoice(selectedVoice);
  }, [segments, selectedVoice, setSegments, setSelectedVoice, totalFrames]);

  // Track frame updates
  useEffect(() => {
    const player = playerRef.current;
    if (!player) {
      console.log('Player ref is null');
      return;
    }

    console.log('Player initialized with total frames:', totalFrames);

    const interval = setInterval(() => {
      try {
        const frame = player.getCurrentFrame();
        // console.log('Raw frame from player:', frame);
        
        // Only update if we have a valid frame number
        if (typeof frame === 'number' && !isNaN(frame) && frame >= 0) {
          setCurrentFrame(frame);
        } else {
          console.warn('Invalid frame value:', frame);
        }
      } catch (error) {
        console.error('Error getting current frame:', error);
      }
    }, 1000 / fps); // Update at FPS rate

    return () => {
      console.log('Cleaning up frame tracking interval');
      clearInterval(interval);
    };
  }, [fps, setCurrentFrame, totalFrames, playerRef]);
  
  // Show placeholder when no segments are available
  if (!segments.length) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
        <p className="text-gray-500">No video segments available</p>
      </div>
    );
  }
  
  // Show loading state when player is not ready
  if (!playerReady) {
    return (
      <div className="w-full h-full flex flex-col items-center justify-center bg-gray-100 rounded-lg">
        <div className="w-16 h-16 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin mb-4"></div>
        <p className="text-gray-700 font-medium">Preparing video player...</p>
      </div>
    );
  }
  
  return (
    <div className="mx-auto">
      <Player
        ref={playerRef}
        component={VideoComposition}
        inputProps={{ 
          showWatermark,
          backgroundMusicVolume,
          isBackgroundMusicEnabled
        }}
        durationInFrames={totalFrames}
        fps={fps}
        compositionWidth={1920}
        compositionHeight={1080}
        style={{ 
          width: '100%', 
          height: '100%',
          borderRadius: '0.5rem',
          overflow: 'hidden',
          aspectRatio: '16/9'
        }}
        controls={false}
        autoPlay={false}
        loop={false}
        showVolumeControls={false}
        spaceKeyToPlayOrPause={true}
        clickToPlay={false}
        allowFullscreen={true}
        initiallyShowControls={false}
        renderLoading={() => null}
      />
    </div>
  );
};

export default ConcatenatedVideoPlayer;
