import { FC, useRef, useState, ReactNode } from 'react';
import { useDrag } from 'react-dnd';

interface DraggableItemProps {
  id: number | string;
  itemType: string;
  startPercent: number;
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onDragStart?: () => void;
  onDragEnd?: () => void;
}

/**
 * A reusable draggable item component that can be used for any element
 * that needs to be dragged and positioned on a timeline
 */
const DraggableItem: FC<DraggableItemProps> = ({
  id,
  itemType,
  startPercent,
  children,
  className = '',
  style = {},
  onDragStart,
  onDragEnd
}) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  
  const [{ isDragging }, drag, dragPreview] = useDrag(() => ({
    type: itemType,
    item: (monitor) => {
      // Calculate initial grab position relative to the element's left edge
      const initialClientOffset = monitor.getInitialClientOffset();
      const initialSourceClientOffset = monitor.getInitialSourceClientOffset();
      
      let grabOffset = 0;
      if (initialClientOffset && initialSourceClientOffset && itemRef.current) {
        const rect = itemRef.current.getBoundingClientRect();
        grabOffset = initialClientOffset.x - rect.left;
      }
      
      if (onDragStart) {
        onDragStart();
      }
      
      return { 
        id, 
        type: itemType,
        startPercent,
        grabOffset,
        width: itemRef.current?.offsetWidth || 0
      };
    },
    end: () => {
      if (onDragEnd) {
        onDragEnd();
      }
    },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging()
    })
  }), [id, itemType, startPercent, onDragStart, onDragEnd]);

  return (
    <div
      ref={(node) => {
        itemRef.current = node;
        drag(node);
        dragPreview(node);
      }}
      id={`draggable-${itemType}-${id}`}
      className={`absolute cursor-move transition-all duration-150 ease-in-out
        ${isDragging ? 'opacity-50 scale-105 z-10 shadow-lg' : ''}
        ${isHovered ? 'ring-2 ring-blue-400 shadow-sm' : ''}
        ${className}
      `}
      style={{
        left: `${startPercent}%`,
        ...style
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </div>
  );
};

export default DraggableItem; 