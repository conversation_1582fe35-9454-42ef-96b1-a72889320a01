import { FC, ReactNode, useRef, useState, useEffect } from 'react';
import { useDrop } from 'react-dnd';

interface DropTrackProps {
  children: ReactNode;
  acceptType: string | string[];
  trackLabel?: string;
  trackColor?: string;
  position: { top: number };
  totalFrames: number;
  fps: number;
  onItemDrop: (id: number | string, startTimeInSeconds: number) => void;
  onItemResize?: (id: number | string, durationInSeconds: number) => void;
  onItemResizing?: (id: number | string, durationInSeconds: number) => void;
  emptyMessage?: string;
  isEmpty?: boolean;
}

/**
 * A reusable track component that can accept draggable items
 */
const DropTrack: FC<DropTrackProps> = ({
  children,
  acceptType,
  trackLabel = '',
  trackColor = 'green',
  position,
  totalFrames,
  fps,
  onItemDrop,
  onItemResize,
  onItemResizing,
  emptyMessage = 'Drag items here',
  isEmpty = false
}) => {
  const trackRef = useRef<HTMLDivElement>(null);
  const [dropIndicator, setDropIndicator] = useState<{ position: number, time: number } | null>(null);
  
  const colorClasses = {
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      indicator: 'bg-green-500'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      indicator: 'bg-blue-500'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      indicator: 'bg-purple-500'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      indicator: 'bg-orange-500'
    },
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      indicator: 'bg-red-500'
    }
  };
  
  const colors = colorClasses[trackColor as keyof typeof colorClasses] || colorClasses.green;
  
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: acceptType,
    drop: (item: { 
      id: number | string, 
      grabOffset?: number, 
      width: number, 
      isResizing?: boolean,
      startPercent?: number
    }, monitor) => {
      if (!trackRef.current) return;
      
      const trackRect = trackRef.current.getBoundingClientRect();
      const dropPosition = monitor.getClientOffset();
      
      if (!dropPosition) return;
      
      // Check if this is a resize operation
      if (item.isResizing && onItemResize) {
        // Calculate new width as percentage of timeline
        const endX = dropPosition.x;
        
        // 确保startPercent存在，如果不存在则默认为0
        if (item.startPercent === undefined) {
          console.warn('item.startPercent is undefined, using 0 as default');
        }
        
        const startPercent = item.startPercent || 0;
        const startX = (startPercent / 100) * trackRect.width + trackRect.left;
        
        // 计算新宽度，确保至少为1像素
        const newWidth = Math.max(1, endX - startX);
        const newWidthPercentage = (newWidth / trackRect.width) * 100;
        
        // 转换为秒数
        const durationInSeconds = (newWidthPercentage / 100) * (totalFrames / fps);
        
        // 确保持续时间至少为0.5秒
        const minDuration = 0.5;
        const boundedDuration = Math.max(minDuration, durationInSeconds);
        
        console.log(`Resize: id=${item.id}, startPercent=${startPercent}, newWidth=${newWidth}px, newDuration=${boundedDuration}s`);
        
        // 调用调整大小回调
        onItemResize(item.id, boundedDuration);
      } else {
        // Regular drag operation (existing code)
        // Adjust the drop position to account for the grab offset
        const adjustedX = dropPosition.x - (item.grabOffset || 0);
        
        // Calculate drop position as percentage of timeline
        const dropPercentage = ((adjustedX - trackRect.left) / trackRect.width) * 100;
        
        // Ensure the percentage is within bounds
        const boundedPercentage = Math.max(0, Math.min(100, dropPercentage));
        
        // Convert percentage to frame and then to seconds
        const startFrame = Math.floor((boundedPercentage / 100) * totalFrames);
        const startTimeInSeconds = startFrame / fps;
        
        // Call the onItemDrop callback
        onItemDrop(item.id, startTimeInSeconds);
      }
      
      // Reset drop indicator
      setDropIndicator(null);
    },
    hover: (item: { 
      id: number | string, 
      grabOffset?: number, 
      width: number,
      isResizing?: boolean,
      startPercent?: number 
    }, monitor) => {
      if (!trackRef.current) return;
      
      const trackRect = trackRef.current.getBoundingClientRect();
      const hoverPosition = monitor.getClientOffset();
      
      if (!hoverPosition) return;
      
      if (item.isResizing) {
        // 计算新的位置百分比
        const hoverPercentage = ((hoverPosition.x - trackRect.left) / trackRect.width) * 100;
        const boundedPercentage = Math.max(0, Math.min(100, hoverPercentage));
        
        // 计算当前时间点（秒）
        const hoverTimeInSeconds = (boundedPercentage / 100) * (totalFrames / fps);
        
        // 更新指示器
        setDropIndicator({
          position: boundedPercentage,
          time: hoverTimeInSeconds
        });
        
        // 计算调整后的持续时间并通过回调通知父组件
        if (onItemResizing) {
          const startX = ((item.startPercent || 0) / 100) * trackRect.width + trackRect.left;
          const newWidth = Math.max(1, hoverPosition.x - startX);
          const newWidthPercentage = (newWidth / trackRect.width) * 100;
          const durationInSeconds = (newWidthPercentage / 100) * (totalFrames / fps);
          
          // 确保最小持续时间
          const minDuration = 0.5;
          const boundedDuration = Math.max(minDuration, durationInSeconds);
          
          // 通知父组件正在调整大小
          onItemResizing(item.id, boundedDuration);
        }
      } else {
        // Regular drag hover (existing code)
        // Adjust hover position for grab offset
        const adjustedX = hoverPosition.x - (item.grabOffset || 0);
        
        // Calculate hover position as percentage of timeline
        const hoverPercentage = ((adjustedX - trackRect.left) / trackRect.width) * 100;
        
        // Bound the percentage between 0 and 100
        const boundedPercentage = Math.max(0, Math.min(100, hoverPercentage));
        
        // Convert percentage to seconds
        const hoverTimeInSeconds = (boundedPercentage / 100) * (totalFrames / fps);
        
        // Update drop indicator
        setDropIndicator({
          position: boundedPercentage,
          time: hoverTimeInSeconds
        });
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop()
    })
  }), [totalFrames, fps, onItemDrop, onItemResize, onItemResizing]);

  // Format time in seconds to MM:SS format
  const formatTime = (seconds: number) => {
    const min = Math.floor(seconds / 60);
    const sec = Math.floor(seconds % 60);
    return `${min}:${sec.toString().padStart(2, '0')}`;
  };

  // When drag ends, reset the drop indicator
  useEffect(() => {
    if (!isOver && !canDrop) {
      setDropIndicator(null);
    }
  }, [isOver, canDrop]);

  return (
    <div 
      ref={(node) => {
        trackRef.current = node;
        drop(node);
      }}
      className={`absolute w-full h-[30px] transition-colors duration-200 
        ${isOver ? colors.bg : ''} 
        ${canDrop ? 'bg-opacity-70' : ''}
        rounded-sm border border-transparent hover:${colors.border}
      `}
      style={{
        top: `${position.top}px`
      }}
    >
      {/* Track label */}
      {trackLabel && (
        <div className="absolute -left-14 top-0 h-full flex items-center">
          <span className="text-[10px] text-gray-500">{trackLabel}</span>
        </div>
      )}
      
      {/* Drop position indicator */}
      {dropIndicator && (
        <div 
          className={`absolute top-0 h-full w-[2px] ${colors.indicator} z-20 pointer-events-none`}
          style={{ left: `${dropIndicator.position}%` }}
        >
          <div className={`absolute -top-5 left-1/2 -translate-x-1/2 ${colors.indicator} text-white text-[9px] px-1 py-0.5 rounded whitespace-nowrap`}>
            {formatTime(dropIndicator.time)}
          </div>
        </div>
      )}
      
      {children}
      
      {/* Empty state indicator */}
      {isEmpty && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-xs pointer-events-none">
          {emptyMessage}
        </div>
      )}
    </div>
  );
};

export default DropTrack; 