import { Edit2, AlertCircle, ChevronUp, ChevronDown, Film, Play, Pause, Loader2, CheckCircle, Maximize2, Volume2, VolumeX, Download, Share2, MessageSquare } from "lucide-react";
import { FC, useState, useEffect, useCallback, memo, useRef, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";

import { useVideoPlayerStore } from '../../../../../lib/store/videoPlayerStore';
import { useVideoScriptStore } from '../../../../../lib/store/videoScriptStore';
import { useVideoSegmentStore, VideoSegment } from '../../../../../lib/store/videoSegmentStore';
import { useMusicStore } from '../../../../../lib/store/musicStore';
import { But<PERSON> } from '@/components/ui/button';
import ConcatenatedVideoPlayer from "./ConcatenatedVideoPlayer";
import Timeline from "./Timeline";
import { PlayerRef } from "@remotion/player";
import FeedbackDialog from "./FeedbackDialog";
import { PreviewDialog } from "./PreviewDialog";

interface PreviewProps {
  projectName?: string;
  onProjectNameChange?: (name: string) => void;
  currentScriptId?: string | null;
  currentSegmentId?: string | null;
  onSegmentSelect?: (id: string, scriptId: string) => void;
  fetchAllCurrentVersionSegments?: () => Promise<VideoSegment[]>;
  currentVersionSegments?: VideoSegment[]; 
  selectedVoice?: 'blue' | 'pink';
  onVoiceChange?: (voice: 'blue' | 'pink') => void;
  isGeneratingSegments?: boolean;
  onGenerateSegment?: () => Promise<any>;
  scripts?: any[]; // Add scripts property to access all scripts
  voicesGenerated?: boolean; // Track if voices have been generated
  textOverlaysCreated?: boolean; // Track if text overlays have been created
  segmentsGenerated?: boolean; // Track if video segments have been generated
  allGenerationComplete?: boolean; // Track if all generation steps are complete
  isTimelineCollapsed?: boolean; // Add new prop for timeline collapse state
}

// Simplified loading indicator component
const SimpleLoadingIndicator = () => {
  return (
    <div className="h-full flex items-center justify-center flex-col">
      <Loader2 className="h-12 w-12 animate-spin text-blue-500 mb-4" />
      <h3 className="text-lg font-medium mb-2">Generating your video</h3>
      <p className="text-gray-500">This may take a few minutes</p>
    </div>
  );
};

export const Preview: FC<PreviewProps> = memo(({
  projectName, 
  onProjectNameChange,
  currentScriptId,
  currentSegmentId,
  onSegmentSelect,
  fetchAllCurrentVersionSegments,
  currentVersionSegments = [],
  selectedVoice = 'blue',
  onVoiceChange,
  isGeneratingSegments = false,
  onGenerateSegment,
  scripts = [],
  voicesGenerated = false,
  textOverlaysCreated = false,
  segmentsGenerated = false,
  allGenerationComplete = false,
  isTimelineCollapsed = true
}: PreviewProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingName, setEditingName] = useState(projectName || "");
  const [videoError, setVideoError] = useState<string | null>(null);
  const [showWatermark, setShowWatermark] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);
  const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false);
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId') ? parseInt(searchParams.get('projectId')!) : undefined;

  // Update local state when projectName prop changes or editing starts
  useEffect(() => {
    if (!isEditing) {
      setEditingName(projectName || "");
    }
  }, [isEditing, projectName]);

  // Get video player state
  const { 
    totalFrames, 
    fps, 
    currentFrame,
    backgroundMusicUrl,
    backgroundMusicVolume,
    isBackgroundMusicEnabled,
    setBackgroundMusicVolume,
    setBackgroundMusicEnabled
  } = useVideoPlayerStore();

  const currentScript = useVideoScriptStore(state => 
    currentScriptId ? state.scripts.find(s => s.id === currentScriptId) : null
  );
  
  const segments = useVideoSegmentStore(state => state.segments);
  const currentSegment = useVideoSegmentStore(state => 
    currentSegmentId ? state.segments.find(s => s.id === currentSegmentId) : null
  );
  
  // Check if all scripts have associated video 
  const allScriptsHaveSegments = useMemo(() => {
    // First, ensure scripts is an array
    if (!Array.isArray(scripts) || scripts.length === 0) return true;
    if (!currentVersionSegments || currentVersionSegments.length === 0) return false;
    
    // Create a set of script IDs that have video segments
    const scriptIdsWithSegments = new Set(
      currentVersionSegments.map(segment => segment.scriptId)
    );
    
    // Check if all script IDs are in the set
    return scripts.every(script => scriptIdsWithSegments.has(script.id));
  }, [scripts, currentVersionSegments]);
  
  const playerRef = useRef<PlayerRef>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Reset video error when segment changes
  useEffect(() => {
    setVideoError(null);
  }, [currentVersionSegments]);

  // Handle segment selection
  const handleSegmentSelect = useCallback((segmentId: string, scriptId: string) => {
    if (onSegmentSelect) {
      onSegmentSelect(segmentId, scriptId);
    }
  }, [onSegmentSelect]);

  // Format time display
  const formatTime = (frame: number) => {
    const seconds = Math.floor(frame / fps);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle play/pause
  const handlePlayPause = () => {
    if (!playerRef.current) return;
    
    if (isPlaying) {
      playerRef.current.pause();
      setIsPlaying(false);
    } else {
      playerRef.current.play();
      setIsPlaying(true);
    }
  };

  // Handle seek when clicking the progress bar
  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!playerRef.current) return;
    
    const progressBar = e.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const relativeX = e.clientX - rect.left;
    const percentage = relativeX / rect.width;
    
    // convert percentage to frame number
    const targetFrame = Math.floor(totalFrames * percentage);
    
    // use playerRef to jump to the specified frame
    playerRef.current.seekTo(targetFrame);
  };

  // Handle fullscreen toggle
  const handleFullscreen = () => {
    if (!playerRef.current) return;
    
    if (document.fullscreenElement) {
      playerRef.current.exitFullscreen();
    } else {
      playerRef.current.requestFullscreen();
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    if (!playerRef.current) return;
    
    const onFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    const player = playerRef.current;
    player.addEventListener('fullscreenchange', onFullscreenChange);
    
    return () => {
      player.removeEventListener('fullscreenchange', onFullscreenChange);
    };
  }, [playerRef.current]);

  // Update play state when video ends
  useEffect(() => {
    if (currentFrame >= totalFrames - 1) {
      setIsPlaying(false);
    }
  }, [currentFrame, totalFrames]);

  // Handle project name input change
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only update local state, not parent component yet
    setEditingName(e.target.value);
  };

  // Handle completion of editing (on blur or enter key)
  const handleEditComplete = () => {
    setIsEditing(false);
    // Only call the update function if name has actually changed
    if (editingName.trim() !== "" && editingName !== projectName) {
      onProjectNameChange?.(editingName);
    }
  };

  const { getCurrentAudio } = useMusicStore();
  const currentBackgroundMusic = getCurrentAudio();

  // Toggle feedback dialog
  const handleToggleFeedback = () => {
    setIsFeedbackOpen(!isFeedbackOpen);
  };

  // Handle download button click
  const handleDownloadClick = () => {
    setIsDownloadDialogOpen(true);
  };

  return (
    <main className="flex-1 flex flex-col h-full">
      <div className="p-4 flex items-center justify-end">
        <div className="flex items-center gap-4 mr-8">
          {/* Download Button */}
          <button 
            onClick={handleDownloadClick}
            className="flex items-center justify-center bg-cyan-600 hover:bg-cyan-700 text-white transition-colors p-3 rounded-md"
          >
            <Download size={20} />
          </button>
          
          {/* Publish Button */}
          <button className="flex items-center justify-center gap-2 bg-cyan-600 hover:bg-cyan-700 text-white transition-colors px-6 py-3 rounded-md">
            <Share2 size={18} />
            <span className="font-medium">Publish</span>
          </button>
          
          {/* Feedback Button */}
          <button 
            onClick={handleToggleFeedback}
            className="flex items-center justify-center gap-2 text-cyan-600 hover:text-cyan-700 transition-colors"
          >
            <MessageSquare size={18} />
            <span className="font-medium">Feedback</span>
          </button>
        </div>
      </div>
      
      {/* Full-size video container */}
      <div className="flex-1 bg- relative">
        {allGenerationComplete ? (
          // Only show video player when all generation is complete
          currentVersionSegments.length > 0 && !videoError ? (
            <div className="w-full h-full">
              <div className="mx-auto mb-4 max-w-lg">
                {isEditing ? (
                  <div className="relative flex items-center w-full rounded-md overflow-hidden transition-all duration-200 focus-within:ring-2 focus-within:ring-cyan-400 focus-within:ring-opacity-50 bg-white shadow-sm">
                    <input
                      type="text"
                      placeholder="Enter project name"
                      className="w-full px-3 py-2 text-base font-medium outline-none border-none"
                      value={editingName}
                      onChange={handleNameChange}
                      onBlur={handleEditComplete}
                      onKeyDown={(e) => {
                        // Save when user presses Enter
                        if (e.key === 'Enter') {
                          handleEditComplete();
                        }
                      }}
                      autoFocus
                    />
                    <button
                      onClick={handleEditComplete}
                      className="absolute right-2 text-cyan-500 hover:text-cyan-600 transition-colors"
                      title="Save project name"
                    >
                      <CheckCircle size={18} />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2 group">
                    <h2 className="text-xl font-medium text-gray-800 group-hover:text-gray-900 transition-colors truncate">
                      {projectName || "Untitled Project"}
                    </h2>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="opacity-70 group-hover:opacity-100 p-1 rounded-full bg-gray-50 text-gray-500 hover:text-cyan-500 hover:bg-gray-100 transition-all duration-200"
                      title="Edit project name"
                    >
                      <Edit2 size={16} />
                    </button>
                  </div>
                )}
              </div>
            
              <div className="w-[65%] border-1 border-gray-200 rounded-lg mx-auto mt-0">
                <ConcatenatedVideoPlayer
                  segments={currentVersionSegments}
                  selectedVoice={selectedVoice}
                  playerRef={playerRef}
                  showWatermark={showWatermark}
                />
                <div className="mx-auto my-2" style={{ width: '100%', height: '30px'}}>

                  {/* Playback controls */}
                  <div className="flex items-center justify-between px-4 mx-30">
                    {/* left: play/pause button */}
                    <div className="flex items-center">
                      <button
                        onClick={handlePlayPause}
                        className="w-10 h-10 flex items-center justify-center rounded-full bg-cyan-600 text-white hover:bg-cyan-700 transition-colors"
                      >
                        {isPlaying ? <Pause size={20} /> : <Play size={20} />}
                      </button>
                    </div>
                    
                    {/* middle progress bar */}
                    <div className="flex-1 mx-4">
                      <div 
                        className="relative h-1 bg-gray-200 rounded-full cursor-pointer hover:h-2 transition-all"
                        onClick={handleSeek}
                      >
                        <div 
                          className="absolute h-full bg-cyan-600 rounded-full" 
                          style={{ width: `${(currentFrame / totalFrames) * 100}%` }}
                        />
                      </div>
                    </div>
                    
                    {/* right: time display and fullscreen button */}
                    <div className="flex items-center space-x-4">
                      <div className="text-sm font-medium text-gray-400">
                        {formatTime(currentFrame)} / {formatTime(totalFrames)}
                      </div>
                      <button
                        onClick={handleFullscreen}
                        className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-blue-500 transition-colors"
                        title={isFullscreen ? "Exit fullscreen" : "Fullscreen"}
                      >
                        <Maximize2 size={16} />
                      </button>
                    </div>
                  </div>

                  
                  
                </div>
                {/* Background Music Controls */}
                <div className="flex items-center justify-between px-4 mx-30 mt-2 border-gray-200 h-10">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-600">Background Music:</span>
                      <span className="text-sm text-gray-500">
                        {currentBackgroundMusic?.name || 'None'}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      {/* Volume Control */}
                      <div className="flex items-center space-x-2">
                        <Volume2 className="h-4 w-4 text-gray-500" />
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={backgroundMusicVolume}
                          onChange={(e) => setBackgroundMusicVolume(parseFloat(e.target.value))}
                          className="w-24 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                      
                      {/* On/Off Toggle */}
                      <button
                        onClick={() => setBackgroundMusicEnabled(!isBackgroundMusicEnabled)}
                        className={cn(
                          "px-3 py-1 rounded-full text-sm font-medium transition-colors",
                          isBackgroundMusicEnabled
                            ? "bg-blue-100 text-blue-600 hover:bg-blue-200"
                            : "bg-gray-100 text-gray-500 hover:bg-gray-200"
                        )}
                      >
                        {isBackgroundMusicEnabled ? "On" : "Off"}
                      </button>
                    </div>
                </div>

                
              </div>
              {/* Regenerate Video Button */}
              <div className="flex items-center justify-center">
                <Button className="bg-cyan-600 hover:bg-cyan-700 text-white mt-4" onClick={onGenerateSegment}>
                  Regenerate
                </Button>
              </div>
            </div>
          ) : videoError ? (
            <div className="h-full flex items-center justify-center text-center text-red-500">
              <div>
                <AlertCircle size={48} className="mx-auto mb-2" />
                <p>{videoError}</p>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-center">
              <div>
                <p>No video segments available</p>
                {onGenerateSegment && (
                  <Button 
                    className="bg-blue-500 hover:bg-blue-600 text-white mt-4" 
                    onClick={onGenerateSegment}
                  >
                    Generate Videos For All Scripts
                  </Button>
                )}
              </div>
            </div>
          )
        ) : (
          // Show simple loading indicator until all generation is complete
          <SimpleLoadingIndicator />
        )}
      </div>

    

      {/* Timeline Component */}
      {!isTimelineCollapsed && (
            <Timeline 
              segments={currentVersionSegments}
              totalFrames={totalFrames}
              fps={fps}
              currentFrame={currentFrame}
              playerRef={playerRef}
            />
      )}

      {/* Feedback Dialog */}
      <FeedbackDialog 
        isOpen={isFeedbackOpen}
        onClose={() => setIsFeedbackOpen(false)}
      />

      {/* Download Preview Dialog */}
      <PreviewDialog 
        isOpen={isDownloadDialogOpen}
        onClose={() => setIsDownloadDialogOpen(false)}
      />
    </main>
  );
});

export default Preview;