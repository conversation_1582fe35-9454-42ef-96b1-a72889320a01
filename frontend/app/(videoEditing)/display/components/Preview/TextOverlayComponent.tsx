import { FC } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOverlay } from '@/lib/store/textOverlayStore';

interface TextOverlayComponentProps {
  overlay: TextOverlay;
  fps: number;
}

/**
 * Component that renders a text overlay at the specified position and time
 * Only renders when the current frame is within the overlay's time range
 * Position is based on the bottom-left corner of the text
 */
export const TextOverlayComponent: FC<TextOverlayComponentProps> = ({ 
  overlay, 
  fps 
}) => {
  const frame = useCurrentFrame();
  const startFrame = overlay.startTime * fps;
  const endFrame = startFrame + (overlay.duration * fps);
  
  // Only render if current frame is within the overlay's time range
  if (frame < startFrame || frame > endFrame) {
    return null;
  }
  
  // Add fade in/out effect
  const fadeDurationFrames = Math.min(0.5 * fps, (endFrame - startFrame) / 2);
  const fadeOutStartFrame = endFrame - fadeDurationFrames;

  const opacity = interpolate(
    frame,
    [startFrame, startFrame + fadeDurationFrames, fadeOutStartFrame, endFrame],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );
  
  // Calculate position based on alignment
  let horizontalPosition;
  
  switch (overlay.alignment) {
    case 'left':
      horizontalPosition = `${overlay.position.x * 100}%`;
      break;
    case 'right':
      horizontalPosition = `${overlay.position.x * 100}%`;
      break;
    case 'center':
    default:
      horizontalPosition = `${overlay.position.x * 100}%`;
      break;
  }
  
  const positionStyles = {
    position: 'absolute' as const,
    left: horizontalPosition,
    bottom: `${(1 - overlay.position.y) * 100}%`, // Convert y position to bottom
    textAlign: overlay.alignment as any,
    color: overlay.color,
    fontSize: `${overlay.fontSize}px`,
    fontWeight: 'bold' as const,
    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
    width: '80%',
    opacity,
  };
  
  return (
    <div style={positionStyles}>
      {overlay.text}
    </div>
  );
}; 