import { FC, useState } from 'react';
import { Edit2 } from 'lucide-react';

interface ProjectHeaderProps {
  projectName?: string;
  onProjectNameChange?: (name: string) => void;
}

export const ProjectHeader: FC<ProjectHeaderProps> = ({
  projectName,
  onProjectNameChange
}) => {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <div className="p-4 border-b flex items-center justify-between">
      {isEditing ? (
        <input 
          type="text" 
          value={projectName || ''} 
          onChange={(e) => onProjectNameChange && onProjectNameChange(e.target.value)}
          className="border rounded px-2 py-1 w-full"
          onBlur={() => setIsEditing(false)}
          autoFocus
        />
      ) : (
        <h2 className="text-lg font-semibold flex items-center">
          {projectName || 'Untitled Project'}
          <button 
            onClick={() => setIsEditing(true)} 
            className="ml-2 text-gray-400 hover:text-gray-600"
          >
            <Edit2 size={16} />
          </button>
        </h2>
      )}
    </div>
  );
};
