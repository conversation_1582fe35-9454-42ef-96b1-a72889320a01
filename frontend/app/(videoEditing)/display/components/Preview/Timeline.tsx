import { FC, useEffect, useRef, useCallback, useState } from 'react';
import { VideoSegment } from '@/lib/store/videoSegmentStore';
import { useVideoPlayerStore } from '../../../../../lib/store/videoPlayerStore';
import { useMusicStore } from '@/lib/store/musicStore';
import { useTextOverlayStore, TextOverlay } from '@/lib/store/textOverlayStore';
import { useDrag, useDrop, DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import ResizableItem from './ResizableItem';
import DropTrack from './DropTrack';
import { PlayerRef } from "@remotion/player";

// Define drag item type
const ItemTypes = {
  TEXT_OVERLAY: 'textOverlay'
};

interface DraggableTextOverlayProps {
  overlay: TextOverlay;
  startPercent: number;
  widthPercent: number;
  totalFrames: number;
  fps: number;
  index: number;
  temporaryDuration?: number;
}

interface TextTrackProps {
  children: React.ReactNode;
  fps: number;
  totalFrames: number;
  resizingOverlay: { id: string; tempDuration: number } | null;
  onResizingChange: (id: number | string, durationInSeconds: number) => void;
  onResizeEnd: (id: number | string, durationInSeconds: number) => void;
}

interface TimelineProps {
  segments: VideoSegment[];
  totalFrames: number;
  fps: number;
  currentFrame: number;
  playerRef?: React.RefObject<PlayerRef | null>;
}

// 计算基于持续时间的宽度百分比
const calculateWidthPercent = (duration: number, fps: number, totalFrames: number): number => {
  return (duration * fps / totalFrames) * 100;
};

// Text overlay component using ResizableItem
const DraggableTextOverlay: FC<DraggableTextOverlayProps> = ({ 
  overlay, 
  startPercent, 
  widthPercent, 
  totalFrames, 
  fps, 
  index,
  temporaryDuration
}) => {
  const { updateTextOverlay } = useTextOverlayStore();
  const [isHovered, setIsHovered] = useState(false);
  
  // 计算当前应该显示的宽度百分比
  const currentWidthPercent = temporaryDuration !== undefined
    ? calculateWidthPercent(temporaryDuration, fps, totalFrames)
    : widthPercent;
  
  // Format time in seconds to MM:SS format
  const formatTime = (seconds: number) => {
    const min = Math.floor(seconds / 60);
    const sec = Math.floor(seconds % 60);
    return `${min}:${sec.toString().padStart(2, '0')}`;
  };

  // 空的resize回调，实际的大小调整已经由TextTrack通过handleTextOverlayResize处理
  const handleResizeEnd = () => {
    // 实际的调整大小操作由DropTrack通过onItemResize回调处理
    // 这个函数只是为了满足组件接口
  };

  // 计算要显示的持续时间
  const displayDuration = temporaryDuration !== undefined ? temporaryDuration : overlay.duration;

  return (
    <ResizableItem
      id={overlay.id}
      itemType={ItemTypes.TEXT_OVERLAY}
      startPercent={startPercent}
      widthPercent={currentWidthPercent}
      className="h-full bg-green-200 border border-green-300 rounded-sm flex items-center justify-center"
      handleColor="bg-green-400"
      onResizeEnd={handleResizeEnd}
    >
      <div 
        className="w-full h-full flex items-center justify-center relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="text-[10px] text-green-600 font-medium truncate px-1">
          {overlay.text}
        </div>
        
        {/* Time indicator visible on hover */}
        {isHovered && (
          <div className="absolute -bottom-4 left-0 text-[9px] bg-black text-white px-1 rounded whitespace-nowrap">
            {formatTime(overlay.startTime)} - {formatTime(overlay.startTime + displayDuration)}
          </div>
        )}
      </div>
    </ResizableItem>
  );
};

// Text track using DropTrack
const TextTrack: FC<TextTrackProps> = ({ 
  children, 
  fps, 
  totalFrames,
  resizingOverlay,
  onResizingChange,
  onResizeEnd
}) => {
  const { textOverlays, updateTextOverlay, saveToServer } = useTextOverlayStore();
  
  // Handle position change (drop)
  const handleTextOverlayDrop = (id: number | string, startTimeInSeconds: number) => {
    const stringId = String(id); // 统一转换为字符串
    const overlay = textOverlays.find((o: TextOverlay) => o.id === stringId);
    if (overlay) {
      updateTextOverlay(overlay.id, {
        startTime: startTimeInSeconds
      });
      // Save changes to server
      saveToServer();
    }
  };

  return (
    <DropTrack
      acceptType={ItemTypes.TEXT_OVERLAY}
      trackLabel="Text"
      trackColor="green"
      position={{ top: 80 }}
      totalFrames={totalFrames}
      fps={fps}
      onItemDrop={handleTextOverlayDrop}
      onItemResize={onResizeEnd}
      onItemResizing={onResizingChange}
      isEmpty={textOverlays.length === 0}
      emptyMessage="Drag text overlays here"
    >
      {children}
    </DropTrack>
  );
};

/**
 * A simple timeline component that displays video segments and current playback position
 * Renders a horizontal timeline with time markers and segment blocks
 */
const Timeline: FC<TimelineProps> = ({
  segments,
  totalFrames,
  fps,
  currentFrame,
  playerRef
}) => {
  // Get frames per segment and other settings from store
  const { 
    framesPerSegment,
    introEnabled,
    endEnabled,
    introBlackScreenFrames,
    endScreenFrames,
    backgroundMusicUrl,
    setCurrentFrame
  } = useVideoPlayerStore();

  // Get current background music from music store
  const { getCurrentAudio } = useMusicStore();
  const backgroundMusic = getCurrentAudio();

  // Get text overlays
  const { textOverlays, updateTextOverlay, saveToServer, projectId, setProjectId, loadFromServer } = useTextOverlayStore();
  
  // Load text overlays when component mounts
  useEffect(() => {
    // Get project ID from URL query parameter
    const searchParams = new URLSearchParams(window.location.search);
    const projectIdParam = searchParams.get('projectId');
    const projectIdFromUrl = projectIdParam ? projectIdParam : null;
    
    if (projectIdFromUrl && projectIdFromUrl !== projectId) {
      setProjectId(projectIdFromUrl);
      loadFromServer();
    }
  }, [projectId, setProjectId, loadFromServer]);
  
  // add state to track resizing overlay
  const [resizingOverlay, setResizingOverlay] = useState<{
    id: string;
    tempDuration: number;
  } | null>(null);
  
  // Handle resizing - temporary updates during drag
  const handleTextOverlayResizing = (id: number | string, durationInSeconds: number) => {
    // ensure ID type compatibility
    const stringId = String(id);
    // update resizing state
    setResizingOverlay({
      id: stringId,
      tempDuration: durationInSeconds
    });
  };
  
  // Handle resize - final update when drag ends
  const handleTextOverlayResize = (id: number | string, durationInSeconds: number) => {
    // ensure ID type compatibility
    const stringId = String(id);
    const overlay = textOverlays.find((o: TextOverlay) => o.id === stringId);
    if (overlay) {
      updateTextOverlay(overlay.id, {
        duration: durationInSeconds
      });
      // reset resizing state
      setResizingOverlay(null);
      // Save changes to server
      saveToServer();
    }
  };

  // Convert frames to seconds for time display
  const totalSeconds = Math.ceil(totalFrames / fps);
  
  // Calculate time markers (one marker every second)
  const timeMarkers = Array.from({ length: totalSeconds }, (_, i) => i);

  // Calculate intro position if enabled
  const introPosition = introEnabled ? {
    startPercent: 0,
    widthPercent: (introBlackScreenFrames / totalFrames) * 100
  } : null;

  // Calculate segment positions based on time
  const segmentPositions = segments.map((_, index) => {
    const startFrame = introEnabled ? 
      introBlackScreenFrames + (index * framesPerSegment) :
      index * framesPerSegment;
    
    return {
      startPercent: (startFrame / totalFrames) * 100,
      widthPercent: (framesPerSegment / totalFrames) * 100
    };
  });

  // Calculate end position if enabled
  const endPosition = endEnabled ? {
    startPercent: ((totalFrames - endScreenFrames) / totalFrames) * 100,
    widthPercent: (endScreenFrames / totalFrames) * 100
  } : null;

  // Calculate text overlay positions
  const textOverlayPositions = textOverlays.map((overlay: TextOverlay) => ({
    startPercent: (overlay.startTime * fps / totalFrames) * 100,
    widthPercent: (overlay.duration * fps / totalFrames) * 100,
    overlay
  }));

  // Format time for display
  const formatTime = (seconds: number) => {
    const min = Math.floor(seconds / 60);
    const sec = Math.floor(seconds % 60);
    return `${min}:${sec.toString().padStart(2, '0')}`;
  };

  // Handle timeline click to set playhead position
  const handleTimelineClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    const timelineElement = e.currentTarget;
    const rect = timelineElement.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const widthPercent = (clickX / rect.width) * 100;
    
    // Convert percent to frame
    const targetFrame = Math.round((widthPercent / 100) * totalFrames);
    
    // Update the current frame in the store
    setCurrentFrame(targetFrame);
    
    // Seek to the target frame if player reference is available
    if (playerRef?.current) {
      playerRef.current.seekTo(targetFrame);
    }
  }, [totalFrames, setCurrentFrame, playerRef]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-[160px] w-full border-t-2 border-gray-200 bg-gray-50 flex">
        {/* Track labels fixed column */}
        {/* <div className="w-[80px] flex-shrink-0 border-r border-gray-200 relative">
          <div className="absolute top-[38px] h-[30px] w-full flex items-center justify-start px-2">
            <span className="text-[10px] text-gray-600 font-medium">Segments</span>
          </div>
          {backgroundMusic && (
            <div className="absolute top-[120px] h-[30px] w-full flex items-center justify-start px-2">
              <span className="text-[10px] text-gray-600 font-medium">Music</span>
            </div>
          )}
        </div> */}
        
        {/* Timeline content scrollable area */}
        <div className="flex-grow overflow-x-auto">
          {/* Main timeline container with fixed width */}
          <div className="relative h-full p-2" 
            style={{ width: 'max(100%, 800px)' }}
            onClick={handleTimelineClick}
          >
            {/* Time markers */}
            <div className="w-full h-6 relative mb-2">
              {timeMarkers.map((second) => (
                <div
                  key={second}
                  className="absolute border-l border-gray-300 h-full"
                  style={{
                    left: `${(second * fps / totalFrames) * 100}%`
                  }}
                >
                  <span className="absolute -left-2 -top-2 text-[10px] text-gray-500">
                    {second}s
                  </span>
                </div>
              ))}
            </div>
            
            {/* Segments visualization */}
            <div className="absolute top-[30px] w-full h-[40px]">
              {/* Intro block */}
              {introPosition && (
                <div
                  className="absolute h-full bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center hover:bg-gray-100 transition-colors"
                  style={{
                    left: `${introPosition.startPercent}%`,
                    width: `${introPosition.widthPercent}%`
                  }}
                >
                  <div className="text-[10px] text-gray-600 font-medium truncate px-1">
                    Intro
                  </div>
                </div>
              )}

              {/* Video segments */}
              {segments.map((segment, index) => {
                const { startPercent, widthPercent } = segmentPositions[index];
                
                return (
                  <div
                    key={segment.id}
                    className="absolute h-full bg-blue-200 border border-blue-300 rounded-sm flex items-center justify-center hover:bg-blue-100 transition-colors"
                    style={{
                      left: `${startPercent}%`,
                      width: `${widthPercent}%`
                    }}
                  >
                    <div className="text-[10px] text-gray-600 font-medium truncate px-1">
                      {`Segment ${index + 1}`}
                    </div>
                  </div>
                );
              })}

              {/* End block */}
              {endPosition && (
                <div
                  className="absolute h-full bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center hover:bg-gray-100 transition-colors"
                  style={{
                    left: `${endPosition.startPercent}%`,
                    width: `${endPosition.widthPercent}%`
                  }}
                >
                  <div className="text-[10px] text-gray-600 font-medium truncate px-1">
                    End
                  </div>
                </div>
              )}
            </div>

            {/* Text overlay track */}
            <TextTrack
              fps={fps}
              totalFrames={totalFrames}
              resizingOverlay={resizingOverlay}
              onResizingChange={handleTextOverlayResizing}
              onResizeEnd={handleTextOverlayResize}
            >
              {textOverlayPositions.map(({ startPercent, widthPercent, overlay }: { startPercent: number, widthPercent: number, overlay: TextOverlay }, index: number) => (
                <DraggableTextOverlay 
                  key={overlay.id}
                  overlay={overlay}
                  startPercent={startPercent}
                  widthPercent={widthPercent}
                  totalFrames={totalFrames}
                  fps={fps}
                  index={index}
                  temporaryDuration={resizingOverlay && resizingOverlay.id === overlay.id ? resizingOverlay.tempDuration : undefined}
                />
              ))}
            </TextTrack>
            
            {/* Background music track */}
            {backgroundMusic && (
              <div className="absolute top-[120px] w-full h-[30px]">
                <div
                  className="absolute h-full w-full bg-purple-200 border border-purple-300 rounded-sm flex items-center justify-center hover:bg-purple-100 transition-colors"
                >
                  <div className="text-[10px] text-purple-600 font-medium truncate px-1">
                    Background Music
                  </div>
                </div>
              </div>
            )}
            
            {/* Frame display */}
            <div className="absolute top-[160px] left-0 text-[10px] text-gray-500">
              Frame: {currentFrame} / {totalFrames}
            </div>
            
            {/* Playhead indicator */}
            <div 
              className="absolute top-0 w-[2px] h-full bg-blue-500 z-30"
              style={{
                left: `${(currentFrame / totalFrames) * 100}%`,
                transition: 'left 0.1s linear'
              }}
            />
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

export default Timeline; 