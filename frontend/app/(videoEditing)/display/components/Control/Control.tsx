import React, { memo, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useVideoScriptStore, VideoScript } from '@/lib/store/videoScriptStore';
import { useVideoSegmentStore } from '@/lib/store/videoSegmentStore';
import { useMusicStore } from '@/lib/store/musicStore';
import { useVideoPlayerStore } from '@/lib/store/videoPlayerStore';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { HelpCircle, AudioLines, Info } from "lucide-react";

export interface ControlProps {
  currentScriptId?: string | null;
  currentSegmentId?: string | null;
  onCreateSegment?: () => void;
  onVoiceChange?: (voice: 'blue' | 'pink') => void;
  onToggleTimeline?: () => void;
}

// Export the Control component interface for refs
export interface ControlRef {
  generateAllBothGendersAudio: () => Promise<void>;
}

// Use forwardRef to make the component accept a ref
export const Control = forwardRef<ControlRef, ControlProps>((
  {
    currentScriptId,
    currentSegmentId,
    onCreateSegment,
    onVoiceChange,
    onToggleTimeline
  },
  ref
) => {
  // Get current script and segment
  const currentScript = useVideoScriptStore(state => 
    currentScriptId ? state.scripts.find(s => s.id === currentScriptId) : null
  );
  
  const currentSegment = useVideoSegmentStore(state => 
    currentSegmentId ? state.segments.find(s => s.id === currentSegmentId) : null
  );

  // Get all scripts
  const scripts = useVideoScriptStore(state => state.scripts);
  
  // Get voice selection from store and local state for UI
  const { selectedVoice, setSelectedVoice } = useVideoPlayerStore();
  const [localSelectedVoice, setLocalSelectedVoice] = useState<'blue' | 'pink'>(selectedVoice);
  
  // State for audio generation
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);
  const [isGeneratingAllAudio, setIsGeneratingAllAudio] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Store access
  const { audioResources } = useMusicStore();
  const { updateScript } = useVideoScriptStore();

  // Sync local state with store state
  useEffect(() => {
    setLocalSelectedVoice(selectedVoice);
  }, [selectedVoice]);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (errorMessage || successMessage) {
      const timer = setTimeout(() => {
        setErrorMessage(null);
        setSuccessMessage(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage, successMessage]);

  // Determine if current script is the first one in order
  const isFirstScript = currentScript && scripts.some(s => 
    s.projectId === currentScript.projectId && 
    s.segmentNumber === 1 && 
    s.id === currentScript.id
  );

  // Get audio from script or audio resources
  const getAudioSource = () => {
    if (!currentScript) return null;
    
    // Check for gender-specific audio based on selected voice
    if (localSelectedVoice === 'blue' && currentScript.narratorAudioMale) {
      return `data:audio/mpeg;base64,${currentScript.narratorAudioMale}`;
    }
    
    if (localSelectedVoice === 'pink' && currentScript.narratorAudioFemale) {
      return `data:audio/mpeg;base64,${currentScript.narratorAudioFemale}`;
    }
    
    // Fallback to legacy narrator audio field (for backward compatibility)
    if (currentScript.narratorAudio) {
      return `data:audio/mpeg;base64,${currentScript.narratorAudio}`;
    }
    
    // If script has audioId, find it in audio resources
    if (currentScript.audioId) {
      const audioResource = audioResources.find(resource => 
        resource.id === Number(currentScript.audioId)
      );
      if (audioResource) {
        return audioResource.url;
      }
    }
  };

  // Generate audio for a single script using ElevenLabs API
  const generateAudio = async (scriptId?: string, forceGender?: 'male' | 'female') => {
    const scriptToUpdate = scriptId ? scripts.find(s => s.id === scriptId) : currentScript;
    
    if (!scriptToUpdate) return;
    
    setIsGeneratingAudio(true);
    setErrorMessage(null);
    setSuccessMessage(null);
    
    try {
      // Get the text to convert to speech
      const textToSpeak = scriptToUpdate.narratorText;
      
      // Determine voice gender based on avatar or use forced gender if provided
      const gender = forceGender || (localSelectedVoice === 'blue' ? 'male' : 'female');
      
      // Call the API route directly
      const response = await fetch('/api/video-editing/elevenlabs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: textToSpeak,
          voiceId: gender === 'male' ? 'JBFqnCBsd6RMkjVDRZzb' : '9BWtsMINqrJLrRacOk9x', // Use default voice IDs
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate speech');
      }
      
      const data = await response.json();
      const audioBase64 = data.audio;
      
      // Create update object with appropriate gender field
      const updateData: Partial<VideoScript> = {};
      
      // We only update the default voice if we're not forcing a specific gender
      if (!forceGender) {
        updateData.defaultVoice = gender;
      }
      
      // Update the gender-specific field
      if (gender === 'male') {
        updateData.narratorAudioMale = audioBase64;
      } else {
        updateData.narratorAudioFemale = audioBase64;
      }
      
      // For backward compatibility, also update the general narratorAudio field if using the current selected gender
      if (!forceGender || gender === (localSelectedVoice === 'blue' ? 'male' : 'female')) {
        updateData.narratorAudio = audioBase64;
      }
      
      // Update the script with the new audio
      await updateScript(scriptToUpdate.id, updateData);
      
      // Only play audio if this is the current script and not part of batch generation
      // and we're not forcing a gender different from the selected one
      if (!scriptId && !forceGender && scriptToUpdate === currentScript) {
        const audio = new Audio(`data:audio/mpeg;base64,${audioBase64}`);
        audio.play();
      }
      
      if (!scriptId && !forceGender) {
        setSuccessMessage("Voice audio has been generated successfully.");
      }
      
      return true;
    } catch (error) {
      console.error('Error generating audio:', error);
      if (!scriptId && !forceGender) {
        setErrorMessage(error instanceof Error ? error.message : "Failed to generate audio");
      }
      return false;
    } finally {
      if (!scriptId && !forceGender) {
        setIsGeneratingAudio(false);
      }
    }
  };
  
  // Generate both male and female audio for a single script
  const generateBothGendersAudio = async (scriptId?: string) => {
    const scriptToUpdate = scriptId ? scripts.find(s => s.id === scriptId) : currentScript;
    
    if (!scriptToUpdate) return false;
    
    try {
      // First generate male voice
      const maleSuccess = await generateAudio(scriptToUpdate.id, 'male');
      
      // Then generate female voice
      const femaleSuccess = await generateAudio(scriptToUpdate.id, 'female');
      
      // Return true only if both voices were generated successfully
      return maleSuccess && femaleSuccess;
    } catch (error) {
      console.error('Error generating both gender audio:', error);
      return false;
    }
  };

  // Generate audio for all scripts
  const generateAllAudio = async () => {
    if (scripts.length === 0) {
      setErrorMessage("No scripts available to generate audio");
      return;
    }
    
    setIsGeneratingAllAudio(true);
    setGenerationProgress(0);
    setErrorMessage(null);
    setSuccessMessage(null);
    
    let successCount = 0;
    let failCount = 0;
    
    // Filter scripts to only include current versions for efficiency
    const currentVersionScripts = scripts.filter(s => s.isCurrentVersion);
    
    for (let i = 0; i < currentVersionScripts.length; i++) {
      const script = currentVersionScripts[i];
      const success = await generateAudio(script.id);
      
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
      
      // Update progress
      const newProgress = Math.round(((i + 1) / currentVersionScripts.length) * 100);
      setGenerationProgress(newProgress);
    }
    
    // Set final message
    if (failCount === 0) {
      setSuccessMessage(`Successfully generated audio for all ${successCount} scripts.`);
    } else {
      setErrorMessage(`Generated audio for ${successCount} scripts, but ${failCount} scripts failed.`);
    }
    
    setIsGeneratingAllAudio(false);
  };

  // Generate both male and female audio for all scripts
  const generateAllBothGendersAudio = async () => {
    if (scripts.length === 0) {
      setErrorMessage("No scripts available to generate audio");
      return;
    }
    
    setIsGeneratingAllAudio(true);
    setGenerationProgress(0);
    setErrorMessage(null);
    setSuccessMessage(null);
    
    // Track success and failure counts
    let successCount = 0;
    let failCount = 0;
    
    // Filter scripts to only include current versions for efficiency
    const currentVersionScripts = scripts.filter(s => s.isCurrentVersion);
    console.log(`[Control] Starting generation for ${currentVersionScripts.length} scripts (both male and female voices)`);
    
    // Total operations will be double (male and female for each script)
    const totalOperations = currentVersionScripts.length * 2;
    let completedOperations = 0;
    
    // Process scripts in small batches to update UI more frequently
    // The actual concurrency control is handled by the ConcurrencyManager in elevenLabsService
    for (let i = 0; i < currentVersionScripts.length; i++) {
      const script = currentVersionScripts[i];
      
      try {
        // First check if we already have both gender audios for this script
        const hasExistingMaleAudio = !!script.narratorAudioMale;
        const hasExistingFemaleAudio = !!script.narratorAudioFemale;
        
        if (hasExistingMaleAudio && hasExistingFemaleAudio) {
          console.log(`[Control] Script ${script.id} already has both male and female audio, skipping.`);
          // Count as success but skip generation
          successCount++;
          // Count as two completed operations (male and female)
          completedOperations += 2;
          setGenerationProgress(Math.round((completedOperations / totalOperations) * 100));
          continue;
        }
        
        // Generate male voice if needed
        if (!hasExistingMaleAudio) {
          const maleSuccess = await generateAudio(script.id, 'male');
          completedOperations++;
          setGenerationProgress(Math.round((completedOperations / totalOperations) * 100));
          if (!maleSuccess) {
            failCount++;
            console.error(`[Control] Failed to generate male voice for script ${script.id}`);
          }
        } else {
          // Count as completed operation
          completedOperations++;
          setGenerationProgress(Math.round((completedOperations / totalOperations) * 100));
        }
        
        // Generate female voice if needed
        if (!hasExistingFemaleAudio) {
          const femaleSuccess = await generateAudio(script.id, 'female');
          completedOperations++;
          setGenerationProgress(Math.round((completedOperations / totalOperations) * 100));
          if (!femaleSuccess) {
            failCount++;
            console.error(`[Control] Failed to generate female voice for script ${script.id}`);
          }
        } else {
          // Count as completed operation
          completedOperations++;
          setGenerationProgress(Math.round((completedOperations / totalOperations) * 100));
        }
        
        // Count as overall success if both genders exist after this process
        // (either they existed before or were successfully generated)
        const updatedScript = await getUpdatedScript(script.id);
        if (
          (hasExistingMaleAudio || updatedScript?.narratorAudioMale) && 
          (hasExistingFemaleAudio || updatedScript?.narratorAudioFemale)
        ) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        console.error(`[Control] Error processing script ${script.id}:`, error);
        failCount++;
        completedOperations += 2 - (completedOperations % 2); // Ensure we count both operations as complete
        setGenerationProgress(Math.round((completedOperations / totalOperations) * 100));
      }
    }
    
    // Set final message
    if (failCount === 0) {
      setSuccessMessage(`Successfully generated male and female audio for all ${successCount} scripts.`);
    } else {
      setErrorMessage(`Generated audio for ${successCount} scripts, but ${failCount} scripts failed.`);
    }
    
    setIsGeneratingAllAudio(false);
  };

  // Helper function to get the latest version of a script
  const getUpdatedScript = async (scriptId: string): Promise<VideoScript | null> => {
    try {
      const response = await fetch(`/api/video-editing/scripts/${scriptId}`);
      if (!response.ok) {
        return null;
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching updated script:`, error);
      return null;
    }
  }

  // Handle avatar selection change
  const handleAvatarChange = (value: string) => {
    const voiceType = value as 'blue' | 'pink';
    setLocalSelectedVoice(voiceType);
    
    // Update global store
    setSelectedVoice(voiceType);
    
    // Notify parent component about voice change
    if (onVoiceChange) {
      onVoiceChange(voiceType);
    }
  };

  // Handle set voice click
  const handleSetVoice = () => {
    // This would be implemented to handle the "set voice" functionality
    console.log("Set voice clicked");
  };

  // Current voice object placeholder (would actually be fetched from your data source)
  const currentVoice = {
    avatar: localSelectedVoice === 'blue' ? '/leo-avatar.svg' : '/jane-avatar.svg',
    name: localSelectedVoice === 'blue' ? 'Male' : 'Female'
  };

  // Define voice options
  const allVoices = [
    { id: 'blue', name: 'Male', avatar: '/leo-avatar.svg', gender: 'male' },
    { id: 'pink', name: 'Female', avatar: '/jane-avatar.svg', gender: 'female' }
  ];

  // Expose methods via useImperativeHandle
  useImperativeHandle(ref, () => ({
    generateAllBothGendersAudio
  }), [generateAllBothGendersAudio]);

  // Main render with new UI format
  return (
    <aside className="w-80 border-l bg-white flex flex-col p-6">
      {/* Voice selection */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-cyan-600">Default Voice</h3>
          <Select
            value={localSelectedVoice}  
            onValueChange={handleAvatarChange}
          >
            <SelectTrigger className="w-40 border border-gray-200 rounded-lg shadow-sm">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-50 flex-shrink-0">
                  {/* Placeholder for image - using div with background color instead */}
                  <div 
                    style={{
                      backgroundImage: `url(${currentVoice.avatar})`,
                      backgroundSize: 'cover',
                      width: '100%',
                      height: '100%'
                    }}
                  />
                </div>
                <SelectValue asChild>
                  <span className="text-sm font-medium">
                    {currentVoice.name}
                  </span>
                </SelectValue>
              </div>
            </SelectTrigger>
            <SelectContent>
              {allVoices.map((v) => (
                <SelectItem 
                  key={v.id} 
                  value={v.id}
                  className="cursor-pointer"
                >
                  <div className="flex items-center gap-2">
                    {/* Placeholder for image - using div with background color instead */}
                    <div 
                      style={{
                        backgroundImage: `url(${v.avatar})`,
                        backgroundSize: 'cover',
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%'
                      }}
                    />
                    <span>{v.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Voice Over Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-cyan-600">Add voice-over (cloning)</h3>
          <div className="flex items-center gap-10">
            <Info className="w-4 h-4 text-gray-400 cursor-help" />
            <Button 
              variant="outline" 
              className="flex items-center gap-2 -ml-3"
              onClick={handleSetVoice}
            >
              <AudioLines className="w-4 h-4" />
              set voice
            </Button>
          </div>
        </div>
      </div>

      {/* Duration */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-cyan-600">Ad Length</h3>
          <span className="text-sm text-gray-500">
            5 seconds
          </span>
        </div>
      </div>
      
      {/* Speed control */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-cyan-600">Speed</h3>
          <span className="text-sm text-gray-500">1x (Fixed)</span>
        </div>
      </div>
      
      {/* Video Editor section is implicitly included */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-cyan-600">Video Editor</h3>
          <Button
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={onToggleTimeline}
          >
            Toggle Timeline
          </Button>
        </div>
      </div>
      
      {/* Error and success messages */}
      {errorMessage && (
        <div className="mt-3 p-2 bg-red-100 border border-red-300 text-red-800 text-xs rounded">
          {errorMessage}
        </div>
      )}
      
      {successMessage && (
        <div className="mt-3 p-2 bg-green-100 border border-green-300 text-green-800 text-xs rounded">
          {successMessage}
        </div>
      )}
    </aside>
  );
});

// Add displayName for better debugging
Control.displayName = 'Control';

// Export as default to maintain compatibility
export default Control;
