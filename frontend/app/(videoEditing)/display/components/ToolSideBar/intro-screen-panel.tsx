import { FC, useState, useRef } from "react";
import { Settings } from "lucide-react";
import { cn } from "../../../../../lib/utils";
import { useDisplaySettings } from "../../../../../lib/hooks/useDisplaySettings";
import { HexColorPicker } from "react-colorful";

interface IntroScreenPanelProps {
  currentScriptId?: string | null;
  projectId?: string;
}

export const IntroScreenPanel: FC<IntroScreenPanelProps> = ({ currentScriptId, projectId }) => {
  const { 
    introSettings,
    updateIntroBackgroundColor,
    updateIntroLogo,
    toggleIntroScreen,
    resetAllScreens
  } = useDisplaySettings(projectId);
  
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle logo file upload
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setIsUploading(true);
    
    try {
      // Create a local URL for immediate preview
      const localPreviewUrl = URL.createObjectURL(file);
      setPreviewUrl(localPreviewUrl);
      
      // Prepare form data for upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'intro');
      
      // Upload to S3 via API
      const response = await fetch('/api/video-editing/upload-logo', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload logo');
      }
      
      const data = await response.json();
      
      // Update store with S3 URL
      updateIntroLogo({ url: data.url });
      
      // Clean up local preview URL
      URL.revokeObjectURL(localPreviewUrl);
      setPreviewUrl(null);
      
      // Reset input
      e.target.value = '';
    } catch (error) {
      console.error("Error uploading logo:", error);
    } finally {
      setIsUploading(false);
    }
  };
  
  // Handle toggling intro screen on/off
  const handleToggleIntroScreen = () => {
    toggleIntroScreen();
  };
  
  // Handle color change
  const handleColorChange = (color: string) => {
    updateIntroBackgroundColor(color);
  };
  
  // Handle logo width change
  const handleLogoWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateIntroLogo({ width: Number(e.target.value) });
  };
  
  // Handle logo height change
  const handleLogoHeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateIntroLogo({ height: Number(e.target.value) });
  };
  
  // Handle logo position changes
  const handleLogoXChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateIntroLogo({ x: Number(e.target.value) });
  };
  
  const handleLogoYChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateIntroLogo({ y: Number(e.target.value) });
  };
  
  // Trigger file input click
  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };
  
  // Toggle color picker visibility
  const toggleColorPicker = () => {
    setShowColorPicker(!showColorPicker);
  };
  
  return (
    <div className="h-full flex flex-col p-4 overflow-y-auto">
      <h2 className="text-lg font-medium mb-4">Intro Screen Settings</h2>
      
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between mb-6 p-3 bg-gray-50 rounded-lg">
        <div>
          <p className="text-sm font-medium">Show Intro Screen</p>
          <p className="text-xs text-gray-500">Display logo at the beginning of video</p>
        </div>
        <button 
          onClick={handleToggleIntroScreen}
          className={cn(
            "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
            introSettings.isEnabled ? "bg-blue-600" : "bg-gray-200"
          )}
        >
          <span
            className={cn(
              "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
              introSettings.isEnabled ? "translate-x-6" : "translate-x-1"
            )}
          />
        </button>
      </div>
      
      {/* Settings (only shown when enabled) */}
      {introSettings.isEnabled && (
        <>
          {/* Background Color Picker */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Background Color</label>
            <div className="flex flex-col space-y-3">
              <div 
                className="h-10 w-full rounded cursor-pointer border"
                style={{ backgroundColor: introSettings.backgroundColor }}
                onClick={toggleColorPicker}
              />
              
              {showColorPicker && (
                <div className="relative z-10">
                  <div 
                    className="fixed inset-0" 
                    onClick={toggleColorPicker}
                  />
                  <HexColorPicker 
                    color={introSettings.backgroundColor} 
                    onChange={handleColorChange}
                    className="absolute"
                  />
                </div>
              )}
              
              <input
                type="text"
                value={introSettings.backgroundColor}
                onChange={(e) => handleColorChange(e.target.value)}
                className="p-2 border rounded text-sm"
                placeholder="#000000"
              />
            </div>
          </div>
          
          {/* Logo Upload */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Logo Image</label>
            <div 
              onClick={triggerFileUpload}
              className={cn(
                "group border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors",
                "border-gray-300 hover:border-blue-500",
                isUploading && "opacity-50 pointer-events-none"
              )}
            >
              {previewUrl || introSettings.logoUrl ? (
                <div className="flex flex-col items-center">
                  <img 
                    src={previewUrl || introSettings.logoUrl} 
                    alt="Logo preview" 
                    className="max-h-32 max-w-full object-contain mb-2"
                  />
                  <p className="text-xs text-gray-500 group-hover:text-blue-500">
                    Click to change logo
                  </p>
                </div>
              ) : (
                <div className="py-4">
                  {isUploading ? (
                    <div className="h-5 w-5 border-2 border-t-blue-500 border-gray-200 rounded-full animate-spin mx-auto"></div>
                  ) : (
                    <>
                      <p className="text-sm text-gray-500 group-hover:text-blue-500">
                        Click to upload logo
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        Recommended: PNG with transparent background
                      </p>
                    </>
                  )}
                </div>
              )}
              <input 
                ref={fileInputRef}
                type="file" 
                className="hidden" 
                accept="image/*"
                onChange={handleLogoUpload}
                disabled={isUploading}
              />
            </div>
          </div>
          
          {/* Logo Size Controls */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Logo Size</label>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Width: {introSettings.logoWidth}%</span>
                </div>
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="1"
                  value={introSettings.logoWidth}
                  onChange={handleLogoWidthChange}
                  className="w-full"
                />
              </div>
              
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Height: {introSettings.logoHeight}%</span>
                </div>
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="1"
                  value={introSettings.logoHeight}
                  onChange={handleLogoHeightChange}
                  className="w-full"
                />
              </div>
            </div>
          </div>
          
          {/* Logo Position Controls */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Logo Position</label>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Horizontal: {introSettings.logoX}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="1"
                  value={introSettings.logoX}
                  onChange={handleLogoXChange}
                  className="w-full"
                />
              </div>
              
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Vertical: {introSettings.logoY}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="1"
                  value={introSettings.logoY}
                  onChange={handleLogoYChange}
                  className="w-full"
                />
              </div>
            </div>
          </div>
          
          {/* Reset Button */}
          <button
            onClick={resetAllScreens}
            className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Reset to Defaults
          </button>
        </>
      )}
    </div>
  );
};

export default IntroScreenPanel;
