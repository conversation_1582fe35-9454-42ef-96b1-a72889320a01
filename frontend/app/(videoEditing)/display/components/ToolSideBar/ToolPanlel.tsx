"use client";
import { type FC } from "react";
import { cn } from "../../../../../lib/utils";
import { MusicPanel } from "./music-panel";
import ScriptSegmentPanel from "./ScriptSegmentPanel";
import { TextPanel } from "./text-panel";
import { IntroScreenPanel } from "./intro-screen-panel";
import { EndScreenPanel } from "./end-screen-panel";
import { ToolPanelProps } from "../../../../../app/(videoEditing)/models/editorType";


export const ToolPanel: FC<ToolPanelProps> = ({
  type,
  isOpen,
  currentScriptId,
  onScriptSelect,
  projectId,
}) => {
  return (
    <div
      className={cn(
        "absolute left-16 inset-y-0 bg-white border-r w-80 transition-all duration-300 ease-in-out shadow-lg z-30",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}
    >
      {type === "music" && <MusicPanel currentScriptId={currentScriptId} />}

      {type === "script" && (
        <ScriptSegmentPanel
          currentScriptId={currentScriptId}
          onScriptSelect={onScriptSelect}
          projectId={projectId}
        />
      )}

      {type === "textOverlay" && (
        <TextPanel currentScriptId={currentScriptId} />
      )}
      
      {type === "introScreen" && (
        <IntroScreenPanel 
          currentScriptId={currentScriptId} 
          projectId={projectId}
        />
      )}
      
      {type === "endScreen" && (
        <EndScreenPanel 
          currentScriptId={currentScriptId} 
          projectId={projectId}
        />
      )}
    </div>
  );
};