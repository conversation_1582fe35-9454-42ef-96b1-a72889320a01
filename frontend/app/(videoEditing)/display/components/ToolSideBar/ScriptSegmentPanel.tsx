'use client';

import { FC, useState, useEffect, useCallback, memo } from "react";
import { useSearchParams } from 'next/navigation';
import { useVideoScriptStore, VideoScript } from "@/lib/store/videoScriptStore";
import { useVideoSegmentStore, VideoSegment } from "@/lib/store/videoSegmentStore";
import { Loader2, RefreshCw, Plus, Save, Check, Clock, AlertCircle, FileText, Film, Video, Trash2 } from 'lucide-react';
import { cn } from '../../../../../lib/utils';
import Image from 'next/image';

interface ScriptSegmentPanelProps {
  currentScriptId?: string | null;
  onScriptSelect?: (id: string) => void;
  projectId?: string;
}

const ScriptSegmentPanel: FC<ScriptSegmentPanelProps> = memo(({ 
  currentScriptId: externalCurrentScriptId,
  onScriptSelect,
  projectId: externalProjectId
}) => {
  const searchParams = useSearchParams();
  const urlProjectId = searchParams.get('projectId');
  
  // Use provided projectId or fallback to URL param
  const projectId = externalProjectId || urlProjectId || undefined;
  
  // Use provided currentScriptId or maintain internal state
  const [internalCurrentScriptId, setInternalCurrentScriptId] = useState<string | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState<string | null>(null);
  const [scriptText, setScriptText] = useState<string>('');
  const [narratorText, setNarratorText] = useState<string>('');
  const [phrase, setPhrase] = useState<string>('');
  const [isDirty, setIsDirty] = useState<boolean>(false);
  
  // Use external or internal currentScriptId
  const effectiveCurrentScriptId = externalCurrentScriptId !== undefined 
    ? externalCurrentScriptId 
    : internalCurrentScriptId;
  
  // Scripts store state
  const {
    scripts, 
    fetchScripts, 
    isLoading: isScriptsLoading, 
    isGenerating, 
    isDeleting,
    isUpdating,
    error: scriptError, 
    clearError: clearScriptError,
    generateScripts,
    deleteScript,
    updateScript,
    currentScript,
    fetchScript
  } = useVideoScriptStore();
  
  // Segments store state
  const {
    segments,
    fetchSegments,
    isLoading: isSegmentsLoading,
    error: segmentError,
    clearError: clearSegmentError
  } = useVideoSegmentStore();
  
  // Combined loading and error states
  const isLoading = isScriptsLoading || isSegmentsLoading;
  const error = scriptError || segmentError;
  const clearError = useCallback(() => {
    clearScriptError();
    clearSegmentError();
  }, [clearScriptError, clearSegmentError]);
  
  // Initialize scripts when component mounts
  useEffect(() => {
    if (projectId) {
      fetchScripts(projectId, false);
    }
  }, [projectId, fetchScripts]);
  
  // Load script data when scriptId changes
  useEffect(() => {
    if (effectiveCurrentScriptId) {
      fetchScript(effectiveCurrentScriptId);
      fetchSegments(effectiveCurrentScriptId);
      setIsDirty(false);
    }
  }, [effectiveCurrentScriptId, fetchScript, fetchSegments]);
  
  // Update form values when currentScript changes
  useEffect(() => {
    if (currentScript) {
      setScriptText(currentScript.scriptText || '');
      setNarratorText(currentScript.narratorText || '');
      setPhrase(currentScript.phrase || '');
      setIsDirty(false);
    }
  }, [currentScript]);
  
  // Handle script selection
  const handleSelectScript = useCallback((scriptId: string) => {
    setInternalCurrentScriptId(scriptId);
    if (onScriptSelect) {
      onScriptSelect(scriptId);
    }
  }, [onScriptSelect]);
  
  // Handle script generation
  const handleGenerateScripts = async () => {
    if (isGenerating || !projectId) return;
    
    try {
      await generateScripts(projectId);
    } catch (error) {
      console.error('Failed to generate scripts:', error);
    }
  };
  
  // Handle script deletion
  const handleDeleteScript = async (scriptId: string) => {
    if (isDeleting) return;
    
    try {
      await deleteScript(scriptId);
      setShowConfirmDelete(null);
      
      // If we deleted the currently selected script, clear the selection
      if (effectiveCurrentScriptId === scriptId) {
        setInternalCurrentScriptId(null);
        if (onScriptSelect) {
          onScriptSelect("");
        }
      }
    } catch (error) {
      console.error('Failed to delete script:', error);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentScript || !isDirty) return;
    
    await updateScript(currentScript.id, {
      scriptText,
      narratorText,
      phrase,
    });
    
    setIsDirty(false);
  };
  
  // Get status icon based on script status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-amber-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-400" />;
      default:
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  // Truncate text for display
  const truncateText = (text: string | null, maxLength: number = 40) => {
    if (!text) return '';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };
  
  // Get the segment related to the current script
  const getCurrentScriptSegments = useCallback((scriptId: string) => {
    return segments.filter(segment => segment.scriptId === scriptId);
  }, [segments]);
  
  if (!projectId) {
    return (
      <div className="h-full flex flex-col p-4">
        <h2 className="text-lg font-medium mb-4">Scripts & Segments</h2>
        <div className="text-gray-500 text-center py-4">
          No project selected
        </div>
      </div>
    );
  }
  
  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <h2 className="text-lg font-medium">Scripts & Segments</h2>
      </div>
      
      <div className="flex-1 overflow-hidden flex flex-col">
        {/* Controls */}
        <div className="p-2 border-b">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm flex items-start gap-2">
              <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">Error</p>
                <p>{error}</p>
                <button 
                  onClick={clearError}
                  className="text-red-700 underline text-xs mt-1"
                >
                  Dismiss
                </button>
              </div>
            </div>
          )}
          
          {/* <div className="flex items-center gap-2">
            <button
              onClick={handleGenerateScripts}
              disabled={isGenerating}
              className={`
                flex items-center gap-2 px-3 py-2 rounded-md flex-1 justify-center
                ${isGenerating
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
                }
              `}
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Generating...
                </>
              ) : scripts.length > 0 ? (
                <>
                  <RefreshCw className="w-4 h-4" />
                  Generate New Version
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4" />
                  Generate Scripts
                </>
              )}
            </button>
          </div> */}
          
          {showConfirmDelete !== null && (
            <div className="mt-4 p-3 border border-red-200 rounded-md bg-red-50">
              <p className="text-sm text-red-600 mb-2">
                Are you sure you want to delete this script?
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => handleDeleteScript(showConfirmDelete)}
                  disabled={isDeleting}
                  className="px-3 py-1 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 flex-1 flex items-center justify-center gap-1"
                >
                  {isDeleting ? 'Deleting...' : 'Yes, Delete'}
                </button>
                <button
                  onClick={() => setShowConfirmDelete(null)}
                  className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300 flex-1"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
        
        {scripts.length === 0 ? (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center text-gray-500">
              <p>No scripts found for this project.</p>
              <p className="text-sm mt-1">
                Click "Generate Scripts" to create scripts automatically.
              </p>
            </div>
          </div>
        ) : (
          <div className="flex-1 overflow-hidden flex flex-col">
            {/* Script List with Segment Info */}
            <div className="flex-1 overflow-y-auto">
              {isLoading ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="w-6 h-6 text-blue-500 animate-spin" />
                </div>
              ) : (
                <div className="space-y-2 p-2">
                  {scripts
                    .sort((a, b) => a.segmentNumber - b.segmentNumber)
                    .map((script) => {
                      const scriptSegments = getCurrentScriptSegments(script.id);
                      const currentVersionSegment = scriptSegments.find(s => s.isCurrentVersion);
                      
                      return (
                        <div key={script.id} className="border rounded-md overflow-hidden">
                          {/* Script Item Header */}
                          <div 
                            className={cn(
                              "p-3 cursor-pointer transition-colors",
                              effectiveCurrentScriptId === script.id 
                                ? "border-blue-500 bg-blue-50" 
                                : "border-transparent hover:bg-blue-50/30"
                            )}
                            onClick={() => handleSelectScript(script.id)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <FileText className="w-4 h-4 text-blue-500" />
                                <span className="font-medium">Segment {script.segmentNumber}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                {getStatusIcon(script.status)}
                                <button 
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setShowConfirmDelete(script.id);
                                  }}
                                  className="text-gray-400 hover:text-red-500"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                            
                            {script.narratorText && (
                              <p className="text-sm text-gray-600 mt-1">
                                {truncateText(script.narratorText)}
                              </p>
                            )}
                            
                            {script.phrase && (
                              <p className="text-sm text-indigo-600 italic mt-1">
                                {script.phrase}
                              </p>
                            )}
                            
                            <p className="text-xs text-gray-400 mt-2">
                              {new Date(script.updatedAt).toLocaleDateString()}
                            </p>
                          </div>
                          
                          {/* Segment Preview (if available) */}
                          {currentVersionSegment && (
                            <div className="border-t p-2 bg-gray-50">
                              <div className="flex items-center gap-2">
                                <Film className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600">Segment Status: {currentVersionSegment.status}</span>
                              </div>
                              
                              {currentVersionSegment.thumbnailUrl && (
                                <div className="mt-2 relative h-24 rounded-md overflow-hidden">
                                  <Image 
                                    src={currentVersionSegment.thumbnailUrl}
                                    alt={`Segment ${currentVersionSegment.id} thumbnail`}
                                    fill
                                    className="object-cover"
                                  />
                                </div>
                              )}
                              
                              {currentVersionSegment.videoUrl && !currentVersionSegment.thumbnailUrl && (
                                <div className="mt-2 flex items-center gap-2 text-sm text-blue-500">
                                  <Video className="w-4 h-4" />
                                  <span>Video available</span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                </div>
              )}
            </div>
            
            {/* Script Editor */}
            {effectiveCurrentScriptId && currentScript && (
              <div className="border-t p-4">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="scriptText" className="block text-sm font-medium text-gray-700 mb-1">
                      Visual Description
                    </label>
                    <textarea
                      id="scriptText"
                      className="w-full p-2 border border-gray-300 rounded-md min-h-[100px]"
                      value={scriptText}
                      onChange={(e) => {
                        setScriptText(e.target.value);
                        setIsDirty(true);
                      }}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="narratorText" className="block text-sm font-medium text-gray-700 mb-1">
                      Narrator Text
                    </label>
                    <textarea
                      id="narratorText"
                      className="w-full p-2 border border-gray-300 rounded-md min-h-[60px]"
                      value={narratorText}
                      onChange={(e) => {
                        setNarratorText(e.target.value);
                        setIsDirty(true);
                      }}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="phrase" className="block text-sm font-medium text-gray-700 mb-1">
                      Short Phrase (max 3 words)
                    </label>
                    <input
                      id="phrase"
                      type="text"
                      className="w-full p-2 border border-gray-300 rounded-md"
                      value={phrase}
                      onChange={(e) => {
                        setPhrase(e.target.value);
                        setIsDirty(true);
                      }}
                      placeholder="Short, impactful phrase"
                    />
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={!isDirty || isUpdating}
                      className={`
                        flex items-center gap-2 px-4 py-2 rounded-md
                        ${isDirty && !isUpdating
                          ? 'bg-blue-500 text-white hover:bg-blue-600'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }
                      `}
                    >
                      {isUpdating ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4" />
                          Save Changes
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

export default ScriptSegmentPanel; 