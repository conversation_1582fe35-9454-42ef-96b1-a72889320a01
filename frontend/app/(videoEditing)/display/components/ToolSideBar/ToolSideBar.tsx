"use client";

import { Music, FileEdit, Type, Play, Square, Layers } from "lucide-react";
import { useEffect, type FC, useRef } from "react";
import { ToolType } from "@/app/(videoEditing)/models/editorType";
import { ToolPanel } from "./ToolPanlel";

type ToolSideBarProps = {
    activeTool: ToolType;
    onToolClick: (tool: ToolType) => void;
    currentScriptId?: string | null;
    onScriptSelect?: (id: string) => void;
    projectId?: string;
};

export const ToolSideBar: FC<ToolSideBarProps> = ({
  activeTool,
  onToolClick,
  currentScriptId,
  onScriptSelect,
  projectId,
}) => {
  const toolbarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (toolbarRef.current && !toolbarRef.current.contains(event.target as Node) && activeTool) {
        onToolClick(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeTool, onToolClick]);

  // Handle script selection with new or old props
  const handleScriptSelect = (id: string) => {
    if (onScriptSelect) {
      onScriptSelect(id);
    } 
  };

  return (
    <div className="relative flex h-full" ref={toolbarRef}>
      <aside className="w-16 border-r bg-white flex flex-col items-center py-4 space-y-4 z-40">
        <button
          className={`p-2 rounded transition-colors ${
            activeTool === "music" ? "bg-gray-100" : "hover:bg-gray-50"
          }`}
          onClick={() => onToolClick(activeTool === "music" ? null : "music")}
          title="Background Music"
        >
          <Music
            className={`w-6 h-6 ${
              activeTool === "music" ? "text-blue-600" : "text-gray-600"
            }`}
          />
        </button>
        <button
          className={`p-2 rounded transition-colors ${
            activeTool === "script" ? "bg-gray-100" : "hover:bg-gray-50"
          }`}
          onClick={() => onToolClick(activeTool === "script" ? null : "script")}
          title="Scripts & Segments"
        >
          <Layers
            className={`w-6 h-6 ${
              activeTool === "script" ? "text-blue-600" : "text-gray-600"
            }`}
          />
        </button>
        <button
          className={`p-2 rounded transition-colors ${
            activeTool === "textOverlay" ? "bg-gray-100" : "hover:bg-gray-50"
          }`}
          onClick={() => onToolClick(activeTool === "textOverlay" ? null : "textOverlay")}
          title="Text Overlays"
        >
          <Type
            className={`w-6 h-6 ${
              activeTool === "textOverlay" ? "text-blue-600" : "text-gray-600"
            }`}
          />
        </button>
        <button
          className={`p-2 rounded transition-colors ${
            activeTool === "introScreen" ? "bg-gray-100" : "hover:bg-gray-50"
          }`}
          onClick={() => onToolClick(activeTool === "introScreen" ? null : "introScreen")}
          title="Intro Screen"
        >
          <Play
            className={`w-6 h-6 ${
              activeTool === "introScreen" ? "text-blue-600" : "text-gray-600"
            }`}
          />
        </button>
        <button
          className={`p-2 rounded transition-colors ${
            activeTool === "endScreen" ? "bg-gray-100" : "hover:bg-gray-50"
          }`}
          onClick={() => onToolClick(activeTool === "endScreen" ? null : "endScreen")}
          title="End Screen"
        >
          <Square
            className={`w-6 h-6 ${
              activeTool === "endScreen" ? "text-blue-600" : "text-gray-600"
            }`}
          />
        </button>
      </aside>

      <ToolPanel
        type={activeTool}
        isOpen={activeTool !== null}
        currentScriptId={currentScriptId}
        onScriptSelect={handleScriptSelect}
        projectId={projectId}
      />
    </div>
  );
};