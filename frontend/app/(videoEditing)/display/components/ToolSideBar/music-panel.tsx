import { FC, useRef, useState, useEffect } from "react";
import { Plus, Trash2, Play, Pause, Volume2, Check, Info } from "lucide-react";
import { cn } from "../../../../../lib/utils";
import { useMusicStore, AudioResource } from "@/lib/store/musicStore";
import { useVideoPlayerStore } from "@/lib/store/videoPlayerStore";

interface MusicPanelProps {
  currentScriptId?: string | null;
}

export const MusicPanel: FC<MusicPanelProps> = ({ currentScriptId }) => {
  const { 
    audioResources, 
    addAudioResourceFromFile, 
    removeAudioResource,
    getAudioByType,
    setCurrentAudio,
    currentAudioId,
    isLoading,
    isUploading,
    error,
    fetchAudioResources,
    getCurrentAudio
  } = useMusicStore();
  
  const { setBackgroundMusicUrl } = useVideoPlayerStore();
  
  const [currentlyPlaying, setCurrentlyPlaying] = useState<number | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  // Fetch audio resources on mount and when currentAudioId changes
  useEffect(() => {
    fetchAudioResources();
  }, [fetchAudioResources, currentAudioId]);
  
  // Get all background music
  const backgroundMusic = getAudioByType('background');
  
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Reset error state
    setUploadError(null);
    
    // Validate file type
    if (!file.type.startsWith('audio/')) {
      setUploadError('Please upload an audio file');
      return;
    }
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setUploadError('File size must be less than 10MB');
      return;
    }

    // Create audio element to check duration
    const audio = new Audio();
    audio.src = URL.createObjectURL(file);
    
    try {
      await new Promise((resolve, reject) => {
        audio.onloadedmetadata = () => {
          const duration = audio.duration;
          if (duration > 300) { // 5 minutes in seconds
            reject(new Error('Background music should not exceed 5 minutes'));
          } else {
            resolve(true);
          }
        };
        audio.onerror = () => reject(new Error('Failed to load audio file'));
      });
      
      await addAudioResourceFromFile(file, 'background');
      // Reset input
      e.target.value = '';
    } catch (error) {
      console.error("Error uploading audio:", error);
      setUploadError(error instanceof Error ? error.message : 'Failed to upload audio file');
    } finally {
      URL.revokeObjectURL(audio.src);
    }
  };
  
  const handlePlayPause = async (audio: AudioResource) => {
    if (currentlyPlaying === audio.id) {
      audioRef.current?.pause();
      setCurrentlyPlaying(null);
    } else {
      if (audioRef.current) {
        audioRef.current.pause();
      }
      
      audioRef.current = new Audio(audio.url);
      
      // Add event listeners for loading and errors
      audioRef.current.oncanplaythrough = () => {
        audioRef.current?.play();
      };
      
      audioRef.current.onerror = () => {
        console.error("Error playing audio:", audioRef.current?.error);
      };
      
      audioRef.current.onended = () => {
        setCurrentlyPlaying(null);
      };
      
      setCurrentlyPlaying(audio.id);
    }
  };
  
  const handleSelectAudio = (audioId: number) => {
    // Toggle selection - if already selected, deselect it
    if (currentAudioId === audioId) {
      setCurrentAudio(null);
      setBackgroundMusicUrl(null);
    } else {
      setCurrentAudio(audioId);
      const selectedAudio = audioResources.find(audio => audio.id === audioId);
      if (selectedAudio) {
        setBackgroundMusicUrl(selectedAudio.url);
      }
    }
  };
  
  const handleRemoveAudio = async (audioId: number) => {
    // Prevent deleting default resources (negative IDs)
    if (audioId < 0) {
      return;
    }
    
    // If removing currently selected audio, clear background music URL
    if (currentAudioId === audioId) {
      setBackgroundMusicUrl(null);
    }
    await removeAudioResource(audioId);
  };
  
  const formatDuration = (seconds?: number) => {
    if (!seconds) return "--:--";
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  const renderAudioItem = (audio: AudioResource) => (
    <div 
      key={audio.id}
      className={cn(
        "flex items-center p-4 rounded-lg transition-all duration-200",
        currentAudioId === audio.id 
          ? "bg-blue-50 border border-blue-200"
          : "bg-white border border-gray-100 hover:border-gray-200 hover:shadow-sm"
      )}
    >
      <button 
        onClick={() => handlePlayPause(audio)}
        className={cn(
          "w-10 h-10 flex items-center justify-center rounded-full transition-all duration-200",
          currentAudioId === audio.id
            ? "bg-blue-100 text-blue-600 hover:bg-blue-200"
            : "bg-gray-50 text-gray-600 hover:bg-gray-100"
        )}
        title="Play/Pause"
      >
        {currentlyPlaying === audio.id ? (
          <Pause className="h-5 w-5" />
        ) : (
          <Play className="h-5 w-5" />
        )}
      </button>
      
      <div className="flex-1 min-w-0 ml-4">
        <div className="flex items-center">
          <p className="text-sm font-medium text-gray-900 truncate">{audio.name}</p>
          {/* Add "Default" badge for default resources */}
          {audio.id < 0 && (
            <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full">
              Default
            </span>
          )}
        </div>
        <p className="text-xs text-gray-500 mt-0.5 flex items-center">
          <Volume2 className="h-3 w-3 mr-1" />
          {formatDuration(audio.duration)}
        </p>
      </div>
      
      <div className="flex items-center gap-2 ml-4">
        <button 
          onClick={() => handleSelectAudio(audio.id)}
          className={cn(
            "p-2 rounded-full transition-all duration-200",
            currentAudioId === audio.id 
              ? "bg-blue-100 text-blue-600 hover:bg-blue-200" 
              : "text-gray-400 hover:text-blue-600 hover:bg-gray-100"
          )}
          title={currentAudioId === audio.id ? "Selected" : "Select as background music"}
        >
          <Check className="h-4 w-4" />
        </button>
        
        {/* Only show delete button for user-uploaded resources */}
        {audio.id > 0 && (
          <button 
            onClick={() => handleRemoveAudio(audio.id)}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-all duration-200"
            title="Delete audio"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );

  const renderLoadingCard = () => (
    <div className="flex items-center p-4 rounded-lg bg-white border border-gray-100">
      <div className="w-10 h-10 rounded-full bg-gray-100 animate-pulse" />
      
      <div className="flex-1 min-w-0 ml-4">
        <div className="h-4 bg-gray-100 rounded w-24 animate-pulse mb-2" />
        <div className="h-3 bg-gray-100 rounded w-16 animate-pulse" />
      </div>
      
      <div className="flex items-center gap-2 ml-4">
        <div className="w-8 h-8 rounded-full bg-gray-100 animate-pulse" />
        <div className="w-8 h-8 rounded-full bg-gray-100 animate-pulse" />
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col p-4">
      <h2 className="text-lg font-medium mb-4">Background Music</h2>
      
      <div className="mb-4">
        <div className="flex items-start space-x-2 mb-4">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-1">
            <p className="text-sm text-blue-700">
              The selected background music will play throughout the entire video.
            </p>
            <p className="text-xs text-gray-500">
              Maximum file size: 10MB • Maximum duration: 5 minutes
            </p>
          </div>
        </div>

        <label className={cn(
          "w-full group flex items-center justify-center p-3 border-2 border-dashed rounded-lg cursor-pointer transition-colors",
          isUploading 
            ? "border-gray-200 bg-gray-50 cursor-not-allowed" 
            : "border-gray-300 hover:border-blue-500"
        )}>
          <input
            type="file"
            accept="audio/*"
            className="hidden"
            onChange={handleFileUpload}
            disabled={isUploading}
          />
          <div className="flex items-center space-x-2">
            <Plus className={cn(
              "h-5 w-5",
              isUploading 
                ? "text-gray-400"
                : "text-gray-400 group-hover:text-blue-500"
            )} />
            <span className={cn(
              "text-sm",
              isUploading
                ? "text-gray-400"
                : "text-gray-500 group-hover:text-blue-500"
            )}>
              {isUploading ? "Uploading..." : "Upload Background Music"}
            </span>
          </div>
        </label>
      </div>
      
      {(error || uploadError) && (
        <div className="p-4 bg-red-50 border border-red-100 rounded-lg mb-4">
          <p className="text-sm text-red-600">
            {error || uploadError}
          </p>
        </div>
      )}
      
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : backgroundMusic.length === 0 && !isUploading ? (
          <div className="flex flex-col items-center justify-center py-8 text-gray-400">
            <Volume2 className="h-12 w-12 mb-2 opacity-20" />
            <p className="text-sm">No background music added yet</p>
          </div>
        ) : (
          <div className="space-y-3">
            {isUploading && renderLoadingCard()}
            {backgroundMusic.map(renderAudioItem)}
          </div>
        )}
      </div>
    </div>
  );
};

export default MusicPanel;