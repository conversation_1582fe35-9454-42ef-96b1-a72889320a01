import { FC, useState, useRef } from "react";
import { Settings } from "lucide-react";
import { cn } from "../../../../../lib/utils";
import { useDisplaySettings } from "../../../../../lib/hooks/useDisplaySettings";
import { HexColorPicker } from "react-colorful";

interface EndScreenPanelProps {
  currentScriptId?: string | null;
  projectId?: string;
}

export const EndScreenPanel: FC<EndScreenPanelProps> = ({ currentScriptId, projectId }) => {
  const { 
    endSettings,
    updateEndBackgroundColor,
    updateEndLogo,
    updateEndCenterText,
    updateEndBottomText,
    toggleEndScreen,
    resetAllScreens,
    updateEndScreen
  } = useDisplaySettings(projectId);
  
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showBgColorPicker, setShowBgColorPicker] = useState(false);
  const [showCenterTextColorPicker, setShowCenterTextColorPicker] = useState(false);
  const [showBottomTextColorPicker, setShowBottomTextColorPicker] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle logo file upload
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setIsUploading(true);
    
    try {
      // Create a local URL for immediate preview
      const localPreviewUrl = URL.createObjectURL(file);
      setPreviewUrl(localPreviewUrl);
      
      // Prepare form data for upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'end');
      
      // Upload to S3 via API
      const response = await fetch('/api/video-editing/upload-logo', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload logo');
      }
      
      const data = await response.json();
      
      // Update store with S3 URL
      updateEndLogo({ url: data.url });
      
      // Clean up local preview URL
      URL.revokeObjectURL(localPreviewUrl);
      setPreviewUrl(null);
      
      // Reset input
      e.target.value = '';
    } catch (error) {
      console.error("Error uploading logo:", error);
    } finally {
      setIsUploading(false);
    }
  };
  
  // Handle toggling end screen on/off
  const handleToggleEndScreen = () => {
    toggleEndScreen();
  };
  
  // Handle color change
  const handleColorChange = (color: string) => {
    updateEndBackgroundColor(color);
  };
  
  // Handle logo width change
  const handleLogoWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndLogo({ width: Number(e.target.value) });
  };
  
  // Handle logo height change
  const handleLogoHeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndLogo({ height: Number(e.target.value) });
  };
  
  // Handle logo position changes
  const handleLogoXChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndLogo({ x: Number(e.target.value) });
  };
  
  const handleLogoYChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndLogo({ y: Number(e.target.value) });
  };
  
  // Handle center text changes
  const handleCenterTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateEndCenterText({ text: e.target.value });
  };
  
  const handleCenterTextColorChange = (color: string) => {
    updateEndCenterText({ color: color });
  };
  
  const handleCenterTextSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndCenterText({ size: Number(e.target.value) });
  };
  
  // Handle center text position changes
  const handleCenterTextXChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndCenterText({ x: Number(e.target.value) });
  };
  
  const handleCenterTextYChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndCenterText({ y: Number(e.target.value) });
  };
  
  // Handle bottom text changes
  const handleBottomTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateEndBottomText({ text: e.target.value });
  };
  
  const handleBottomTextColorChange = (color: string) => {
    updateEndBottomText({ color: color });
  };
  
  const handleBottomTextSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndBottomText({ size: Number(e.target.value) });
  };
  
  // Handle bottom text position changes
  const handleBottomTextXChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndBottomText({ x: Number(e.target.value) });
  };
  
  const handleBottomTextYChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndBottomText({ y: Number(e.target.value) });
  };
  
  // Handle end screen audio URL change
  const handleAudioUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndScreen({ audioUrl: e.target.value });
  };

  // Handle end screen audio volume change
  const handleAudioVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEndScreen({ audioVolume: Number(e.target.value) });
  };

  // Handle toggling end screen audio
  const handleToggleAudio = () => {
    updateEndScreen({ isAudioEnabled: !endSettings.isAudioEnabled });
  };
  
  // Trigger file input click
  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };
  
  // Toggle color picker visibility
  const toggleBgColorPicker = () => {
    setShowBgColorPicker(!showBgColorPicker);
  };
  
  const toggleCenterTextColorPicker = () => {
    setShowCenterTextColorPicker(!showCenterTextColorPicker);
  };
  
  const toggleBottomTextColorPicker = () => {
    setShowBottomTextColorPicker(!showBottomTextColorPicker);
  };
  
  return (
    <div className="h-full flex flex-col p-4 overflow-y-auto">
      <h2 className="text-lg font-medium mb-4">End Screen Settings</h2>
      
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between mb-6 p-3 bg-gray-50 rounded-lg">
        <div>
          <p className="text-sm font-medium">Show End Screen</p>
          <p className="text-xs text-gray-500">Display end screen at the end of video</p>
        </div>
        <button 
          onClick={handleToggleEndScreen}
          className={cn(
            "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
            endSettings.isEnabled ? "bg-blue-600" : "bg-gray-200"
          )}
        >
          <span
            className={cn(
              "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
              endSettings.isEnabled ? "translate-x-6" : "translate-x-1"
            )}
          />
        </button>
      </div>
      
      {/* Settings (only shown when enabled) */}
      {endSettings.isEnabled && (
        <>
          {/* Background Color Picker */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Background Color</label>
            <div className="flex flex-col space-y-3">
              <div 
                className="h-10 w-full rounded cursor-pointer border"
                style={{ backgroundColor: endSettings.backgroundColor }}
                onClick={toggleBgColorPicker}
              />
              
              {showBgColorPicker && (
                <div className="relative z-10">
                  <div 
                    className="fixed inset-0" 
                    onClick={toggleBgColorPicker}
                  />
                  <HexColorPicker 
                    color={endSettings.backgroundColor} 
                    onChange={handleColorChange}
                    className="absolute"
                  />
                </div>
              )}
              
              <input
                type="text"
                value={endSettings.backgroundColor}
                onChange={(e) => handleColorChange(e.target.value)}
                className="p-2 border rounded text-sm"
                placeholder="#000000"
              />
            </div>
          </div>
          
          {/* Logo Upload */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Logo Image</label>
            <div 
              onClick={triggerFileUpload}
              className={cn(
                "group border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors",
                "border-gray-300 hover:border-blue-500",
                isUploading && "opacity-50 pointer-events-none"
              )}
            >
              {previewUrl || endSettings.logoUrl ? (
                <div className="flex flex-col items-center">
                  <img 
                    src={previewUrl || endSettings.logoUrl} 
                    alt="Logo preview" 
                    className="max-h-32 max-w-full object-contain mb-2"
                  />
                  <p className="text-xs text-gray-500 group-hover:text-blue-500">
                    Click to change logo
                  </p>
                </div>
              ) : (
                <div className="py-4">
                  {isUploading ? (
                    <div className="h-5 w-5 border-2 border-t-blue-500 border-gray-200 rounded-full animate-spin mx-auto"></div>
                  ) : (
                    <>
                      <p className="text-sm text-gray-500 group-hover:text-blue-500">
                        Click to upload logo
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        Recommended: PNG with transparent background
                      </p>
                    </>
                  )}
                </div>
              )}
              <input 
                ref={fileInputRef}
                type="file" 
                className="hidden" 
                accept="image/*"
                onChange={handleLogoUpload}
                disabled={isUploading}
              />
            </div>
          </div>
          
          {/* Logo Size Controls */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Logo Size</label>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Width: {endSettings.logoWidth}%</span>
                </div>
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="1"
                  value={endSettings.logoWidth}
                  onChange={handleLogoWidthChange}
                  className="w-full"
                />
              </div>
              
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Height: {endSettings.logoHeight}%</span>
                </div>
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="1"
                  value={endSettings.logoHeight}
                  onChange={handleLogoHeightChange}
                  className="w-full"
                />
              </div>
            </div>
          </div>
          
          {/* Logo Position Controls */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Logo Position</label>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Horizontal: {endSettings.logoX}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="1"
                  value={endSettings.logoX}
                  onChange={handleLogoXChange}
                  className="w-full"
                />
              </div>
              
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Vertical: {endSettings.logoY}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="1"
                  value={endSettings.logoY}
                  onChange={handleLogoYChange}
                  className="w-full"
                />
              </div>
            </div>
          </div>
          
          {/* Center Text Controls */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Center Text</label>
            <div className="space-y-4">
              <div>
                <textarea
                  value={endSettings.centerText}
                  onChange={handleCenterTextChange}
                  className="w-full h-20 p-2 border rounded-md text-sm resize-none"
                  placeholder="Enter center text..."
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-500 mb-1">Text Color</label>
                <div className="flex flex-col space-y-2">
                  <div 
                    className="h-8 w-full rounded cursor-pointer border"
                    style={{ backgroundColor: endSettings.centerTextColor }}
                    onClick={toggleCenterTextColorPicker}
                  />
                  
                  {showCenterTextColorPicker && (
                    <div className="relative z-10">
                      <div 
                        className="fixed inset-0" 
                        onClick={toggleCenterTextColorPicker}
                      />
                      <HexColorPicker 
                        color={endSettings.centerTextColor} 
                        onChange={handleCenterTextColorChange}
                        className="absolute"
                      />
                    </div>
                  )}
                  
                  <input
                    type="text"
                    value={endSettings.centerTextColor}
                    onChange={(e) => handleCenterTextColorChange(e.target.value)}
                    className="p-2 border rounded text-xs"
                    placeholder="#FFFFFF"
                  />
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Size: {endSettings.centerTextSize}px</span>
                </div>
                <input
                  type="range"
                  min="12"
                  max="72"
                  step="1"
                  value={endSettings.centerTextSize}
                  onChange={handleCenterTextSizeChange}
                  className="w-full"
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-500 mb-1">Position</label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Horizontal: {endSettings.centerTextX}%</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="1"
                      value={endSettings.centerTextX}
                      onChange={handleCenterTextXChange}
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Vertical: {endSettings.centerTextY}%</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="1"
                      value={endSettings.centerTextY}
                      onChange={handleCenterTextYChange}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Bottom Text Controls */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Bottom Text</label>
            <div className="space-y-4">
              <div>
                <textarea
                  value={endSettings.bottomText}
                  onChange={handleBottomTextChange}
                  className="w-full h-20 p-2 border rounded-md text-sm resize-none"
                  placeholder="Enter bottom text..."
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-500 mb-1">Text Color</label>
                <div className="flex flex-col space-y-2">
                  <div 
                    className="h-8 w-full rounded cursor-pointer border"
                    style={{ backgroundColor: endSettings.bottomTextColor }}
                    onClick={toggleBottomTextColorPicker}
                  />
                  
                  {showBottomTextColorPicker && (
                    <div className="relative z-10">
                      <div 
                        className="fixed inset-0" 
                        onClick={toggleBottomTextColorPicker}
                      />
                      <HexColorPicker 
                        color={endSettings.bottomTextColor} 
                        onChange={handleBottomTextColorChange}
                        className="absolute"
                      />
                    </div>
                  )}
                  
                  <input
                    type="text"
                    value={endSettings.bottomTextColor}
                    onChange={(e) => handleBottomTextColorChange(e.target.value)}
                    className="p-2 border rounded text-xs"
                    placeholder="#FFFFFF"
                  />
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Size: {endSettings.bottomTextSize}px</span>
                </div>
                <input
                  type="range"
                  min="8"
                  max="48"
                  step="1"
                  value={endSettings.bottomTextSize}
                  onChange={handleBottomTextSizeChange}
                  className="w-full"
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-500 mb-1">Position</label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Horizontal: {endSettings.bottomTextX}%</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="1"
                      value={endSettings.bottomTextX}
                      onChange={handleBottomTextXChange}
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Vertical: {endSettings.bottomTextY}%</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="1"
                      value={endSettings.bottomTextY}
                      onChange={handleBottomTextYChange}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* End Screen Audio Controls */}
          <div className="mb-6">
            <h3 className="text-md font-medium mb-3 border-t pt-4">End Screen Audio</h3>
            <div className="space-y-4">
              {/* Audio Enable/Disable Toggle */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Enable Audio</label>
                <button 
                  onClick={handleToggleAudio}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    endSettings.isAudioEnabled ? "bg-blue-600" : "bg-gray-200"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      endSettings.isAudioEnabled ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
              </div>

              {/* Audio URL Input */}
              <div>
                <label className="block text-sm font-medium mb-1">Audio URL</label>
                <input
                  type="text"
                  value={endSettings.audioUrl || ''}
                  onChange={handleAudioUrlChange}
                  className="w-full p-2 border rounded text-sm"
                  placeholder="https://example.com/audio.mp3"
                  disabled={!endSettings.isAudioEnabled}
                />
              </div>

              {/* Audio Volume Slider */}
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Volume: {Math.round((endSettings.audioVolume || 1) * 100)}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.05"
                  value={endSettings.audioVolume || 1}
                  onChange={handleAudioVolumeChange}
                  className="w-full"
                  disabled={!endSettings.isAudioEnabled}
                />
              </div>
            </div>
          </div>
          
          {/* Reset Button */}
          <button
            onClick={resetAllScreens}
            className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Reset to Defaults
          </button>
        </>
      )}
    </div>
  );
};

export default EndScreenPanel;
