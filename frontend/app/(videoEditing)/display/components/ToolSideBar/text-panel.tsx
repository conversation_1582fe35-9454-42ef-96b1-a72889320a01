import { FC, useState, useEffect } from "react";
import { Plus, Trash2, Edit2, Type, Move, AlignLeft, AlignCenter, AlignRight } from "lucide-react";
import { cn } from "../../../../../lib/utils";
import { useTextOverlayStore, TextOverlay } from "../../../../../lib/store/textOverlayStore";
import { useSearchParams } from "next/navigation";

interface TextPanelProps {
  currentScriptId?: string | null;
}

export const TextPanel: FC<TextPanelProps> = ({ currentScriptId }) => {
  const { 
    textOverlays, 
    addTextOverlay, 
    updateTextOverlay, 
    removeTextOverlay,
    isLoading,
    error,
    projectId,
    setProjectId,
    loadFromServer,
    saveToServer
  } = useTextOverlayStore();
  
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [editText, setEditText] = useState('');
  const [editFontSize, setEditFontSize] = useState(24);
  const [editColor, setEditColor] = useState('#ffffff');
  const [editAlignment, setEditAlignment] = useState<'left' | 'center' | 'right'>('center');
  const [editStartTime, setEditStartTime] = useState(0);
  const [editDuration, setEditDuration] = useState(5);
  
  // Get search params from Next.js router
  const searchParams = useSearchParams();
  
  // Load project data
  useEffect(() => {
    // Get project ID from URL query parameter
    const projectIdFromUrl = searchParams.get('projectId');
    
    console.log(`[TextPanel] URL projectId: ${projectIdFromUrl}, current store projectId: ${projectId}`);
    
    if (projectIdFromUrl && projectIdFromUrl !== projectId) {
      console.log(`[TextPanel] Setting projectId to ${projectIdFromUrl} and loading from server`);
      setProjectId(projectIdFromUrl);
      
      // Load existing text overlays for this project
      loadFromServer().then(() => {
        console.log(`[TextPanel] Successfully loaded text overlays for project ${projectIdFromUrl}`);
      }).catch(err => {
        console.error('[TextPanel] Error loading text overlays from server:', err);
      });
    }
  }, [projectId, setProjectId, loadFromServer, searchParams]);
  
  // Add a new text overlay
  const handleAddTextOverlay = () => {
    const newOverlay = addTextOverlay({
      text: 'New Text',
      fontSize: 33,
      color: '#ffffff',
      position: { x: 0.1, y: 0.9 },
      alignment: 'center',
      startTime: 0,
      duration: 5
    });
    
    setIsEditing(newOverlay.id);
    setEditText(newOverlay.text);
    setEditFontSize(newOverlay.fontSize);
    setEditColor(newOverlay.color);
    setEditAlignment(newOverlay.alignment);
    setEditStartTime(newOverlay.startTime);
    setEditDuration(newOverlay.duration);
  };
  
  // Start editing a text overlay
  const handleEditStart = (overlay: TextOverlay) => {
    setIsEditing(overlay.id);
    setEditText(overlay.text);
    setEditFontSize(overlay.fontSize);
    setEditColor(overlay.color);
    setEditAlignment(overlay.alignment);
    setEditStartTime(overlay.startTime);
    setEditDuration(overlay.duration);
  };
  
  // Save edits
  const handleSaveEdit = () => {
    if (isEditing === null) return;
    
    updateTextOverlay(isEditing, {
      text: editText,
      fontSize: editFontSize,
      color: editColor,
      alignment: editAlignment,
      startTime: editStartTime,
      duration: editDuration
    });
    
    setIsEditing(null);
    
    // Save changes to server
    saveToServer();
  };
  
  // Cancel editing
  const handleCancelEdit = () => {
    setIsEditing(null);
  };
  
  // Remove a text overlay
  const handleRemoveTextOverlay = (id: string) => {
    removeTextOverlay(id);
    if (isEditing === id) {
      setIsEditing(null);
    }
    
    // Save changes to server
    saveToServer();
  };
  
  // Loading state
  if (isLoading) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-4">
        <div className="animate-pulse text-gray-400">Loading text overlays...</div>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="h-full flex flex-col p-4">
        <div className="text-red-500 mb-4">Error: {error}</div>
        <button 
          onClick={() => loadFromServer()}
          className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }
  
  return (
    <div className="h-full flex flex-col p-4">
      <h2 className="text-lg font-medium mb-4">Text Overlay</h2>
      
      <div className="mb-4">
        <button 
          onClick={handleAddTextOverlay}
          className="w-full group flex items-center justify-center p-3 border-2 border-dashed rounded-lg cursor-pointer transition-colors border-gray-300 hover:border-blue-500"
        >
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5 text-gray-400 group-hover:text-blue-500" />
            <span className="text-sm text-gray-500 group-hover:text-blue-500">
              Add Text Overlay
            </span>
          </div>
        </button>
      </div>

      <div className="flex-1 overflow-y-auto space-y-3">
        {textOverlays.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-gray-400">
            <Type className="h-12 w-12 mb-2 opacity-20" />
            <p className="text-sm">Click the button above to add text</p>
          </div>
        ) : (
          textOverlays.map(overlay => (
            <div 
              key={overlay.id}
              className="bg-gray-50 rounded-lg overflow-hidden border border-gray-200"
            >
              {isEditing === overlay.id ? (
                // Edit mode
                <div className="p-3">
                  <div className="mb-3">
                    <label className="block text-xs font-medium text-gray-700 mb-1">Text Content</label>
                    <input
                      type="text"
                      value={editText}
                      onChange={(e) => setEditText(e.target.value)}
                      className="w-full p-2 border rounded-md text-sm"
                      placeholder="Enter text content"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Font Size</label>
                      <input
                        type="number"
                        value={editFontSize}
                        onChange={(e) => setEditFontSize(Number(e.target.value))}
                        className="w-full p-2 border rounded-md text-sm"
                        min={10}
                        max={72}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Text Color</label>
                      <input
                        type="color"
                        value={editColor}
                        onChange={(e) => setEditColor(e.target.value)}
                        className="w-full h-9 p-1 border rounded-md"
                      />
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <label className="block text-xs font-medium text-gray-700 mb-1">Alignment</label>
                    <div className="flex border rounded-md overflow-hidden">
                      <button
                        type="button"
                        onClick={() => setEditAlignment('left')}
                        className={cn(
                          "flex-1 p-2 flex items-center justify-center",
                          editAlignment === 'left' ? "bg-blue-50 text-blue-600" : "bg-white text-gray-700"
                        )}
                      >
                        <AlignLeft size={16} />
                      </button>
                      <button
                        type="button"
                        onClick={() => setEditAlignment('center')}
                        className={cn(
                          "flex-1 p-2 flex items-center justify-center border-l border-r",
                          editAlignment === 'center' ? "bg-blue-50 text-blue-600" : "bg-white text-gray-700"
                        )}
                      >
                        <AlignCenter size={16} />
                      </button>
                      <button
                        type="button"
                        onClick={() => setEditAlignment('right')}
                        className={cn(
                          "flex-1 p-2 flex items-center justify-center",
                          editAlignment === 'right' ? "bg-blue-50 text-blue-600" : "bg-white text-gray-700"
                        )}
                      >
                        <AlignRight size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Start Time (sec)</label>
                      <input
                        type="number"
                        value={editStartTime}
                        onChange={(e) => setEditStartTime(Number(e.target.value))}
                        className="w-full p-2 border rounded-md text-sm"
                        min={0}
                        step={0.1}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Duration (sec)</label>
                      <input
                        type="number"
                        value={editDuration}
                        onChange={(e) => setEditDuration(Number(e.target.value))}
                        className="w-full p-2 border rounded-md text-sm"
                        min={0.1}
                        step={0.1}
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={handleCancelEdit}
                      className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveEdit}
                      className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                      Save
                    </button>
                  </div>
                </div>
              ) : (
                // Preview mode
                <div className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium text-sm truncate flex-1">{overlay.text}</div>
                    <div className="flex space-x-1">
                      <button
                        onClick={() => handleEditStart(overlay)}
                        className="p-1 text-gray-500 hover:text-blue-500"
                        title="Edit"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button
                        onClick={() => handleRemoveTextOverlay(overlay.id)}
                        className="p-1 text-gray-500 hover:text-red-500"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <div 
                    className="bg-gray-800 rounded-md p-3 flex items-center justify-center mb-2"
                    style={{ minHeight: '60px' }}
                  >
                    <div 
                      style={{ 
                        color: overlay.color,
                        fontSize: `${overlay.fontSize / 3}px`,
                        textAlign: overlay.alignment
                      }}
                      className="w-full"
                    >
                      {overlay.text}
                    </div>
                  </div>
                  
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Start: {overlay.startTime}s</span>
                    <span>Duration: {overlay.duration}s</span>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TextPanel;