import { Suspense } from 'react';
import VideoEditor from './components/VideoEditor';
import { getVideoProjectById } from '@/lib/db/queries';
import { notFound } from 'next/navigation';

export default async function DisplayPage({
  searchParams,
}: {
  searchParams: Promise<{ projectId?: string }>;
}) {
  // get projectId from query parameters
  const resolvedParams = await searchParams;
  const projectId = resolvedParams.projectId;
  
  if (!projectId) {
    notFound();
  }
  
  try {
    const project = await getVideoProjectById(projectId);
    
    if (!project) {
      notFound();
    }
    
    return (
      <Suspense fallback={
        <div className="flex h-full w-full items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      }>
        <VideoEditor />
      </Suspense>
    );
  } catch (error) {
    console.error('Error fetching project:', error);
    notFound();
  }
}