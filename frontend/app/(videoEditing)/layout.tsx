'use client';

import Link from 'next/link';
import { use, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CircleIcon, Home, LogOut, Download } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useUser } from '@/lib/auth';
import { signOut } from '@/app/(login)/actions';
import { useRouter, usePathname } from 'next/navigation';
import { Footer } from '@/components/ui/Footer';
import { Divider } from '@/components/ui/Divider';
import { PreviewDialog } from './display/components/Preview/PreviewDialog';
import { useVideoProjectStore } from '@/lib/store/videoProjectStore';

type HeaderProps = {
  activeNavItem?: string;
  onNavItemClick?: (item: string) => void;
  isMobileMenuOpen: boolean; 
  toggleMobileMenu: () => void;
};

function Header({ activeNavItem, onNavItemClick, isMobileMenuOpen, toggleMobileMenu }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const { userPromise } = useUser();
  const user = userPromise ? use(userPromise) : null;
  const { currentProject } = useVideoProjectStore();
  const router = useRouter();

  async function handleSignOut() {
    await signOut();
    router.refresh();
    router.push('/');
  }

  return (
    <header className="border-b-transparent border-gray-200 bg-white shadow-md z-[100]">
      <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <button 
            onClick={toggleMobileMenu}
            className="md:hidden text-cyan-700 hover:text-cyan-800 cursor-pointer focus:outline-none mr-4"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 6h16M4 12h16M4 18h16" 
              />
            </svg>
          </button>
          <Link 
            href="/" 
            className="flex items-center"
            onClick={(e) => {
              e.preventDefault();
              window.location.href = '/adTargeting';
            }}
          >
            <span className="ml-2">
              <img className="h-6 w-auto object-contain" src="/Fylow-copy4.png" alt="Logo"/>
            </span>
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-bold text-cyan-700 font-sans">
            {currentProject?.brandName || "Fylow"}
          </h1>
          {user ? (
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <DropdownMenuTrigger>
                <Avatar className="cursor-pointer size-9">
                  <AvatarImage alt={user.name || ''} />
                  <AvatarFallback>
                    {user.email
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="flex flex-col gap-1 z-[200]">
                <DropdownMenuItem 
                  className="cursor-pointer text-cyan-700 hover:text-cyan-800"
                  onClick={() => {
                    setIsMenuOpen(false);
                    router.push('/dashboard');
                  }}
                >
                  <Home className="mr-2 h-4 w-4" />
                  <span>Dashboard</span>
                </DropdownMenuItem>
                <form action={handleSignOut} className="w-full">
                  <button type="submit" className="flex w-full">
                    <DropdownMenuItem className="w-full flex-1 cursor-pointer text-cyan-700 hover:text-cyan-800">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign out</span>
                    </DropdownMenuItem>
                  </button>
                </form>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              asChild
              className="bg-cyan-700 hover:bg-cyan-800 text-white text-sm px-4 py-2 rounded-md"
            >
              <Link href="/sign-up">Sign Up</Link>
            </Button>
          )}
        </div>
      </div>

      <PreviewDialog 
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />
    </header>
  );
}

export default function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  const [activeNavItem, setActiveNavItem] = useState<string | undefined>(undefined);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { userPromise } = useUser();
  const user = userPromise ? use(userPromise) : null;
  const router = useRouter();
  
  // // check user's login status, if not logged in, redirect to login page
  useEffect(() => {
    if (!user) {
      router.push('/sign-in');
    }
  }, [user, router]);

  const handleNavItemClick = (item: string) => {
    setActiveNavItem(item);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header 
        activeNavItem={activeNavItem} 
        onNavItemClick={handleNavItemClick}
        isMobileMenuOpen={isMobileMenuOpen}
        toggleMobileMenu={toggleMobileMenu}
      />
      <div className="flex flex-1 overflow-hidden">
          <>
            <main className="flex-1">
              <div className="flex-1 h-full">
                {children}
              </div>
            </main>
          </>
      </div>
      <Divider />
      <Footer />
    </div>
  );
}
