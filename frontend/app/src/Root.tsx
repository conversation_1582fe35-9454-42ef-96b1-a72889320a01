import { Composition } from 'remotion';
import { VideoComposition } from '../(videoEditing)/display/components/Preview/VideoComposition';

/**
 * Root component for Remotion Lambda rendering
 * This component defines the available compositions and their default props
 * The VideoCompositionWithWatermark is used by Lambda for rendering exported videos
 */
export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="VideoComposition"
        component={VideoComposition}
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{
          showWatermark: true,
          // Default empty values that will be populated from API
          segments: [],
          framesPerSegment: 140,
          introBlackScreenFrames: 60,
          endScreenFrames: 99,
          backgroundMusicUrl: null,
          introEnabled: false,
          endEnabled: false,
          fps: 30,
          backgroundMusicVolume: 1,
          isBackgroundMusicEnabled: true,
          durationInFrames: 300, // Default duration, will be overridden by API
          textOverlays: [],
          useSettings: {
            endSettings: {
              audioUrl: null,
              audioVolume: 1,
              isAudioEnabled: true,
            }
          },
          brandName: 'Brand',
          getVoiceAudioFile: () => '',
          scripts: {},
          selectedVoice: ''
        }}
        calculateMetadata={({ props }) => {
          // Use durationInFrames from props if provided, otherwise use default
          const durationInFrames = typeof props.durationInFrames === 'number' ? props.durationInFrames : 300;
          
          console.log('calculateMetadata: Using durationInFrames =', durationInFrames);
          
          return {
            durationInFrames: durationInFrames,
          };
        }}
      />
    </>
  );
}; 