'use client';

import Link from 'next/link';
import { use, useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown , Building2,BriefcaseBusiness, ChartNoAxesCombined, House, Clapperboard , BotMessageSquare, ArrowRightToLine, X, Menu} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {getUser} from "@/lib/db/queries"
import { useRouter, useSearchParams } from 'next/navigation';
import { Footer } from '@/components/ui/Footer';
import { Divider } from '@/components/ui/Divider';
import HoverDropdown from './components/HoverDropDown';
import { useUser } from '@/lib/auth';
import { signOut } from '@/app/(login)/actions';
function Header({ activeNavItem, onNavItemClick}: { 
  activeNavItem?: string;
  onNavItemClick?: (item: string) => void;
}) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [openDropdown,setOpenDropdown] = useState(false);
  const [isVideoFormOpen, setIsVideoFormOpen] = useState(false);
  const [signedOut, setSignedOut] = useState(false);
  const { userPromise } = useUser();
  const user = signedOut ? null : (userPromise ? use(userPromise) : null);
  const router = useRouter();
  const SideBarRef = useRef<HTMLDivElement | null> (null);
      // Close sidebar if clicked outside on mobile
       useEffect(() => {
         const handleClickOutside = (event: MouseEvent) => {
           if (
             SideBarRef.current &&
             !SideBarRef.current.contains(event.target as Node) &&
             isMenuOpen 
           ) {
             setIsMenuOpen(false);
           }
         };
         
         document.addEventListener("mousedown", handleClickOutside);
         return () => {
           document.removeEventListener("mousedown", handleClickOutside);
         };
       }, [isMenuOpen]);

  async function handleSignOut() {
      await signOut();
      setSignedOut(true);
      router.refresh();
      router.push('/');
      window.location.href = '/';
    }
  

  //when the user clicks on "Contact Sales" it'll trigger opening an email app to contact
  const handleEmailClick = () => {
    const recipient = '<EMAIL>';
    const subject = 'Subject';
    const body = 'Body';

    const mailtoLink = `mailto:${recipient}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    window.location.href = mailtoLink;
  };
  return (
    <header className="border-b-transparent border-gray-200 bg-white shadow-md z-10">
    <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div className="flex justify-between items-center w-full">
        
        {/* logo Section */}
        <div className="flex items-center space-x-0.5 md:space-x-4">
          <Link href="/" onClick={(e) => { e.preventDefault(); window.location.href = '/' }} className="flex items-center">
            <span className="ml-2">
              <img className="h-6 w-auto object-contain" src="/Fylow-copy.png" alt="Logo"/>
            </span>
          </Link>
      
  
          {/* Desktop topbar */}
          <div className="hidden md:flex space-x-3">
            <HoverDropdown
              label="Product"
              items={[
                { icon: <ChartNoAxesCombined className="h-4 w-4" />, label: 'AI-powered audience & market analysis', href: '/adTargeting' },
                { icon: <Clapperboard className="h-4 w-4" />, label: 'AI-powered video campaign', href: '/adTargeting?VideoForm=true' },
                { icon: <BotMessageSquare className="h-4 w-4" />, label: 'AI-powered marketing chat', href: '/marketing-ai' }
              ]}
            />
            <a href="#solutions" className="p-2 text-md text-cyan-700 hover:bg-cyan-50 hover:text-cyan-800 rounded-full">Solutions</a>
            <a href="#about" className="p-2 text-md text-cyan-700 hover:bg-cyan-50 hover:text-cyan-800 rounded-full">About</a>
            <Link href="/pricing" className="p-2 py-2 text-md text-cyan-700 hover:bg-cyan-50 hover:text-cyan-800 rounded-full">Pricing</Link>
          </div>
        </div>
        <div className="hidden md:flex items-center space-x-3 ">
          <Link  onClick = {handleEmailClick} href="#" className=" py-2 text-md text-cyan-700 font-semibold hover:text-cyan-800 font-sans">Contact Sales</Link>
          {user ? (
            <Link href="/dashboard" className="block text-md px-2 py-1 text-black font-semibold">Profile</Link>
          ) : (
            <Link href="/sign-in" className="block text-md px-2 py-1 text-black font-semibold">Log in</Link>
          )}
      
      {user ? (
            <Link onClick={handleSignOut} href="#" className="block text-md px-2 py-1 text-black font-semibold">Sign Out</Link>
          ) : (
            <Link href="/sign-up" className="block  text-md px-2 py-1 font-bold text-white bg-cyan-700 hover:bg-cyan-800 rounded-full text-center w-fit">Join<ArrowRightToLine className="inline align-middle ml-1 h-3 w-3 stroke-white font-sans" /></Link>
          )}
        </div>
        {/* Mobile Toggle */}
        <div className="md:hidden flex justify-end  align-top">
            <button onClick= {() =>setIsMenuOpen(true)} >
               <Menu  className="h-6 w-6 stroke-cyan-700 mr-0 cursor-pointer" />
            </button>
          </div>
      </div>
         
      {/* Mobile Sidebar */}
      {isMenuOpen && (
        <div  className="md:hidden absolute inset-0  mt-14 px-2">
           {isMenuOpen && (
    <div ref={SideBarRef} className=" relative mt-1 md:mt-3 w-64 md:w-64   h-[calc(100vh-4rem)] bg-white shadow-md rounded-md z-50  transition-all duration-200 ease-in-out p-4 space-y-4">
              <div className='flex justify-end'>
              <X onClick={() => setIsMenuOpen(!isMenuOpen)} className="h-6 w-6 stroke-cyan-700 hover:stroke-cyan-800  cursor-pointer" />

              </div>
      <HoverDropdown isMobile = {true}
        label="Product"
        items={[
          { icon: <ChartNoAxesCombined className='h-4 w-4'/>, label: 'AI-powered audience & market analysis', href: '/adTargeting' },
          { icon: <Clapperboard className='h-4 w-4'/>, label: 'AI-powered video campaign', href: '/adTargeting?VideoForm=true' },
          { icon: <BotMessageSquare className='h-4 w-4'/>, label: 'AI-powered marketing chat', href: '/marketing-ai' }
        ]}
      />
      <a href="#solutions" className="block px-2 py-1 text-md text-cyan-700 hover:bg-cyan-50 rounded-md">Solutions</a>
      <a href="#about" className="block px-2 py-1 text-md  text-cyan-700 hover:bg-cyan-50 rounded-md">About</a>
      <Link href="/pricing" className="block px-2 text-md py-1 text-cyan-700 hover:bg-cyan-50 rounded-md">Pricing</Link>
      <Link onClick={handleEmailClick} href="#" className="block px-2 py-1 text-md font-bold text-cyan-700">Contact Sales</Link>



      {user ? (
            <Link href="/dashboard" className="block text-md px-2 py-1 text-black font-semibold">Profile</Link>
          ) : (
            <Link href="/sign-in" className="block text-md px-2 py-1 text-black font-semibold">Log in</Link>
          )}
      
      {user ? (
            <Link onClick={handleSignOut} href="/" className="block text-md px-2 py-1 text-black font-semibold">Sign Out</Link>
          ) : (
            <Link href="/sign-up" className="block  text-md px-2 py-1 font-bold text-white bg-cyan-700 hover:bg-cyan-800 rounded-full text-center w-fit">Join <ArrowRightToLine className="inline align-middle ml-1 h-3 w-3 stroke-white font-sans" /></Link>
          )}
    </div>
  )}
        </div>
      )}
    </div>
  </header>

 
  );
}

export default function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  const [activeNavItem, setActiveNavItem] = useState<string | undefined>(undefined);
  const [isVideoFormOpen, setIsVideoFormOpen] = useState(false);
  const [targetingSummary, setTargetingSummary] = useState<string>("");
  const router = useRouter();
  
  // Removed login redirect to allow all users to access the home page
  // regardless of their login status. This supports generating audience
  // without requiring login.

  const handleNavItemClick = (item: string) => {
    setActiveNavItem(item);
  };


  return (

    <div className="flex flex-col min-h-screen bg-gray-50 " style={{ scrollBehavior: 'smooth' }}>
      <Header 
        activeNavItem={activeNavItem} 
        onNavItemClick={handleNavItemClick}
      />
      <div className="flex flex-1 overflow-hidden">
          <>
            <main className="flex-1 overflow-y-auto overflow-x-hidden">
              <div className="flex-1 h-full px-4 py-0">
                {children}
              </div>
            </main>
          </>
      </div>
      <Divider />
      <Footer />
    </div>
  );
}
