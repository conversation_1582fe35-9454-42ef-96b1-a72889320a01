'use client';

import { useState, useRef } from 'react';
import Link from 'next/link';
import { ChevronDown } from 'lucide-react';

type DropdownItem = {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
};

interface HoverDropdownProps {
  label: string;
  items: DropdownItem[];
  isMobile?: boolean;
}

export default function HoverDropdown({ label, items, isMobile = false }: HoverDropdownProps) {
  const [open, setOpen] = useState(false);
  // so that the dropwdown stops glitching when hovering on
  const timeoutRef = useRef<NodeJS.Timeout | null> (null); 
  const handleMouseEnter = () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      setOpen(true);
  }
  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {setOpen(false)}, 200)
}
  return (
    <div
      className=" inline-block text-left"
      onMouseEnter={!isMobile? handleMouseEnter:undefined}
      onMouseLeave={!isMobile? handleMouseLeave:undefined}
    >
      <div className="flex items-center gap-1 cursor-pointer select-none hover:bg-cyan-50 rounded-md md:rounded-full p-2" onClick={isMobile ? () => setOpen(!open) : undefined}>
        <span className={`text-md  text-cyan-700 ${isMobile ? '' : ' hover:text-cyan-800'}`}>{label}</span>
        <ChevronDown
          className={`h-4 w-4 stroke-cyan-700 hover:text-cyan-800 transition-transform ${
            open ? 'rotate-180' : ''
          }`}
        />
      </div>

      {open && (
        <div className={`${isMobile ? 'mt-2' : 'absolute left-0 mt-2 rounded-md shadow-md bg-white'}  z-50 w-full p-1 `}
      >
          <div className="py-1 flex flex-col">
            {items.map((item, i) =>
            item.href  && item.icon? (
                <Link
                  key={i}
                  href={item.href}
                  className="px-2 py-2 text-sm text-cyan-700 hover:bg-cyan-50 hover:text-cyan-800 rounded-md"
                >
                    <div className='flex flex-row gap-x-1'>
                   {item.icon}
                  {item.label}
                  </div>
                </Link>
              ): item.href ? (
                <Link
                  key={i}
                  href={item.href}
                  className="p-4 py-2 text-sm text-cyan-700 hover:bg-cyan-50 hover:text-cyan-800 rounded-md"
                >
                  {item.label}
                </Link>
              )  : (
                <button
                  key={i}
                  onClick={item.onClick}
                  className="p-4 py-2 text-left text-sm text-cyan-700 hover:bg-cyan-50 hover:text-cyan-800 w-full rounded-md"
                >
                  {item.label}
                </button>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
}
