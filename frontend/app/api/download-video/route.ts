import {renderMediaOnLambda, getRenderProgress} from '@remotion/lambda/client'
import { NextRequest, NextResponse } from 'next/server';


export async function POST(req: NextRequest){
  try {
    // Extract parameters from request body
    const data = await req.json();
    const { showWatermark = true, videoState } = data;
    
    const region = 'us-east-2';
    const functionName = 'remotion-render-4-0-289-mem2048mb-disk2048mb-120sec';

    // Calculate actual duration in frames from videoState or use a default
    const durationInFrames = videoState?.totalFrames || 300;
    
    // Process background music URL for Lambda rendering
    let backgroundMusicUrl = videoState?.backgroundMusicUrl;
    if (backgroundMusicUrl && typeof backgroundMusicUrl === 'string') {
      // If it's a relative path starting with /audio/background/default/, convert to full S3 URL
      if (backgroundMusicUrl.startsWith('/audio/background/default/')) {
        const fileName = backgroundMusicUrl.substring('/audio/background/default/'.length);
        backgroundMusicUrl = `https://remotionlambda-useast2-f6dqsuuieg.s3.us-east-2.amazonaws.com/sites/adcreate-remotion/public/audio/background/default/${fileName}`;
        console.log('Converted default audio path to S3 URL:', backgroundMusicUrl);
      } else if (backgroundMusicUrl.startsWith('/')) {
        // Other relative paths - set to null for Lambda rendering
        console.log('Filtering out relative path background music URL for Lambda rendering:', backgroundMusicUrl);
        backgroundMusicUrl = null;
      } else if (!backgroundMusicUrl.startsWith('http://') && !backgroundMusicUrl.startsWith('https://')) {
        // If it's not a full URL, set to null
        console.log('Filtering out invalid background music URL for Lambda rendering:', backgroundMusicUrl);
        backgroundMusicUrl = null;
      }
    }

    // Prepare input props for the Lambda function
    const inputProps = {
      showWatermark,
      durationInFrames,
      // Pass all videoState properties to the Lambda function
      ...(videoState && {
        segments: videoState.segments,
        fps: videoState.fps,
        framesPerSegment: videoState.framesPerSegment,
        introBlackScreenFrames: videoState.introBlackScreenFrames,
        endScreenFrames: videoState.endScreenFrames,
        backgroundMusicUrl: backgroundMusicUrl,
        introEnabled: videoState.introEnabled,
        endEnabled: videoState.endEnabled,
        backgroundMusicVolume: videoState.backgroundMusicVolume,
        isBackgroundMusicEnabled: videoState.isBackgroundMusicEnabled,
        textOverlays: videoState.textOverlays,
        useSettings: videoState.useSettings,
        brandName: videoState.brandName,
        getVoiceAudioFile: videoState.getVoiceAudioFile,
        scripts: videoState.scripts,
        selectedVoice: videoState.selectedVoice
      })
    };

    console.log('Rendering video with durationInFrames:', durationInFrames);
    console.log('InputProps being sent to Lambda:', JSON.stringify(inputProps, null, 2));
    
    const { bucketName, renderId } = await renderMediaOnLambda({
      region,
      functionName,
      composition: 'VideoComposition',
      serveUrl: 'https://remotionlambda-useast2-f6dqsuuieg.s3.us-east-2.amazonaws.com/sites/adcreate-remotion/index.html',
      inputProps,
      codec: 'h264',
      imageFormat: 'jpeg',
      timeoutInMilliseconds: 120000,
    });

    let progress = null;
    
    // Wait for rendering to complete
    while (!progress?.done) {
      await new Promise((r) => setTimeout(r, 5000));
      progress = await getRenderProgress({ 
        renderId, 
        bucketName, 
        region, 
        functionName
      });
      
      // If a fatal error is encountered, return the error immediately
      if (progress.fatalErrorEncountered) {
        console.error('Render error:', progress.errors);
        return NextResponse.json({ error: 'Video rendering failed' }, { status: 500 });
      }
    }

    // Return the rendered video URL
    return NextResponse.json({ 
      exportFile: progress.outputFile,
      renderId,
      bucketName
    });
  } catch (err: any) {
    console.error('Video download error:', err);
    return NextResponse.json({ error: err.message || 'Internal Server Error' }, { status: 500 });
  }
}