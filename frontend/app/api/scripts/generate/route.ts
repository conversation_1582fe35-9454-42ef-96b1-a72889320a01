import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { generateScript } from '@/lib/services/script-service';

/**
 * API endpoint for script generation
 * This is now a thin wrapper around the script service
 */
export async function POST(req: NextRequest) {
  try {
    const { projectData, segmentNumber, model: requestedModel } = await req.json();
    
    // Get the current user
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Use the extracted service function to generate the script
    const result = await generateScript({
      user,
      projectData,
      segmentNumber,
      requestedModel
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error generating script:', error);
    
    // Handle different error types appropriately
    if (error instanceof Error) {
      const status = 
        error.message.includes('Unauthorized') || error.message.includes('permission') ? 403 :
        error.message.includes('Missing required') ? 400 : 500;
      
      return NextResponse.json(
        { error: error.message },
        { status }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to generate script', details: String(error) },
      { status: 500 }
    );
  }
}