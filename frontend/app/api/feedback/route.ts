import { NextResponse } from 'next/server';
import { sendEmail, isEmailConfigured, verifyConnection } from '@/lib/email';

// Define SMTP environment variables
const SMTP_SENDER_EMAIL = process.env.SMTP_SENDER_EMAIL || '';
const FEEDBACK_RECEIVER_EMAIL = process.env.FEEDBACK_RECEIVER_EMAIL || process.env.SMTP_SENDER_EMAIL || '';

export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json();
    const { subject, message } = body;

    // Validate required fields
    if (!subject || !message) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if email configuration is available
    if (!isEmailConfigured()) {
      return NextResponse.json(
        { 
          message: 'Email service not configured properly', 
          details: 'Check if SMTP_SERVER, SMTP_SENDER_EMAIL, and SMTP_PASSWORD are set in environment variables'
        },
        { status: 500 }
      );
    }

    // Verify SMTP connection before sending
    const connectionValid = await verifyConnection();
    if (!connectionValid) {
      return NextResponse.json(
        { 
          message: 'Failed to connect to email server', 
          details: 'Could not establish a connection with the SMTP server. Check your server settings and credentials.'
        },
        { status: 500 }
      );
    }

    // Prepare email content
    const emailSubject = `[Feedback] ${subject}`;
    const emailText = `
      Subject: ${subject}
      
      Message:
      ${message}
    `;
    
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Feedback Submission</h2>
        <p><strong>Subject:</strong> ${subject}</p>
        <div style="margin-top: 20px;">
          <h3>Message:</h3>
          <p style="white-space: pre-line;">${message}</p>
        </div>
      </div>
    `;

    // Send email
    const success = await sendEmail(
      FEEDBACK_RECEIVER_EMAIL,
      emailSubject,
      emailText,
      emailHtml
    );

    if (!success) {
      throw new Error('Failed to send email. The email delivery system returned an error.');
    }

    // Return success response
    return NextResponse.json({ message: 'Feedback sent successfully' });
  } catch (error) {
    console.error('Error sending feedback:', error);
    
    // Return detailed error response
    return NextResponse.json(
      { 
        message: 'Failed to send feedback', 
        details: error instanceof Error ? error.message : 'An unexpected error occurred during email delivery',
        error: process.env.NODE_ENV === 'development' ? (error as any)?.toString() : undefined
      },
      { status: 500 }
    );
  }
} 