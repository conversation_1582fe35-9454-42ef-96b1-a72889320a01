import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { eq, and } from 'drizzle-orm';
import { teamMembers } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';

export async function GET(request: NextRequest) {
  try {
    // Verify user is logged in
    const currentUser = await getUser();
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    // Get user ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');
    
    if (!userId || isNaN(Number(userId))) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Ensure current user can only query their own role
    if (userId !== currentUser.id) {
      return NextResponse.json(
        { error: 'No permission to query other users\' roles' },
        { status: 403 }
      );
    }

    // Query user's role in the team
    const memberRecord = await db
      .select({
        role: teamMembers.role
      })
      .from(teamMembers)
      .where(eq(teamMembers.userId, userId))
      .limit(1);

    if (memberRecord.length === 0) {
      return NextResponse.json(
        { role: null, error: 'Team member record not found' },
        { status: 404 }
      );
    }

    // Return user's role in the team
    return NextResponse.json({ role: memberRecord[0].role });
  } catch (error) {
    console.error('Error getting team role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
