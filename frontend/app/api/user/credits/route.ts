import { NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { getUserCreditsBalance } from '@/lib/auth/permissions';
import { db } from '@/lib/db/drizzle';
import { usageTracking } from '@/lib/db/schema';
import { eq, and, desc, sql } from 'drizzle-orm';

/**
 * API endpoint to get user's credit balance and consumption history
 */
export async function GET() {
  try {
    // Check if user is authenticated
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get credit balance
    const creditsBalance = await getUserCreditsBalance(user.id);
    
    // Get credit consumption history
    const creditHistory = await db
      .select({
        id: usageTracking.id,
        feature: usageTracking.feature,
        count: usageTracking.count,
        createdAt: usageTracking.createdAt,
        updatedAt: usageTracking.updatedAt,
      })
      .from(usageTracking)
      .where(
        and(
          eq(usageTracking.userId, user.id),
          eq(usageTracking.feature, 'credits_consumption')
        )
      )
      .orderBy(desc(usageTracking.createdAt))
      .limit(20);
    
    // Get feature usage history using SQL aggregate function
    const featureUsage = await db
      .select({
        feature: usageTracking.feature,
        totalUsage: sql<number>`sum(${usageTracking.count})`.as('total_usage')
      })
      .from(usageTracking)
      .where(
        and(
          eq(usageTracking.userId, user.id),
          eq(usageTracking.feature, 'script_generation')
        )
      )
      .groupBy(usageTracking.feature);
    
    return NextResponse.json({
      credits: creditsBalance,
      history: creditHistory,
      featureUsage: featureUsage,
      planName: user.planName || 'Free',
      subscriptionStatus: user.subscriptionStatus || 'inactive'
    });
    
  } catch (error) {
    console.error('Error fetching user credits:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 