import { NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { hasEnoughCredits, CREDIT_COSTS, getUserCreditsBalance } from '@/lib/auth/permissions';

// Define supported model types
type SupportedModel = 'gpt-4.1' | 'gpt-4.1';

export async function POST(request: Request) {
  try {
    // Check if user is authenticated
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body to get feature and parameters
    const body = await request.json();
    const { feature, parameters } = body;
    
    // Calculate required credits based on the feature and parameters
    let requiredCredits = 0;
    
    switch (feature) {
      case 'audience_insight':
        requiredCredits = CREDIT_COSTS.GENERATE_AUDIENCE_INSIGHT;
        break;
      case 'video_campaign':
        if (!parameters?.durationInSeconds) {
          return NextResponse.json(
            { message: 'Missing duration parameter' },
            { status: 400 }
          );
        }
        requiredCredits = parameters.durationInSeconds * CREDIT_COSTS.VIDEO_CAMPAIGN_PER_SECOND;
        break;
      case 'script_generation':
        const modelParam = parameters?.model || 'gpt-4.1';
        // Ensure model is one of the allowed types
        if (modelParam !== 'gpt-4.1' && modelParam !== 'gpt-4.1') {
          return NextResponse.json(
            { message: 'Invalid model type' },
            { status: 400 }
          );
        }
        // Use type assertion since we've validated the model
        const model = modelParam as SupportedModel;
        requiredCredits = CREDIT_COSTS.SCRIPT_GENERATION[model];
        break;
      default:
        return NextResponse.json(
          { message: 'Unknown feature' },
          { status: 400 }
        );
    }
    
    // Get current credit balance
    const creditBalance = await getUserCreditsBalance(user.id);
    
    // Check if user has enough credits
    const hasCredits = await hasEnoughCredits(user.id, requiredCredits);
    
    return NextResponse.json({
      hasEnoughCredits: hasCredits,
      requiredCredits: requiredCredits,
      currentBalance: creditBalance,
      feature: feature
    });
    
  } catch (error) {
    console.error('Error checking credits:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 