import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { db, audioResources } from '@/lib/db';
import { S3Service } from '@/lib/services/s3Service';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  segmentData: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await segmentData.params;
    const resourceId = id;
    if (!(resourceId)) {
      return NextResponse.json(
        { error: 'Invalid resource ID' },
        { status: 400 }
      );
    }

    const [resource] = await db
      .select()
      .from(audioResources)
      .where(eq(audioResources.id, resourceId));

    if (!resource) {
      return NextResponse.json(
        { error: 'Audio resource not found' },
        { status: 404 }
      );
    }

    if (resource.userId !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json(resource);
  } catch (error) {
    console.error('Error fetching audio resource:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  segmentData: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await segmentData.params;
    const resourceId = id;
    if (!(resourceId)) {
      return NextResponse.json(
        { error: 'Invalid resource ID' },
        { status: 400 }
      );
    }

    const [resource] = await db
      .select()
      .from(audioResources)
      .where(eq(audioResources.id, resourceId));

    if (!resource) {
      return NextResponse.json(
        { error: 'Audio resource not found' },
        { status: 404 }
      );
    }

    if (resource.userId !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await request.json();
    const { name, type } = data;

    const [updatedResource] = await db
      .update(audioResources)
      .set({ name, type })
      .where(eq(audioResources.id, resourceId))
      .returning();

    return NextResponse.json(updatedResource);
  } catch (error) {
    console.error('Error updating audio resource:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  segmentData: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await segmentData.params;
    const resourceId = id;
    if (!(resourceId)) {
      return NextResponse.json(
        { error: 'Invalid resource ID' },
        { status: 400 }
      );
    }

    const [resource] = await db
      .select()
      .from(audioResources)
      .where(eq(audioResources.id, resourceId));

    if (!resource) {
      return NextResponse.json(
        { error: 'Audio resource not found' },
        { status: 404 }
      );
    }

    if (resource.userId !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Delete file from S3
    const s3Service = S3Service.getInstance();
    
    // Extract complete key from URL
    // Check if it's a permanent URL or presigned URL
    let key;
    if (resource.url.includes('?')) {
      // If it's a presigned URL, extract key in the original way
      const url = new URL(resource.url);
      key = url.pathname.substring(1); // Remove leading slash
    } else {
      // If it's a permanent URL, extract key directly from URL path
      // Format: https://bucket-name.s3.region.amazonaws.com/audio/user-id/filename
      const urlParts = resource.url.split('.amazonaws.com/');
      if (urlParts.length > 1) {
        key = urlParts[1]; // This is the audio/user-id/filename part
      } else {
        console.error('Unable to extract key from URL:', resource.url);
        throw new Error('Invalid URL format');
      }
    }
    
    try {
      await s3Service.deleteFile(key);
    } catch (error) {
      console.error('Error deleting file from S3:', error);
      // Even if S3 deletion fails, we continue to delete the database record
    }

    // Delete record from database
    await db
      .delete(audioResources)
      .where(eq(audioResources.id, resourceId));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting audio resource:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 