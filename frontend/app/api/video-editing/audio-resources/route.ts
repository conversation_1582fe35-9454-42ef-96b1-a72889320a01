import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { db, audioResources } from '@/lib/db';
import { S3Service } from '@/lib/services/s3Service';
import { eq, and } from 'drizzle-orm';

export async function POST(req: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get('file') as File;
    const name = formData.get('name') as string;
    const type = formData.get('type') as 'background' | 'voice' | 'effect';
    const duration = formData.get('duration') ? parseInt(formData.get('duration') as string) : null;

    if (!file || !name || !type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const key = `audio/${user.id}/${Date.now()}.${fileExtension}`;

    // Upload file to S3
    const s3Service = S3Service.getInstance();
    // First upload the file
    await s3Service.uploadFile(file, key);
    // Then get permanent URL
    const url = s3Service.getPublicUrl(key);

    // Save to database
    const [audioResource] = await db
      .insert(audioResources)
      .values({
        userId: user.id,
        name,
        type,
        url,
        duration,
      })
      .returning();

    return NextResponse.json(audioResource);
  } catch (error) {
    console.error('Error uploading audio:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type') as 'background' | 'voice' | 'effect';

    const resources = await db
      .select()
      .from(audioResources)
      .where(
        type
          ? and(eq(audioResources.userId, user.id), eq(audioResources.type, type))
          : eq(audioResources.userId, user.id)
      );

    return NextResponse.json(resources);
  } catch (error) {
    console.error('Error fetching audio resources:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 