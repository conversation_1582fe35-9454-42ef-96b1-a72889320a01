import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoScripts, videoProjects } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq, and } from 'drizzle-orm';

// Get a specific script by ID
export async function GET(
  request: Request,
  segmentData: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: scriptId } = await segmentData.params;
    const id = scriptId;
    if (!(id)) {
      return NextResponse.json(
        { message: 'Invalid script ID' },
        { status: 400 }
      );
    }

    // Get the script
    const [script] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, id));

    if (!script) {
      return NextResponse.json(
        { message: 'Script not found' },
        { status: 404 }
      );
    }

    // Check if the project belongs to the user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, script.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json(script);
  } catch (error) {
    console.error('Error fetching script:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update a script
export async function PUT(
  request: Request,
  segmentData: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: scriptId } = await segmentData.params;
    const id = scriptId;
    if (!(id)) {
      return NextResponse.json(
        { message: 'Invalid script ID' },
        { status: 400 }
      );
    }

    // Get the existing script
    const [existingScript] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, id));

    if (!existingScript) {
      return NextResponse.json(
        { message: 'Script not found' },
        { status: 404 }
      );
    }

    // Check if the project belongs to the user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, existingScript.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      scriptText, 
      narratorText, 
      phrase,
      narratorAudio, 
      narratorAudioMale,
      narratorAudioFemale,
      audioId, 
      defaultVoice, 
      elevenlabsVoiceIds, 
      status
    } = body;

    // Create a new version if this is a significant update
    if (body.createNewVersion) {
      // Set current version to false
      await db
        .update(videoScripts)
        .set({ isCurrentVersion: false })
        .where(eq(videoScripts.id, id));

      // Create new version
      const [newVersion] = await db
        .insert(videoScripts)
        .values({
          projectId: existingScript.projectId,
          segmentNumber: existingScript.segmentNumber,
          version: existingScript.version + 1,
          isCurrentVersion: true,
          scriptText: scriptText || existingScript.scriptText,
          narratorText: narratorText !== undefined ? narratorText : existingScript.narratorText,
          phrase: phrase !== undefined ? phrase : existingScript.phrase,
          narratorAudio: narratorAudio !== undefined ? narratorAudio : existingScript.narratorAudio,
          narratorAudioMale: narratorAudioMale !== undefined ? narratorAudioMale : existingScript.narratorAudioMale,
          narratorAudioFemale: narratorAudioFemale !== undefined ? narratorAudioFemale : existingScript.narratorAudioFemale,
          audioId: audioId !== undefined ? audioId : existingScript.audioId,
          defaultVoice: defaultVoice !== undefined ? defaultVoice : existingScript.defaultVoice,
          elevenlabsVoiceIds: elevenlabsVoiceIds !== undefined ? elevenlabsVoiceIds : existingScript.elevenlabsVoiceIds,
          status: status || existingScript.status,
        })
        .returning();

      return NextResponse.json(newVersion);
    } else {
      // Update the existing script
      const [updatedScript] = await db
        .update(videoScripts)
        .set({
          scriptText: scriptText || existingScript.scriptText,
          narratorText: narratorText !== undefined ? narratorText : existingScript.narratorText,
          phrase: phrase !== undefined ? phrase : existingScript.phrase,
          narratorAudio: narratorAudio !== undefined ? narratorAudio : existingScript.narratorAudio,
          narratorAudioMale: narratorAudioMale !== undefined ? narratorAudioMale : existingScript.narratorAudioMale,
          narratorAudioFemale: narratorAudioFemale !== undefined ? narratorAudioFemale : existingScript.narratorAudioFemale,
          audioId: audioId !== undefined ? audioId : existingScript.audioId,
          defaultVoice: defaultVoice !== undefined ? defaultVoice : existingScript.defaultVoice,
          elevenlabsVoiceIds: elevenlabsVoiceIds !== undefined ? elevenlabsVoiceIds : existingScript.elevenlabsVoiceIds,
          status: status || existingScript.status,
          updatedAt: new Date(),
        })
        .where(eq(videoScripts.id, id))
        .returning();

      return NextResponse.json(updatedScript);
    }
  } catch (error) {
    console.error('Error updating script:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Delete a script
export async function DELETE(
  request: Request,
  segmentData: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: scriptId } = await segmentData.params;
    const id = scriptId;
    if (!(id)) {
      return NextResponse.json(
        { message: 'Invalid script ID' },
        { status: 400 }
      );
    }

    // Get the script
    const [script] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, id));

    if (!script) {
      return NextResponse.json(
        { message: 'Script not found' },
        { status: 404 }
      );
    }

    // Check if the project belongs to the user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, script.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    // Delete the script
    await db
      .delete(videoScripts)
      .where(eq(videoScripts.id, id));

    return NextResponse.json(
      { message: 'Script deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting script:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}