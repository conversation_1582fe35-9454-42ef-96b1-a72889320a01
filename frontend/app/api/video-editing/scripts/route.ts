import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoScripts, videoProjects } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq, and } from 'drizzle-orm';

// Create a new script
export async function POST(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      projectId, 
      segmentNumber, 
      scriptText, 
      narratorText, 
      phrase,
      defaultVoice 
    } = body;

    if (!projectId || !segmentNumber || !scriptText) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if project exists and belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Check if a script with the same segment number already exists
    const [existingScript] = await db
      .select()
      .from(videoScripts)
      .where(
        and(
          eq(videoScripts.projectId, projectId),
          eq(videoScripts.segmentNumber, segmentNumber),
          eq(videoScripts.isCurrentVersion, true)
        )
      );

    if (existingScript) {
      // If exists, create a new version and update the existing one
      await db
        .update(videoScripts)
        .set({ isCurrentVersion: false })
        .where(eq(videoScripts.id, existingScript.id));
    }

    // Create new script
    const [newScript] = await db
      .insert(videoScripts)
      .values({
        projectId,
        segmentNumber,
        scriptText,
        narratorText: narratorText || null,
        phrase: phrase || null,
        defaultVoice: defaultVoice || null,
        status: 'pending',
      })
      .returning();

    return NextResponse.json(newScript);
  } catch (error) {
    console.error('Error creating script:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get all scripts for a project
export async function GET(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const includeAllVersions = searchParams.get('includeAllVersions') === 'true';
    
    if (!projectId) {
      return NextResponse.json(
        { message: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Check if project exists and belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Get scripts for the project
    let scripts;
    
    if (includeAllVersions) {
      // Get all versions
      scripts = await db
        .select()
        .from(videoScripts)
        .where(eq(videoScripts.projectId, projectId))
        .orderBy(videoScripts.segmentNumber, videoScripts.version);
    } else {
      // Get only current versions
      scripts = await db
        .select()
        .from(videoScripts)
        .where(
          and(
            eq(videoScripts.projectId, projectId),
            eq(videoScripts.isCurrentVersion, true)
          )
        )
        .orderBy(videoScripts.segmentNumber);
    }
    
    return NextResponse.json(scripts);
  } catch (error) {
    console.error('Error fetching scripts:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}