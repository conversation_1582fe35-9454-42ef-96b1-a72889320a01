import { NextResponse } from 'next/server';
import { getUser, cleanupDuplicateScripts } from '@/lib/db/queries';

/**
 * API endpoint to clean up duplicate scripts for a project
 * POST /api/video-editing/scripts/cleanup
 */
export async function POST(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { projectId } = body;

    if (!projectId) {
      return NextResponse.json(
        { message: 'Missing project ID' },
        { status: 400 }
      );
    }

    // Perform the cleanup operation
    const result = await cleanupDuplicateScripts(projectId);

    return NextResponse.json({
      message: `Successfully cleaned up project scripts. Fixed ${result.fixedCount} duplicates.`,
      fixedCount: result.fixedCount
    });
  } catch (error) {
    console.error('Error cleaning up scripts:', error);
    return NextResponse.json(
      { 
        message: 'Error cleaning up scripts', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
} 