import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoProjects, videoScripts, usageTracking } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq, and } from 'drizzle-orm';
import { 
  canGenerateScripts, 
  getAllowedModels,
  CREDIT_COSTS,
  hasEnoughCredits,
  deductCredits,
  getUserCreditsBalance
} from '@/lib/auth/permissions';
// Import the script generation service
import { generateScript } from '@/lib/services/script-service';

export async function POST(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { projectId, segmentCount } = body;

    if (!projectId) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Add request ID for logging and debugging
    const requestId = Math.random().toString(36).substring(2, 9);
    console.log(`[${requestId}] Script generation request started for project: ${projectId}`);

    // Check if project exists and belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Check if user can generate scripts
    if (!canGenerateScripts(user)) {
      return NextResponse.json(
        { error: `You do not have permission to generate scripts` },
        { status: 403 }
      );
    }
    
    // Calculate number of segments based on adLength if not provided
    // Assuming each segment is 5 seconds
    const calculatedSegmentCount = segmentCount || Math.ceil(project.adLength / 5);
    
    // Comprehensive fetch of existing scripts to avoid duplicates
    const existingScripts = await db
      .select()
      .from(videoScripts)
      .where(
        eq(videoScripts.projectId, projectId)
      );
    
    console.log(`[${requestId}] Found ${existingScripts.length} existing scripts for project ${projectId}`);
    
    // Create a map of segment numbers to scripts for faster lookup
    const existingScriptMap = new Map();
    for (const script of existingScripts) {
      // If we already have a script for this segment, only keep the current version
      if (script.isCurrentVersion || !existingScriptMap.has(script.segmentNumber)) {
        existingScriptMap.set(script.segmentNumber, script);
      }
    }
    
    // Get only the current version scripts
    const currentVersionScripts = existingScripts.filter(script => script.isCurrentVersion);
    
    // Calculate how many new scripts need to be generated
    const newScriptsNeeded = calculatedSegmentCount - currentVersionScripts.length;
    
    if (newScriptsNeeded <= 0) {
      console.log(`[${requestId}] All segments already have scripts for project ${projectId}`);
      return NextResponse.json({
        message: 'All segments already have scripts',
        scripts: currentVersionScripts
      });
    }
    
    console.log(`[${requestId}] Need to generate ${newScriptsNeeded} new scripts for project ${projectId}`);
    
    // Get the allowed models for this user
    const allowedModels = getAllowedModels(user);
    const model = allowedModels.includes('gpt-4.1') ? 'gpt-4.1' : 'gpt-4.1';
    
    // CREDITS HANDLING REMOVED: No longer checking or deducting credits for script generation
    // since these costs are already included in the video campaign credits
    
    // Improve the script generation process by using a single transaction for all segments
    // This greatly reduces the chance of creating duplicate scripts
    const generateScriptsWithTransaction = async (segmentsToProcess: number[]) => {
      const generatedScripts: any[] = [];
      
      // We'll collect all the script data first to minimize time in transaction
      const scriptDataBySegment = new Map<number, any>();
      
      // First, collect all script data using the service directly instead of API calls
      for (const segmentNumber of segmentsToProcess) {
        try {
          // Use the script generation service directly instead of making an HTTP request
          const scriptData = await generateScript({
            user,
            projectData: {
              brandName: project.brandName,
              summary: project.summary || '',
              description: project.description || ''
            },
            segmentNumber,
            requestedModel: model
          });
          
          scriptDataBySegment.set(segmentNumber, scriptData);
          
        } catch (error) {
          console.error(`[${requestId}] Error generating script data for segment ${segmentNumber}:`, error);
        }
      }
      
      // Now use a single transaction to insert all scripts
      // This minimizes the time window for race conditions
      if (scriptDataBySegment.size > 0) {
        try {
          await db.transaction(async (tx) => {
            console.log(`[${requestId}] Starting transaction to insert ${scriptDataBySegment.size} scripts`);
            
            // Double-check all segments in the transaction
            for (const segmentNumber of scriptDataBySegment.keys()) {
              // Check if script already exists inside transaction
              const [existingScriptInTx] = await tx
                .select()
                .from(videoScripts)
                .where(
                  and(
                    eq(videoScripts.projectId, projectId),
                    eq(videoScripts.segmentNumber, segmentNumber),
                    eq(videoScripts.isCurrentVersion, true)
                  )
                );
              
              if (existingScriptInTx) {
                console.log(`[${requestId}] Script for segment ${segmentNumber} already exists in DB, skipping`);
                generatedScripts.push(existingScriptInTx);
                continue;
              }
              
              const scriptData = scriptDataBySegment.get(segmentNumber);
              // Store the generated script in the database
              const [newScript] = await tx
                .insert(videoScripts)
                .values({
                  projectId,
                  segmentNumber,
                  scriptText: scriptData.script,
                  narratorText: scriptData.narrator,
                  phrase: scriptData.phrase,
                  status: 'pending',
                  isCurrentVersion: true
                })
                .returning();
              
              // USAGE TRACKING REMOVED: We no longer track script generation separately
              // since it's included in the video campaign cost
              
              generatedScripts.push(newScript);
            }
            
            console.log(`[${requestId}] Successfully inserted ${generatedScripts.length} scripts`);
          });
        } catch (error) {
          console.error(`[${requestId}] Transaction error while inserting scripts:`, error);
        }
      }
      
      return generatedScripts;
    };
    
    // Generate scripts for segments that don't have one yet
    const segmentsToProcess = [];
    for (let i = 1; i <= calculatedSegmentCount; i++) {
      if (!existingScriptMap.has(i)) {
        segmentsToProcess.push(i);
      }
    }
    
    // Generate the scripts
    let generatedScripts: any[] = [];
    
    if (segmentsToProcess.length > 0) {
      // CREDIT DEDUCTION REMOVED: No longer deducting credits here
      // Credits are now only deducted once during video project creation
      
      // Generate scripts without deducting credits
      generatedScripts = await generateScriptsWithTransaction(segmentsToProcess);
      
      console.log(`[${requestId}] Generated ${generatedScripts.length} scripts for project ${projectId}`);
    }
    
    // Combine existing and newly generated scripts
    const allScripts = [...currentVersionScripts, ...generatedScripts];
    
    return NextResponse.json({
      message: 'Scripts generated successfully',
      scripts: allScripts
    });
    
  } catch (error) {
    console.error('Error generating scripts:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
