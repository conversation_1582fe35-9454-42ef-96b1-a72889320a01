import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoProjects, projectDisplaySettings } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq, and } from 'drizzle-orm';

// Get a specific video project by ID
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await the params object before destructuring
    const resolvedParams = await params;
    const { id: projectId } = resolvedParams;

    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Project not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(project);
  } catch (error) {
    console.error('Error fetching video project:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update a video project
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await the params object before destructuring
    const resolvedParams = await params;
    const { id: projectId } = resolvedParams;

    // Check if project exists and belongs to user
    const [existingProject] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!existingProject) {
      return NextResponse.json(
        { message: 'Project not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { title, brandName, summary, description, adLength, status } = body;

    // Update the project
    const [updatedProject] = await db
      .update(videoProjects)
      .set({
        title: title || existingProject.title,
        brandName: brandName || existingProject.brandName,
        summary: summary !== undefined ? summary : existingProject.summary,
        description: description || existingProject.description,
        adLength: adLength || existingProject.adLength,
        status: status || existingProject.status,
        updatedAt: new Date(),
      })
      .where(eq(videoProjects.id, projectId))
      .returning();

    return NextResponse.json(updatedProject);
  } catch (error) {
    console.error('Error updating video project:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Delete a video project
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await the params object before destructuring
    const resolvedParams = await params;
    const { id: projectId } = resolvedParams;

    // Check if project exists and belongs to user
    const [existingProject] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, projectId),
          eq(videoProjects.userId, user.id)
        )
      );
    
    //find if the project exists in project display settings table
    const [projectdisplaySettings] = await db
      .select()
      .from(projectDisplaySettings)
      .where(
        and(
          eq(projectDisplaySettings.projectId, projectId),
        )
      );

    if (!existingProject) {
      return NextResponse.json(
        { message: 'Project not found' },
        { status: 404 }
      );
    }

    
    // Delete the relevant row in project display settings table
    await db
      .delete(projectDisplaySettings)
      .where(eq(projectDisplaySettings.projectId, projectId));
    
    // Delete the project
    await db
      .delete(videoProjects)
      .where(eq(videoProjects.id, projectId));  
    return NextResponse.json(
      { message: 'Project deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting video project:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}