import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { and, eq } from 'drizzle-orm';
import { projectTextOverlays, videoProjects } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';

// GET - Retrieve all text overlays for a specific project
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Await params before accessing properties
    const resolvedParams = await params;
    const projectId = resolvedParams.id;
    
    // Verify the project exists and belongs to the user
    const project = await db.query.videoProjects.findFirst({
      where: and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id)
      ),
    });
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Get all text overlays for this project
    const overlays = await db.query.projectTextOverlays.findMany({
      where: eq(projectTextOverlays.projectId, projectId),
    });
    
    return NextResponse.json(overlays);
  } catch (error) {
    console.error('Error fetching text overlays:', error);
    return NextResponse.json(
      { error: 'Failed to fetch text overlays' },
      { status: 500 }
    );
  }
}

// POST - Create a new text overlay
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Await params before accessing properties
    const resolvedParams = await params;
    const projectId = resolvedParams.id;
    const data = await request.json();
    
    // Verify the project exists and belongs to the user
    const project = await db.query.videoProjects.findFirst({
      where: and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id)
      ),
    });
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Create a new text overlay
    const newOverlay = await db.insert(projectTextOverlays).values({
      projectId,
      overlayData: data,
    }).returning();
    
    return NextResponse.json(newOverlay[0]);
  } catch (error) {
    console.error('Error creating text overlay:', error);
    return NextResponse.json(
      { error: 'Failed to create text overlay' },
      { status: 500 }
    );
  }
}

// PUT - Update a specific text overlay
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Await params before accessing properties
    const resolvedParams = await params;
    const projectId = resolvedParams.id;
    const { overlayId, ...data } = await request.json();
    
    if (!overlayId) {
      return NextResponse.json(
        { error: 'Overlay ID is required' },
        { status: 400 }
      );
    }
    
    // Verify the project exists and belongs to the user
    const project = await db.query.videoProjects.findFirst({
      where: and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id)
      ),
    });
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Update the text overlay
    const updatedOverlay = await db.update(projectTextOverlays)
      .set({
        overlayData: data,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(projectTextOverlays.id, overlayId),
          eq(projectTextOverlays.projectId, projectId)
        )
      )
      .returning();
    
    if (updatedOverlay.length === 0) {
      return NextResponse.json(
        { error: 'Text overlay not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedOverlay[0]);
  } catch (error) {
    console.error('Error updating text overlay:', error);
    return NextResponse.json(
      { error: 'Failed to update text overlay' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a specific text overlay
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Await params before accessing properties
    const resolvedParams = await params;
    const projectId = resolvedParams.id;
    const url = new URL(request.url);
    const overlayId = url.searchParams.get('overlayId');
    
    if (!overlayId) {
      return NextResponse.json(
        { error: 'Overlay ID is required' },
        { status: 400 }
      );
    }
    
    // Verify the project exists and belongs to the user
    const project = await db.query.videoProjects.findFirst({
      where: and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id)
      ),
    });
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Delete the text overlay
    const deletedOverlay = await db.delete(projectTextOverlays)
      .where(
        and(
          eq(projectTextOverlays.id, overlayId),
          eq(projectTextOverlays.projectId, projectId)
        )
      )
      .returning();
    
    if (deletedOverlay.length === 0) {
      return NextResponse.json(
        { error: 'Text overlay not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting text overlay:', error);
    return NextResponse.json(
      { error: 'Failed to delete text overlay' },
      { status: 500 }
    );
  }
}
