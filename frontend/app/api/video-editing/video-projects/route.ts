import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoProjects } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq } from 'drizzle-orm';
import { calculateVideoCampaignCredits, hasEnoughCredits, deductCredits, getUserCreditsBalance } from '@/lib/auth/permissions';

// Create a new video project
export async function POST(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, brandName, summary, description, adLength } = body;

    if (!title || !brandName || !description || !adLength) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Calculate required credits based on video duration
    const requiredCredits = calculateVideoCampaignCredits(adLength);
    
    // Get user's current credit balance
    const currentCredits = await getUserCreditsBalance(user.id);
    
    // Check if user has enough credits
    if (!(await hasEnoughCredits(user.id, requiredCredits))) {
      return NextResponse.json(
        { 
          message: 'Insufficient credits', 
          error: `This project requires ${requiredCredits} credits. You have ${currentCredits} credits available.`,
          requiredCredits,
          currentCredits,
          creditSystem: true
        },
        { status: 403 }
      );
    }

    // Create the video project
    const [newProject] = await db
      .insert(videoProjects)
      .values({
        userId: user.id,
        title,
        brandName,
        summary,
        description,
        adLength,
        status: 'pending',
      })
      .returning();

    if (!newProject) {
      return NextResponse.json(
        { message: 'Failed to create project' },
        { status: 500 }
      );
    }

    // Deduct credits for project creation
    const deducted = await deductCredits(user.id, requiredCredits, 'video_campaign');
    
    if (!deducted) {
      // If credits deduction failed, roll back project creation
      await db.delete(videoProjects).where(eq(videoProjects.id, newProject.id));
      
      return NextResponse.json(
        { message: 'Failed to deduct credits' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Video project created successfully',
      project: newProject,
      creditsDeducted: requiredCredits
    });
  } catch (error) {
    console.error('Error creating video project:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get all video projects
export async function GET(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get all projects for the user
    const projects = await db
      .select()
      .from(videoProjects)
      .where(eq(videoProjects.userId, user.id))
      .orderBy(videoProjects.createdAt);

    return NextResponse.json(projects);
  } catch (error) {
    console.error('Error fetching video projects:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}