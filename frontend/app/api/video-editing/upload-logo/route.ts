import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { S3Service } from '@/lib/services/s3Service';

/**
 * API route handler for logo and product asset uploads
 * Uploads files to S3 and returns the permanent URL
 * Supports:
 * - intro/end: logo images for intro/end screens (image only)
 * - product: product images or videos for video generation (image or video)
 */
export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string; // 'intro', 'end', or 'product'

    // Validate required fields
    if (!file || !type || !['intro', 'end', 'product'].includes(type)) {
      return NextResponse.json(
        { error: 'Missing required fields or invalid type' },
        { status: 400 }
      );
    }

    // Validate file type based on upload type
    if (type === 'product') {
      // Product can be image or video
      if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        return NextResponse.json(
          { error: 'Product must be an image or video file' },
          { status: 400 }
        );
      }
    } else {
      // Intro and end logos must be images
      if (!file.type.startsWith('image/')) {
        return NextResponse.json(
          { error: 'Logo must be an image file' },
          { status: 400 }
        );
      }
    }

    // Set maximum file size: 30MB for videos, 5MB for images
    const maxSize = type === 'product' && file.type.startsWith('video/') 
      ? 30 * 1024 * 1024  // 30MB for videos
      : 5 * 1024 * 1024;  // 5MB for images
    
    if (file.size > maxSize) {
      const sizeLabel = maxSize === 30 * 1024 * 1024 ? '30MB' : '5MB';
      return NextResponse.json(
        { error: `File size exceeds maximum allowed (${sizeLabel})` },
        { status: 400 }
      );
    }

    // Generate unique filename within appropriate folder structure
    const fileExtension = file.name.split('.').pop();
    
    // Structure: 
    // - logo/[userId]/[type]/timestamp.ext for intro/end
    // - product/[userId]/timestamp.ext for product
    const key = type === 'product'
      ? `product/${user.id}/${Date.now()}.${fileExtension}`
      : `logo/${user.id}/${type}/${Date.now()}.${fileExtension}`;

    // Upload file to S3
    const s3Service = S3Service.getInstance();
    await s3Service.uploadFile(file, key);
    
    // Get permanent URL
    const url = s3Service.getPublicUrl(key);

    return NextResponse.json({
      success: true,
      url: url,
      type: type
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 