import { NextResponse } from 'next/server';
import { DEFAULT_VOICES, DEFAULT_MALE_VOICE, DEFAULT_FEMALE_VOICE } from '@/lib/constants/voices';

/**
 * GET /api/video-editing/elevenlabs/default-voices
 * Get default voices configuration
 */
export async function GET() {
  try {
    return NextResponse.json({
      defaultVoices: DEFAULT_VOICES,
      defaultMaleVoice: DEFAULT_MALE_VOICE,
      defaultFemaleVoice: DEFAULT_FEMALE_VOICE
    });
  } catch (error) {
    console.error('Error in default voices API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch default voices' },
      { status: 500 }
    );
  }
}
