import { NextRequest, NextResponse } from 'next/server';
import { elevenLabsService } from '@/lib/services/elevenLabsService';

/**
 * GET /api/video-editing/elevenlabs/[voiceId]
 * Get a specific voice by ID
 */
export async function GET(
  request: NextRequest,
  segmentData: { params: Promise<{ voiceId: string }> }
) {
  try {
    const { voiceId } = await segmentData.params;
    
    if (!voiceId) {
      return NextResponse.json(
        { error: 'Voice ID is required' },
        { status: 400 }
      );
    }

    const voice = await elevenLabsService.getVoice(voiceId);
    
    if (!voice) {
      return NextResponse.json(
        { error: 'Voice not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ voice });
  } catch (error) {
    console.error('Error in ElevenLabs API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch voice from ElevenLabs' },
      { status: 500 }
    );
  }
}
