import { NextRequest, NextResponse } from 'next/server';
import { elevenLabsService } from '@/lib/services/elevenLabsService';

/**
 * GET /api/video-editing/elevenlabs
 * Get all available voices from ElevenLabs
 */
export async function GET() {
  try {
    const voices = await elevenLabsService.getVoices();
    return NextResponse.json({ voices });
  } catch (error) {
    console.error('Error in ElevenLabs API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch voices from ElevenLabs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/video-editing/elevenlabs
 * Generate speech from text
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text, voiceId, modelId, voiceSettings, outputFormat } = body;

    if (!text || !voiceId) {
      return NextResponse.json(
        { error: 'Text and voiceId are required' },
        { status: 400 }
      );
    }

    // Get concurrency manager status for debugging
    const manager = elevenLabsService.getConcurrencyManager();
    const queueStatus = manager.getStatus();
    console.log(`[ElevenLabs API] Request received. Concurrency status: ${queueStatus}`);

    try {
      const audioBase64 = await elevenLabsService.generateSpeechAsBase64(
        text,
        voiceId,
        {
          modelId,
          voiceSettings,
          outputFormat,
        }
      );

      return NextResponse.json({ audio: audioBase64 });
    } catch (error) {
      // Check if the error contains a message about too many concurrent requests
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('too_many_concurrent_requests') || 
          errorMessage.includes('Too Many Requests')) {
        return NextResponse.json(
          { 
            error: 'Too many concurrent requests to ElevenLabs API', 
            details: errorMessage,
            queueStatus: manager.getStatus()
          },
          { status: 429 }  // Use 429 Too Many Requests status code
        );
      }
      
      // Re-throw other errors to be caught by the outer catch
      throw error;
    }
  } catch (error) {
    console.error('Error in ElevenLabs API:', error);
    return NextResponse.json(
      { error: 'Failed to generate speech from ElevenLabs' },
      { status: 500 }
    );
  }
}
