import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoSegments } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { lumaLabsService } from '@/lib/services/lumaLabsService';

// Mock video URL for testing
const mockVideoUrl = 'https://storage.cdn-luma.com/dream-machine/af5d4efe-44cc-4252-b6e4-a15c479b4357/aaad5f3a-2381-41d5-aa10-6429c6a6cdce/2025-03-12T10-18-52_in_an_upscale_indoor_watermarked.mp4';

// Helper function to handle callback logic for both GET and POST
async function handleCallback(segmentId: string, data: any) {
  console.log(`Processing callback data for segment ID: ${segmentId}`, data);
  
  // Extract generation data
  const generationId = data?.id;
  const state = data?.state;
  const videoUrl = data?.assets?.video;
  const failureReason = data?.failure_reason;
  
  console.log(`Extracted info - generationId: ${generationId}, state: ${state}, videoUrl: ${videoUrl ? 'present' : 'not present'}`);
  
  if (!generationId && !state && !videoUrl) {
    console.log('No useful data found in callback. Cannot update segment.');
    return false;
  }
  
  // Update the segment with the generation status and URL
  if (state === 'completed' && videoUrl) {
    console.log(`Updating segment ${segmentId} with video URL: ${videoUrl}`);
    
    await db
      .update(videoSegments)
      .set({
        status: 'completed',
        videoUrl: videoUrl,
        completedAt: new Date()
      })
      .where(eq(videoSegments.id, segmentId));
      
    console.log(`Successfully updated segment ${segmentId} to completed`);
    return true;
  } else if (state === 'failed') {
    console.log(`Updating segment ${segmentId} to failed with reason: ${failureReason || 'Unknown'}`);
    
    await db
      .update(videoSegments)
      .set({
        status: 'failed'
      })
      .where(eq(videoSegments.id, segmentId));
      
    console.log(`Updated segment ${segmentId} to failed`);
    return true;
  }
  
  console.log(`No update needed for segment ${segmentId}, state: ${state}`);
  return false;
}

// Handler for GET requests
export async function GET(request: Request) {
  console.log('GET callback received:', request.url);
  
  try {
    // Get segment ID from query params
    const { searchParams } = new URL(request.url);
    const segmentId = searchParams.get('segmentId');
    
    if (!segmentId) {
      console.error('Missing segment ID in GET callback');
      return NextResponse.json(
        { message: 'Missing segment ID' },
        { status: 400 }
      );
    }
    
    console.log(`Processing GET callback for segment ID: ${segmentId}`);
    
    // Check for data in request body
    let callbackData = null;
    
    // Try to get data from body, if this is a generated GET request with a payload
    try {
      // NOTE: This is normally not standard for GET requests, but we'll try it
      // in case Luma Labs is sending data this way
      callbackData = await request.json().catch(() => null);
      if (callbackData) {
        console.log('Found data in GET request body:', JSON.stringify(callbackData));
      }
    } catch (error) {
      console.log('No data in GET request body or invalid JSON');
    }
    
    // If we got data from the body, process it
    if (callbackData) {
      const wasUpdated = await handleCallback(segmentId, callbackData);
      
      return NextResponse.json({ 
        message: wasUpdated 
          ? 'GET callback processed successfully, segment updated' 
          : 'GET callback processed successfully, no update needed',
        segmentId
      });
    }
    
    // If no data in body, try to get generationId from query params
    const generationId = searchParams.get('generationId') || searchParams.get('id');
    
    if (generationId) {
      console.log(`Fetching generation info for ID: ${generationId}`);
      try {
        const generationInfo = await lumaLabsService.getGeneration(generationId);
        
        console.log(`Generation info received:`, generationInfo);
        
        const wasUpdated = await handleCallback(segmentId, {
          id: generationId,
          state: generationInfo.status === 'completed' ? 'completed' : 
                 generationInfo.status === 'failed' ? 'failed' : 'processing',
          assets: { video: generationInfo.url },
          failure_reason: generationInfo.error
        });
        
        return NextResponse.json({ 
          message: wasUpdated 
            ? 'GET callback processed successfully with generationId, segment updated' 
            : 'GET callback processed successfully with generationId, no update needed',
          segmentId
        });
      } catch (error) {
        console.error(`Error fetching generation info:`, error);
      }
    } else {
      // Manual update - use query parameters to update the segment if provided
      const status = searchParams.get('status') || searchParams.get('state');
      const videoUrl = searchParams.get('url') || searchParams.get('video');
      
      if (status || videoUrl) {
        console.log(`Using query parameters for update - status: ${status}, videoUrl: ${videoUrl ? 'present' : 'not present'}`);
        
        const wasUpdated = await handleCallback(segmentId, {
          state: status,
          assets: videoUrl ? { video: videoUrl } : undefined
        });
        
        return NextResponse.json({ 
          message: wasUpdated 
            ? 'GET callback processed successfully with query params, segment updated' 
            : 'GET callback processed successfully with query params, no update needed',
          segmentId
        });
      }
      
      // If we get here, we don't have enough information to update the segment
      console.log(`Insufficient data to update segment ${segmentId}. Manual update may be required.`);
    }

    return NextResponse.json({ 
      message: 'GET callback processed, but insufficient data to update segment',
      segmentId
    });
  } catch (error) {
    console.error('Error processing GET callback:', error);
    return NextResponse.json(
      { message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}

// Handler for POST requests
export async function POST(request: Request) {
  console.log('POST callback received:', request.url);
  
  try {
    // Get segment ID from query params
    const { searchParams } = new URL(request.url);
    const segmentId = searchParams.get('segmentId');
    
    if (!segmentId) {
      console.error('Missing segment ID in POST callback');
      return NextResponse.json(
        { message: 'Missing segment ID' },
        { status: 400 }
      );
    }
    
    console.log(`Processing POST callback for segment ID: ${segmentId}`);

    // Check if mock mode is enabled in lumaLabsService
    if (lumaLabsService.getMockMode()) {
      console.log('MOCK MODE: Auto-completing segment in callback route');
      
      await db
        .update(videoSegments)
        .set({
          status: 'completed',
          videoUrl: mockVideoUrl,
          completedAt: new Date()
        })
        .where(eq(videoSegments.id, segmentId));

      return NextResponse.json({ 
        message: 'Callback processed successfully (MOCK MODE)',
        mockVideoUrl
      });
    }
    
    // Parse the request body from Luma Labs
    let callbackData;
    try {
      callbackData = await request.json();
      console.log('Callback body received:', JSON.stringify(callbackData));
    } catch (error) {
      console.error('Error parsing request body:', error);
      return NextResponse.json(
        { message: 'Invalid request body' },
        { status: 400 }
      );
    }
    
    const wasUpdated = await handleCallback(segmentId as string, callbackData);

    return NextResponse.json({ 
      message: wasUpdated 
        ? 'POST callback processed successfully, segment updated' 
        : 'POST callback processed successfully, no update needed',
      segmentId
    });
  } catch (error) {
    console.error('Error processing Luma Labs callback:', error);
    return NextResponse.json(
      { message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
