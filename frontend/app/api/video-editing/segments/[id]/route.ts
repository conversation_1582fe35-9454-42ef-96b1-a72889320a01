import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoSegments, videoScripts, videoProjects } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq, and } from 'drizzle-orm';

// get single video segment
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id: segmentId } = resolvedParams;
    const id = segmentId;
    if (!(id)) {
      return NextResponse.json(
        { message: 'Invalid segment ID' },
        { status: 400 }
      );
    }

    // get video segment
    const [segment] = await db
      .select()
      .from(videoSegments)
      .where(eq(videoSegments.id, id));

    if (!segment) {
      return NextResponse.json(
        { message: 'Segment not found' },
        { status: 404 }
      );
    }

    // get associated script
    const [script] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, segment.scriptId));

    if (!script) {
      return NextResponse.json(
        { message: 'Associated script not found' },
        { status: 404 }
      );
    }

    // check project belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, script.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json(segment);
  } catch (error) {
    console.error('Error fetching video segment:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// update video segment
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id: segmentId } = resolvedParams;
    const id = segmentId;
    if (!(id)) {
      return NextResponse.json(
        { message: 'Invalid segment ID' },
        { status: 400 }
      );
    }

    // get existing video segment
    const [existingSegment] = await db
      .select()
      .from(videoSegments)
      .where(eq(videoSegments.id, id));

    if (!existingSegment) {
      return NextResponse.json(
        { message: 'Segment not found' },
        { status: 404 }
      );
    }

    // get associated script
    const [script] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, existingSegment.scriptId));

    if (!script) {
      return NextResponse.json(
        { message: 'Associated script not found' },
        { status: 404 }
      );
    }

    // check project belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, script.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      generationId, 
      videoUrl, 
      status, 
      completedAt 
    } = body;

    // create new version if this is a major update
    if (body.createNewVersion) {
      // set current version to false
      await db
        .update(videoSegments)
        .set({ isCurrentVersion: false })
        .where(eq(videoSegments.id, id));

      // create new version
      const [newVersion] = await db
        .insert(videoSegments)
        .values({
          scriptId: existingSegment.scriptId,
          version: existingSegment.version + 1,
          isCurrentVersion: true,
          generationId: generationId !== undefined ? generationId : existingSegment.generationId,
          videoUrl: videoUrl !== undefined ? videoUrl : existingSegment.videoUrl,
          status: status || existingSegment.status,
          completedAt: completedAt !== undefined ? new Date(completedAt) : existingSegment.completedAt,
        })
        .returning();

      return NextResponse.json(newVersion);
    } else {
      // update existing video segment
      const [updatedSegment] = await db
        .update(videoSegments)
        .set({
          generationId: generationId !== undefined ? generationId : existingSegment.generationId,
          videoUrl: videoUrl !== undefined ? videoUrl : existingSegment.videoUrl,
          status: status || existingSegment.status,
          completedAt: completedAt !== undefined ? new Date(completedAt) : existingSegment.completedAt,
        })
        .where(eq(videoSegments.id, id))
        .returning();

      return NextResponse.json(updatedSegment);
    }
  } catch (error) {
    console.error('Error updating video segment:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// delete video segment
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id: segmentId } = resolvedParams;
    const id = segmentId;
    if (!(id)) {
      return NextResponse.json(
        { message: 'Invalid segment ID' },
        { status: 400 }
      );
    }

    // get existing video segment
    const [existingSegment] = await db
      .select()
      .from(videoSegments)
      .where(eq(videoSegments.id, id));

    if (!existingSegment) {
      return NextResponse.json(
        { message: 'Segment not found' },
        { status: 404 }
      );
    }

    // get associated script
    const [script] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, existingSegment.scriptId));

    if (!script) {
      return NextResponse.json(
        { message: 'Associated script not found' },
        { status: 404 }
      );
    }

    // check project belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, script.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    // delete the segment
    await db
      .delete(videoSegments)
      .where(eq(videoSegments.id, id));

    return NextResponse.json(
      { message: 'Segment deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting video segment:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
