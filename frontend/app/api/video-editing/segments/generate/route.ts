import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoSegments, videoScripts, videoProjects } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq, and } from 'drizzle-orm';
import { lumaLabsService } from '@/lib/services/lumaLabsService';



/**
 * Extract camera movement description from script text
 * This preserves dynamic elements for video generation
 */
function extractCameraMovement(scriptText: string): string {
  const hasMovement = scriptText.match(/(camera|shot|pan|zoom|rotate|dynamic|smooth|track|movement|transitions)/gi);
  return hasMovement ? scriptText : 'Smooth cinematic camera movement with professional transitions';
}

export async function POST(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { scriptId } = body;

    if (!scriptId) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if script exists and belongs to user
    const [script] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, scriptId));

    if (!script) {
      return NextResponse.json(
        { message: 'Script not found' },
        { status: 404 }
      );
    }

    // Check if project belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, script.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    // 检查是否有正在处理中或已完成的段落
    const existingSegments = await db
      .select()
      .from(videoSegments)
      .where(
        and(
          eq(videoSegments.scriptId, scriptId),
          eq(videoSegments.isCurrentVersion, true)
        )
      );

    // 如果存在正在处理中或已完成的段落，直接返回该段落
    if (existingSegments.length > 0) {
      const activeSegment = existingSegments[0];
      
      // 如果段落状态为 pending 或 processing，直接返回
      if (activeSegment.status === 'pending' || activeSegment.status === 'processing') {
        console.log(`Segment ${activeSegment.id} for script ${scriptId} is already being processed, returning existing segment`);
        return NextResponse.json({
          message: 'Video generation already in progress',
          segment: activeSegment
        });
      }
      
      // 如果段落状态为 completed，直接返回
      if (activeSegment.status === 'completed') {
        console.log(`Segment ${activeSegment.id} for script ${scriptId} is already completed, returning existing segment`);
        return NextResponse.json({
          message: 'Video already generated',
          segment: activeSegment
        });
      }
      
      // 如果段落状态为 failed，则创建新版本
      console.log(`Previous segment ${activeSegment.id} for script ${scriptId} failed, creating new version`);
      await db
        .update(videoSegments)
        .set({ isCurrentVersion: false })
        .where(eq(videoSegments.id, activeSegment.id));
    }

    // Create a new segment first to get the ID
    const [newSegment] = await db
      .insert(videoSegments)
      .values({
        scriptId,
        version: existingSegments.length > 0 ? existingSegments[0].version + 1 : 1,
        isCurrentVersion: true,
        generationId: null,
        status: 'processing',
      })
      .returning();

    console.log(`Created new segment ${newSegment.id} for script ${scriptId}`);

    // Generate the callback URL
    const callbackUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/video-editing/segments/callback?segmentId=${newSegment.id}`;

    // Call Luma Labs API to generate video
    try {
      // Check if project has a product image/video for enhanced generation
      const hasProductAsset = project.productUrl && project.productUrl.trim() !== '';
      
      // Declare variables for product-aware generation
      let imageGenerationSuccessful = false;
      let generatedImageUrl: string | null = null;
      
      if (hasProductAsset) {
        console.log(`[Product-Aware Generation] Project ${project.id} has product asset: ${project.productUrl}`);
        console.log(`[Product-Aware Generation] Script text: "${script.scriptText || script.narratorText}"`);
        
        // Check if we already have a cached generated image for this project
        if (project.generatedProductImageUrl && project.generatedProductImageUrl.trim() !== '') {
          console.log(`[Product-Aware Generation] Using cached generated image: ${project.generatedProductImageUrl}`);
          generatedImageUrl = project.generatedProductImageUrl;
          imageGenerationSuccessful = true;
        } else {
          console.log(`[Product-Aware Generation] No cached image found, attempting two-step generation flow (Phase 2)`);
          
          // Phase 2: Try Image Generation with product reference
          try {
            console.log(`[Product-Aware Generation] Step 1: Attempting image generation with product reference`);
            
            // Create image generation request with product reference
            // Use original script text for image generation
            const scriptText = script.scriptText || script.narratorText || '';
            const imageGeneration = await lumaLabsService.createImageGeneration({
              prompt: `Professional product photography: ${scriptText}. Clean composition, focused lighting, commercial quality.`,
              image_ref: [
                {
                  url: project.productUrl!, // We've already verified it exists with hasProductAsset check
                  weight: 0.8
                }
              ],
              aspect_ratio: '16:9',
              model: 'photon-1'
            });
            
            console.log(`[Product-Aware Generation] Step 1: Image generation started with ID: ${imageGeneration.id}`);
            
            // Phase 3: Wait for image generation to complete
            console.log(`[Product-Aware Generation] Step 2: Waiting for image generation to complete...`);
            generatedImageUrl = await lumaLabsService.waitForImageGeneration(imageGeneration.id, 120000); // Increased to 2 minutes timeout
            
            if (generatedImageUrl) {
              console.log(`[Product-Aware Generation] Step 2: Image generation completed successfully: ${generatedImageUrl}`);
              imageGenerationSuccessful = true;
              
              // Cache the generated image URL in the project for future clips
              console.log(`[Product-Aware Generation] Caching generated image URL for future clips`);
              await db
                .update(videoProjects)
                .set({ generatedProductImageUrl: generatedImageUrl })
                .where(eq(videoProjects.id, project.id));
              
              console.log(`[Product-Aware Generation] Successfully cached generated image for project ${project.id}`);
            } else {
              console.warn(`[Product-Aware Generation] Step 2: Image generation failed or timed out, falling back to enhanced prompt`);
              imageGenerationSuccessful = false;
            }
            
          } catch (error) {
            console.error(`[Product-Aware Generation] Image generation process failed, falling back to enhanced prompt:`, error);
            imageGenerationSuccessful = false;
          }
        }
        
        // Phase 4: Determine video generation strategy based on image generation result
        var prompt: string;
        var videoGenerationMethod: string;
        
        if (imageGenerationSuccessful && generatedImageUrl) {
          console.log(`[Product-Aware Generation] Step 3: Will use generated image for Image-to-Video conversion`);
          
          // Use camera movement description for Image-to-Video, since the static scene is already in the generated image
          prompt = extractCameraMovement(script.scriptText || script.narratorText || '');
          videoGenerationMethod = 'image-to-video';
          
          console.log(`[Product-Aware Generation] Using Image-to-Video prompt: "${prompt}"`);
          console.log(`[Product-Aware Generation] Generated image will be used as keyframe: ${generatedImageUrl}`);
          
        } else {
          console.log(`[Product-Aware Generation] Step 3: Using enhanced text-to-video fallback`);
          
          // Fallback to enhanced text prompt when image generation failed
          // Use full script text with product emphasis for text-to-video
          prompt = `Create a video featuring the product in a realistic scene: ${script.scriptText || script.narratorText}. Focus on product placement and natural integration.`;
          videoGenerationMethod = 'enhanced-text-to-video';
          
          console.log(`[Product-Aware Generation] Using enhanced prompt fallback: "${prompt}"`);
        }
      } else {
        console.log(`[Standard Generation] Project ${project.id} has no product asset, using standard text-to-video generation`);
        
        // Standard text-to-video generation - use full script text for comprehensive scene creation
        var prompt = script.scriptText || script.narratorText || '';
        console.log(`[Standard Generation] Using standard prompt: "${prompt}"`);
      }
      
      // Call Luma Labs API with the determined prompt and method
      let generation;
      
      if (hasProductAsset && imageGenerationSuccessful && generatedImageUrl) {
        console.log(`[Product-Aware Generation] Step 4: Creating Image-to-Video generation with keyframe`);
        
        try {
          // Use generated image as keyframe for video generation
          generation = await lumaLabsService.createGeneration({
            prompt,
            keyframes: {
              frame0: {
                type: 'image',
                url: generatedImageUrl
              }
            },
            aspect_ratio: '16:9',
            loop: true,
            callback_url: callbackUrl,
            model: 'ray-2',
            duration: '5s',
            resolution: '720p',
          });
          
          console.log(`[Product-Aware Generation] Step 4: Image-to-Video generation started successfully with ID: ${generation.id}`);
          
        } catch (error) {
          console.error(`[Product-Aware Generation] Image-to-Video generation failed, falling back to enhanced text-to-video:`, error);
          
          // Final fallback to enhanced text prompt with product emphasis
          generation = await lumaLabsService.createGeneration({
            prompt: `Create a video featuring the product in a realistic scene: ${script.scriptText || script.narratorText}. Focus on product placement and natural integration.`,
            aspect_ratio: '16:9',
            loop: true,
            callback_url: callbackUrl,
            model: 'ray-2',
            duration: '5s',
            resolution: '720p',
          });
          
          console.log(`[Product-Aware Generation] Step 4: Final fallback generation started with ID: ${generation.id}`);
        }
      } else {
        console.log(`[Generation] Creating standard video generation`);
        
        // Standard or enhanced text-to-video generation
        generation = await lumaLabsService.createGeneration({
          prompt,
          aspect_ratio: '16:9',
          loop: true,
          callback_url: callbackUrl,
          model: 'ray-2',
          duration: '5s',
          resolution: '720p',
        });
      }

      console.log(`Started generation ${generation.id} for segment ${newSegment.id} (Product-aware: ${hasProductAsset})`);

      // Update segment with generation ID
      await db
        .update(videoSegments)
        .set({
          generationId: generation.id,
        })
        .where(eq(videoSegments.id, newSegment.id));

      return NextResponse.json({
        message: hasProductAsset ? 'Product-aware video generation started' : 'Video generation started',
        segment: {
          ...newSegment,
          generationId: generation.id
        }
      });
    } catch (error) {
      console.error('Error generating video with Luma Labs:', error);
      
      // Update segment status to failed
      await db
        .update(videoSegments)
        .set({
          status: 'failed',
        })
        .where(eq(videoSegments.id, newSegment.id));
      
      return NextResponse.json(
        { message: 'Error generating video', error: error instanceof Error ? error.message : String(error) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error generating video segment:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
