import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoSegments, videoScripts, videoProjects } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { eq, and } from 'drizzle-orm';

// create new video segment
export async function POST(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { scriptId, generationId, videoUrl } = body;

    if (!scriptId) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // check if script exists and belongs to user
    const [script] = await db
      .select()
      .from(videoScripts)
      .where(eq(videoScripts.id, scriptId));

    if (!script) {
      return NextResponse.json(
        { message: 'Script not found' },
        { status: 404 }
      );
    }

    // check if project belongs to user
    const [project] = await db
      .select()
      .from(videoProjects)
      .where(
        and(
          eq(videoProjects.id, script.projectId),
          eq(videoProjects.userId, user.id)
        )
      );

    if (!project) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    // check if video segment already exists
    const [existingSegment] = await db
      .select()
      .from(videoSegments)
      .where(
        and(
          eq(videoSegments.scriptId, scriptId),
          eq(videoSegments.isCurrentVersion, true)
        )
      );

    if (existingSegment) {
      // if exists, create new version and update existing version
      await db
        .update(videoSegments)
        .set({ isCurrentVersion: false })
        .where(eq(videoSegments.id, existingSegment.id));
    }

    // create new video segment
    const [newSegment] = await db
      .insert(videoSegments)
      .values({
        scriptId,
        version: existingSegment ? existingSegment.version + 1 : 1,
        isCurrentVersion: true,
        generationId: generationId || null,
        videoUrl: videoUrl || null,
        status: 'pending',
      })
      .returning();

    return NextResponse.json(newSegment);
  } catch (error) {
    console.error('Error creating video segment:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// get video segments
export async function GET(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const scriptId = searchParams.get('scriptId');
    const projectId = searchParams.get('projectId');
    const includeAllVersions = searchParams.get('includeAllVersions') === 'true';
    
    if (!scriptId && !projectId) {
      return NextResponse.json(
        { message: 'Script ID or Project ID is required' },
        { status: 400 }
      );
    }

    // if scriptId is provided, get video segments for that script
    if (scriptId) {
      // check if script exists and belongs to user
      const [script] = await db
        .select()
        .from(videoScripts)
        .where(eq(videoScripts.id, scriptId));

      if (!script) {
        return NextResponse.json(
          { message: 'Script not found' },
          { status: 404 }
        );
      }

      // check if project belongs to user
      const [project] = await db
        .select()
        .from(videoProjects)
        .where(
          and(
            eq(videoProjects.id, script.projectId),
            eq(videoProjects.userId, user.id)
          )
        );

      if (!project) {
        return NextResponse.json(
          { message: 'Access denied' },
          { status: 403 }
        );
      }

      // get video segments
      let segments;
      
      if (includeAllVersions) {
        // get all versions
        segments = await db
          .select()
          .from(videoSegments)
          .where(eq(videoSegments.scriptId, scriptId))
          .orderBy(videoSegments.version);
      } else {
        // only get current version
        segments = await db
          .select()
          .from(videoSegments)
          .where(
            and(
              eq(videoSegments.scriptId, scriptId),
              eq(videoSegments.isCurrentVersion, true)
            )
          );
      }
      
      return NextResponse.json(segments);
    }
    
    // if projectId is provided, get all video segments for that project
    if (projectId) {
      // check if project belongs to user
      const [project] = await db
        .select()
        .from(videoProjects)
        .where(
          and(
            eq(videoProjects.id, projectId),
            eq(videoProjects.userId, user.id)
          )
        );

      if (!project) {
        return NextResponse.json(
          { message: 'Project not found or access denied' },
          { status: 404 }
        );
      }

      // get all scripts for project
      const scripts = await db
        .select()
        .from(videoScripts)
        .where(eq(videoScripts.projectId, projectId));
      
      if (scripts.length === 0) {
        return NextResponse.json([]);
      }
      
      // get all scripts for project
      const scriptIds = scripts.map(script => script.id);
      
      let segments;
      
      if (includeAllVersions) {
        // get all versions
        segments = await db
          .select({
            segment: videoSegments,
            script: {
              id: videoScripts.id,
              segmentNumber: videoScripts.segmentNumber
            }
          })
          .from(videoSegments)
          .innerJoin(videoScripts, eq(videoSegments.scriptId, videoScripts.id))
          .where(eq(videoScripts.projectId, projectId))
          .orderBy(videoScripts.segmentNumber, videoSegments.version);
      } else {
        // only get current version
        segments = await db
          .select({
            segment: videoSegments,
            script: {
              id: videoScripts.id,
              segmentNumber: videoScripts.segmentNumber
            }
          })
          .from(videoSegments)
          .innerJoin(videoScripts, eq(videoSegments.scriptId, videoScripts.id))
          .where(
            and(
              eq(videoScripts.projectId, projectId),
              eq(videoSegments.isCurrentVersion, true)
            )
          )
          .orderBy(videoScripts.segmentNumber);
      }
      
      return NextResponse.json(segments);
    }
  } catch (error) {
    console.error('Error fetching video segments:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
