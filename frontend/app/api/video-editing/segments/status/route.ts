import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoSegments } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { getUser } from '@/lib/db/queries';
import { lumaLabsService } from '@/lib/services/lumaLabsService';

export async function GET(request: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const generationId = searchParams.get('generationId');
    const segmentId = searchParams.get('segmentId');

    if (!generationId && !segmentId) {
      return NextResponse.json(
        { message: 'Missing generationId or segmentId parameter' },
        { status: 400 }
      );
    }

    // If segmentId is provided, get segment information from database
    if (segmentId) {
      const [segment] = await db
        .select()
        .from(videoSegments)
        .where(eq(videoSegments.id, segmentId));

      if (!segment) {
        return NextResponse.json(
          { message: 'Segment not found' },
          { status: 404 }
        );
      }

      // If segment is completed or failed, return status immediately
      if (segment.status === 'completed' || segment.status === 'failed') {
        return NextResponse.json({
          status: segment.status,
          videoUrl: segment.videoUrl,
          generationId: segment.generationId
        });
      }

      // If there is no generationId, cannot check status
      if (!segment.generationId) {
        return NextResponse.json(
          { message: 'Segment has no generation ID' },
          { status: 400 }
        );
      }

      // Check status using segment's generationId
      return await checkGenerationStatus(segment.generationId);
    }

    // Check status using provided generationId
    return await checkGenerationStatus(generationId as string);
  } catch (error) {
    console.error('Error checking generation status:', error);
    
    // Extract more detailed error information
    const errorDetails = {
      message: error instanceof Error ? error.message : String(error),
      statusCode: (error as any)?.statusCode,
      originalError: (error as any)?.originalError,
    };
    
    return NextResponse.json(
      { 
        message: 'Internal server error', 
        error: errorDetails 
      },
      { status: 500 }
    );
  }
}

// Helper function: Check generation status
async function checkGenerationStatus(generationId: string) {
  try {
    const generation = await lumaLabsService.getGeneration(generationId);

    return NextResponse.json({
      status: generation.status,
      videoUrl: generation.url,
      generationId
    });
  } catch (error) {
    console.error('Error fetching generation status from Luma Labs:', error);
    
    // Extract more detailed error information
    const errorDetails = {
      message: error instanceof Error ? error.message : String(error),
      statusCode: (error as any)?.statusCode,
      originalError: (error as any)?.originalError,
      generationId: (error as any)?.generationId || generationId,
    };
    
    return NextResponse.json(
      { 
        message: 'Error fetching generation status', 
        error: errorDetails 
      },
      { status: 500 }
    );
  }
}
