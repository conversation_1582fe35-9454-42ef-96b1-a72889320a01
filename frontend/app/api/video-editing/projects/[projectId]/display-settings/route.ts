import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { projectDisplaySettings, videoProjects } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { getUser } from '@/lib/db/queries';
import { S3Service } from '@/lib/services/s3Service';

// Default logo URL using environment variables
const DEFAULT_LOGO_URL = `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.S3_BUCKET_REGION}.amazonaws.com/logo/default/Fylow.png`;

// Default settings for intro screen
const defaultIntroSettings = {
  backgroundColor: '#ffffff',
  logoUrl: DEFAULT_LOGO_URL,
  logoWidth: 30,
  logoHeight: 70,
  logoX: 50,
  logoY: 50,
  isEnabled: true,
};

// Default settings for end screen
const defaultEndSettings = {
  backgroundColor: '#ffffff',
  logoUrl: DEFAULT_LOGO_URL,
  logoWidth: 30,
  logoHeight: 80,
  logoX: 50,
  logoY: 25,
  centerText: 'Thank You For Watching',
  centerTextColor: '#000000',
  centerTextSize: 32,
  centerTextX: 50,
  centerTextY: 60,
  bottomText: 'Visit our website for more information',
  bottomTextColor: '#000000',
  bottomTextSize: 18,
  bottomTextX: 50,
  bottomTextY: 85,
  isEnabled: false,
};

/**
 * GET handler - Retrieves display settings for a project
 */
export async function GET(
  request: NextRequest,
  segmentData: { params: Promise<{ projectId: string }> }
) {
  try {
    // Get authenticated user
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Await params before using its properties
    const { projectId } = await segmentData.params;
    
    // Verify user has access to this project
    const project = await db.query.videoProjects.findFirst({
      where: and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id)
      ),
    });

    if (!project) {
      return NextResponse.json({ error: 'Project not found or access denied' }, { status: 404 });
    }

    // Query the database for display settings
    const settings = await db.query.projectDisplaySettings.findFirst({
      where: eq(projectDisplaySettings.projectId, projectId),
    });

    if (!settings) {
      // Return default settings if none exist for this project
      return NextResponse.json({ 
        introSettings: defaultIntroSettings,
        endSettings: defaultEndSettings
      }, { status: 200 });
    }

    // Return stored settings or fallback to defaults if partial
    return NextResponse.json({
      introSettings: settings.introSettings || defaultIntroSettings,
      endSettings: settings.endSettings || defaultEndSettings
    }, { status: 200 });
  } catch (error) {
    console.error('Error fetching display settings:', error);
    return NextResponse.json({ error: 'Failed to fetch display settings' }, { status: 500 });
  }
}

/**
 * POST handler - Saves display settings for a project
 */
export async function POST(
  request: NextRequest,
  segmentData: { params: Promise<{ projectId: string }> }
) {
  try {
    // Get authenticated user
    const user = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Await params before using its properties
    const { projectId } = await segmentData.params;

    // Verify user has access to this project
    const project = await db.query.videoProjects.findFirst({
      where: and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id)
      ),
    });

    if (!project) {
      return NextResponse.json({ error: 'Project not found or access denied' }, { status: 404 });
    }

    // Get request body
    const { introSettings, endSettings } = await request.json();

    // Check if settings already exist for this project
    const existingSettings = await db.query.projectDisplaySettings.findFirst({
      where: eq(projectDisplaySettings.projectId, projectId),
    });

    let result;
    if (existingSettings) {
      // Update existing settings
      result = await db
        .update(projectDisplaySettings)
        .set({
          introSettings,
          endSettings,
          updatedAt: new Date(),
        })
        .where(eq(projectDisplaySettings.projectId, projectId))
        .returning();
    } else {
      // Create new settings
      result = await db
        .insert(projectDisplaySettings)
        .values({
          projectId: projectId,
          introSettings,
          endSettings,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();
    }

    return NextResponse.json({
      success: true,
      data: result[0]
    }, { status: 200 });
  } catch (error) {
    console.error('Error saving display settings:', error);
    return NextResponse.json({ error: 'Failed to save display settings' }, { status: 500 });
  }
} 