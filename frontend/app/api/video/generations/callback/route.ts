import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const data = await req.json();
    
    // Process callback data here
    console.log('Received generation status update:', data);
    
    // You can store the status update here
    // For example, save it to a database
    // 这里后续就是保存到数据库中的video_segments里面
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Callback processing error:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing the callback.' },
      { status: 500 }
    );
  }
}