import { NextResponse } from 'next/server';

// define request interface
interface GenerationRequest {
  generation_type?: string;
  prompt: string;
  aspect_ratio?: string;
  loop?: boolean;
  keyframes?: object;
  callback_url?: string;
  model?: string;
  resolution?: string;
  duration?: string;
}

// define response interface
interface GenerationResponse {
  id: string;
  generation_type: string;
  state: 'queued' | 'dreaming' | 'completed' | 'failed';
  failure_reason?: string;
  created_at: string;
  assets: {
    video?: string;
    image?: string;
    progress_video?: string;
  };
  model: string;
  request: GenerationRequest;
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                   (req.headers.get('origin') || '');
    
    const generationRequest: GenerationRequest = {
      generation_type: body.generation_type || 'video',
      prompt: body.prompt,
      aspect_ratio: body.aspect_ratio || '16:9',
      loop: body.loop !== undefined ? body.loop : false,
      keyframes: body.keyframes || {},
      callback_url: `${baseUrl}/api/video/generations/callback`,
      model: body.model || 'ray-2',
      resolution: body.resolution || '720p',
      duration: body.duration
    };

    // Check required parameters
    if (!generationRequest.prompt) {
      return NextResponse.json(
        { error: 'Missing required parameter: prompt' },
        { status: 400 }
      );
    }

    // Call Luma Labs API
    const response = await fetch('https://api.lumalabs.ai/dream-machine/v1/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.LUMA_API_KEY}`
      },
      body: JSON.stringify(generationRequest)
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: 'Generation request failed', details: errorData },
        { status: response.status }
      );
    }

    const data: GenerationResponse = await response.json();
    return NextResponse.json(data, { status: 201 });
    
  } catch (error) {
    console.error('Video generation API error:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing the request' },
      { status: 500 }
    );
  }
}