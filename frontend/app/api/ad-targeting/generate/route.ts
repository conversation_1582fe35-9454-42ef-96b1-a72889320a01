import { NextResponse } from 'next/server';
import { createAdCampaign, getUserDailyAdCampaignCount, getUser } from '@/lib/db/queries';
import { TargetingResults } from '@/lib/models/TargetingResult';
import { OpenAI } from 'openai';
import { 
  CREDIT_COSTS, 
  hasEnoughCredits, 
  deductCredits 
} from '@/lib/auth/permissions';

// Define cost for ad targeting generation
const AD_TARGETING_COST = 20;

// Define the base targeting results structure
const baseTargetingResults = {
  title: "Audience Analysis and Targeting Results",
  sections: [
    {
      id: "ch-001",
      title: "Recommended Distribution Channels",
      type: "tags",
      data: [] // Will be filled with AI-generated data
    },
    {
      id: "demo-001",
      title: "Demographics",
      type: "list",
      data: [] // Will be filled with AI-generated data
    },
    {
      id: "beh-001",
      title: "Behavior",
      type: "structured",
      data: [
        {
          id: "usage-001",
          subTitle: "Usage",
          descriptions: [], // Will be filled with AI-generated data
          reviewGroups: []  // Will be filled with AI-generated data
        },
        {
          id: "satisfaction-001",
          subTitle: "Satisfaction Level",
          descriptions: [], // Will be filled with AI-generated data
          reviewGroups: []  // Will be filled with AI-generated data
        },
        {
          id: "buying-behavior-001",
          subTitle: "Buying Behavior",
          descriptions: [], // Will be filled with AI-generated data
          reviewGroups: []  // Will be filled with AI-generated data
        }
      ]
    },
    {
      id: "int-001",
      title: "Interests",
      type: "list",
      data: [] // Will be filled with AI-generated data
    },
    {
      id: "psychographic-001",
      title: "Psychographic Segmentation",
      type: "structured",
      data: [
        {
          id: "personality-001",
          subTitle: "Personality Traits",
          descriptions: [], // Will be filled with AI-generated data
          reviewGroups: []  // Will be filled with AI-generated data
        },
        {
          id: "lifestyle-001",
          subTitle: "Lifestyle Attributes",
          descriptions: [], // Will be filled with AI-generated data
          reviewGroups: []  // Will be filled with AI-generated data
        },
        {
          id: "values-001",
          subTitle: "Core Values",
          descriptions: [], // Will be filled with AI-generated data
          reviewGroups: []  // Will be filled with AI-generated data
        }
      ]
    },
    {
      id: "keywords-001",
      title: "Keywords and Phrases",
      type: "list",
      data: [] // Will be filled with AI-generated data
    }
  ]
};

// Enhanced prompts for more accurate targeting
const ENHANCED_SYSTEM_PROMPT = `You are an expert marketing strategist and audience targeting specialist. Your task is to analyze the provided product description and generate highly specific, data-driven audience targeting information.

Important guidelines:
1. Be extremely specific to the product - avoid generic marketing language
2. Focus on realistic, evidence-based insights rather than aspirational claims
3. Provide concrete examples and specific market segments
4. Consider the product's unique value proposition and competitive landscape
5. Base your analysis on typical user behaviors and needs for this specific product category
6. Avoid clichés and overgeneralized statements
`;

// Main targeting analysis prompt
const TARGETING_ANALYSIS_PROMPT = `
Analyze the following product description and create a comprehensive audience targeting profile. The profile should be highly specific to this particular product and based on realistic market insights.

For each category, provide concrete, specific information that would be immediately actionable for marketing campaigns:

1. Summary: Provide a concise overview of the target audience (30-50 words).

2. Project Title: Create a catchy and descriptive title for the video campaign (3-8 words). The title should capture both the product essence and target audience appeal.

3. Product Positioning: Based on the audience characteristics, recommend how this product should be positioned and what key features/benefits should be emphasized. Focus on what messaging and value propositions would resonate most with this specific audience (2-3 sentences).

4. Distribution Channels: Identify 5-7 specific platforms or channels where this product's target audience is most active and receptive.

5. Demographics: List 5-8 specific demographic segments most likely to purchase this product, including age ranges, income levels, education, occupation types, etc.

6. Behavior Patterns:
   - Usage: How, when, where and why would customers use this specific product?
   - Satisfaction: What specific pain points does this product address for customers?
   - Buying Behavior: What triggers purchase decisions for this product category? Include specific purchase cycles, price sensitivity factors, and decision-making influences.

7. Interests: Identify 6-10 specific interests, hobbies, or activities strongly correlated with this product's target audience.

8. Psychographic Segmentation:
   - Personality Traits: What specific personality characteristics define the ideal customer for this product?
   - Lifestyle Attributes: Describe daily life patterns and preferences of target customers.
   - Core Values: Identify the fundamental beliefs and priorities that would make someone choose this product.

9. Keywords and Phrases: List 8-12 specific search terms, hashtags, or phrases the target audience would use when looking for this product or discussing related needs.

For each section, provide evidence-based insights rather than aspirational marketing language. Focus on being concrete, specific, and realistic.
`;

// JSON schema for the complete targeting analysis
const targetingAnalysisSchema = {
  type: "object",
  properties: {
    summary: {
      type: "string",
      description: "A concise summary of the targeting analysis, around 30-50 words."
    },
    projectTitle: {
      type: "string",
      description: "A catchy and descriptive project title for the video campaign, 2-3 words, focusing on the product."
    },
    productPositioning: {
      type: "string",
      description: "Product positioning and key selling points recommendations based on audience characteristics, 2-3 sentences focusing on what features/benefits to emphasize for this specific audience."
    },
    distributionChannels: {
      type: "array",
      items: { 
        type: "string",
        enum: ["TikTok", "Instagram", "Facebook", "YouTube", "LinkedIn", "Twitter"]
      },
      description: "4-5 specific platforms or channels where the target audience is most active, must choose from: TikTok, Instagram, Facebook, YouTube, LinkedIn, Twitter"
    },
    demographics: {
      type: "array",
      items: { type: "string" },
      description: "5-8 specific demographic segments most likely to purchase this product"
    },
    behavior: {
      type: "object",
      properties: {
        usage: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              description: { type: "string" }
            },
            required: ["subTitle", "description"]
          }
        },
        satisfaction: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              description: { type: "string" }
            },
            required: ["subTitle", "description"]
          }
        },
        buyingBehavior: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              description: { type: "string" }
            },
            required: ["subTitle", "description"]
          }
        }
      },
      required: ["usage", "satisfaction", "buyingBehavior"]
    },
    interests: {
      type: "array",
      items: { type: "string" },
      description: "6-10 specific interests strongly correlated with the target audience"
    },
    psychographics: {
      type: "object",
      properties: {
        personalityTraits: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              description: { type: "string" }
            },
            required: ["subTitle", "description"]
          }
        },
        lifestyleAttributes: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              description: { type: "string" }
            },
            required: ["subTitle", "description"]
          }
        },
        coreValues: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              description: { type: "string" }
            },
            required: ["subTitle", "description"]
          }
        }
      },
      required: ["personalityTraits", "lifestyleAttributes", "coreValues"]
    },
    keywords: {
      type: "array",
      items: { type: "string" },
      description: "8-12 specific search terms or phrases the target audience would use"
    },
    reviewInsights: {
      type: "object",
      properties: {
        usageReviews: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              reviews: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    description: { type: "string" },
                    reviewer: { type: "string" },
                    link: { type: "string" }
                  },
                  required: ["description"]
                }
              }
            },
            required: ["subTitle", "reviews"]
          }
        },
        satisfactionReviews: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              reviews: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    description: { type: "string" },
                    reviewer: { type: "string" },
                    link: { type: "string" }
                  },
                  required: ["description"]
                }
              }
            },
            required: ["subTitle", "reviews"]
          }
        },
        psychographicReviews: {
          type: "array",
          items: {
            type: "object",
            properties: {
              subTitle: { type: "string" },
              reviews: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    description: { type: "string" },
                    reviewer: { type: "string" },
                    link: { type: "string" }
                  },
                  required: ["description"]
                }
              }
            },
            required: ["subTitle", "reviews"]
          }
        }
      },
      required: ["usageReviews", "satisfactionReviews", "psychographicReviews"]
    }
  },
  required: [
    "summary", "projectTitle", "productPositioning", "distributionChannels", "demographics", "behavior", 
    "interests", "psychographics", "keywords", "reviewInsights"
  ]
};

// Helper function to generate a comprehensive targeting analysis
async function generateTargetingAnalysis(openai: OpenAI, description: string) {
  // Add product category classification step
  const categoryResponse = await openai.chat.completions.create({
    model: "gpt-4.1",
    messages: [
      {
        role: "system",
        content: "You are a product categorization specialist. Identify the specific product category, industry, and target market of the described product in 1-2 sentences."
      },
      {
        role: "user",
        content: `Product Description: ${description}\n\nIdentify the specific product category, industry vertical, and likely target market.`
      }
    ],
    temperature: 0.3 // Lower temperature for more precise categorization
  });
  
  const productContext = categoryResponse.choices[0].message.content || "";
  
  // Generate the comprehensive targeting analysis with the product context
  const response = await openai.chat.completions.create({
    model: "gpt-4.1",
    messages: [
      {
        role: "system",
        content: `${ENHANCED_SYSTEM_PROMPT}\n\nProduct Context: ${productContext}`
      },
      {
        role: "user",
        content: `${TARGETING_ANALYSIS_PROMPT}\n\nProduct Description: ${description}\n\nGenerate highly specific targeting information based on this product description. Avoid generic statements, and focus on concrete, actionable insights.`
      }
    ],
    tools: [
      {
        type: "function",
        function: {
          name: "generateTargetingAnalysis",
          description: "Generate a comprehensive targeting analysis based on product description",
          parameters: targetingAnalysisSchema
        }
      }
    ],
    tool_choice: {
      type: "function",
      function: { name: "generateTargetingAnalysis" }
    },
    temperature: 0.5 // Balanced between creativity and precision
  });
  
  const toolCall = response.choices[0].message.tool_calls?.[0];
  
  if (!toolCall || toolCall.function.name !== "generateTargetingAnalysis") {
    throw new Error('Failed to generate targeting analysis');
  }
  
  return JSON.parse(toolCall.function.arguments);
}

// Function to map the comprehensive analysis to our targeting results structure
function mapAnalysisToTargetingResults(analysis: any) {
  const targetingResults = JSON.parse(JSON.stringify(baseTargetingResults));
  
  // Map distribution channels
  targetingResults.sections[0].data = analysis.distributionChannels;
  
  // Map demographics
  targetingResults.sections[1].data = analysis.demographics;
  
  // Map behavior data
  targetingResults.sections[2].data[0].descriptions = analysis.behavior.usage;
  targetingResults.sections[2].data[0].reviewGroups = analysis.reviewInsights.usageReviews;
  
  targetingResults.sections[2].data[1].descriptions = analysis.behavior.satisfaction;
  targetingResults.sections[2].data[1].reviewGroups = analysis.reviewInsights.satisfactionReviews;
  
  targetingResults.sections[2].data[2].descriptions = analysis.behavior.buyingBehavior;
  
  // Map interests
  targetingResults.sections[3].data = analysis.interests;
  
  // Map psychographic data
  targetingResults.sections[4].data[0].descriptions = analysis.psychographics.personalityTraits;
  targetingResults.sections[4].data[1].descriptions = analysis.psychographics.lifestyleAttributes;
  targetingResults.sections[4].data[2].descriptions = analysis.psychographics.coreValues;
  targetingResults.sections[4].data[0].reviewGroups = analysis.reviewInsights.psychographicReviews;
  
  // Map keywords
  targetingResults.sections[5].data = analysis.keywords;
  
  return targetingResults;
}

export async function POST(request: Request) {
  try {
    // Get user information, but don't require login
    const user = await getUser();
    const isAnonymous = !user;
    
    // Parse request data
    const { description } = await request.json();
    
    if (!description || description.trim() === '') {
      return NextResponse.json(
        { error: 'Product description cannot be empty' },
        { status: 400 }
      );
    }
    
    // Create OpenAI client
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    try {
      // Generate a comprehensive analysis in a single call for better context
      const analysis = await generateTargetingAnalysis(openai, description);
      
      // Map the analysis to our targeting results structure
      const targetingResults = mapAnalysisToTargetingResults(analysis);
      
      // Prepare the final result
      const result = {
        summary: analysis.summary,
        projectTitle: analysis.projectTitle,
        description: analysis.productPositioning,
        targetingResults: targetingResults
      };
    

      //save to database
      const campaign = await createAdCampaign(description, result);
      
      // Return result for both anonymous and logged-in users
      return NextResponse.json(result);
      
    } catch (error) {
      console.error('Error generating content:', error);
      return NextResponse.json(
        { error: 'Failed to generate targeting results' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in ad targeting API:', error);
    return NextResponse.json(
      { error: (error as Error).message || 'Internal server error' },
      { status: 500 }
    );
  }
}