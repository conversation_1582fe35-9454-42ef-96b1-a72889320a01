import { NextResponse } from 'next/server';
import { getUserAdCampaigns } from '@/lib/db/queries';
import { getUser } from '@/lib/db/queries';
import { adCampaigns, adHistory } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { db } from '@/lib/db/drizzle';
export async function GET() {
  try {
    // Fetch user's ad campaigns from database
    const campaigns = await getUserAdCampaigns();
    
    // Return ad campaign data
    return NextResponse.json(campaigns);
  } catch (error) {
    console.error('Error fetching ad campaigns:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ad campaign data' },
      { status: 500 }
    );
  }
}


// Delete an ad campaign
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Resolve the params promise to get the ID
    const resolvedParams = await params;
    const { id: campaignId } = resolvedParams;

    // Check if campaign exists
    const [existingAdCampaign] = await db
      .select()
      .from(adCampaigns)
      .where(
        and(
          eq(adCampaigns.id, campaignId),
        )
      );
     // Check if ad History exists
     const [existingAdHistory] = await db
     .select()
     .from(adHistory)
     .where(
       and(
         eq(adHistory.campaignId, campaignId),
       )
     );

    if (!existingAdCampaign) {
      return NextResponse.json(
        { message: 'Ad campaign not found' },
        { status: 404 }
      );
    }

    // Delete the relevant row in adHistory
    await db
      .delete(adHistory)
      .where(eq(adHistory.campaignId, campaignId));
    // Delete the campaign
    await db
      .delete(adCampaigns)
      .where(eq(adCampaigns.id, campaignId));  
      
    return NextResponse.json(
      { message: 'Campaign deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting video project:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
