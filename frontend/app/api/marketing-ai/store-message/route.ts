
import { NextResponse } from 'next/server';
import { storeNewMessages } from "@/lib/db/queries"; 
//for storing conversation messages
export async function POST(req: Request) {
  try {
    const { role, content, conversationId } = await req.json();
    console.log("input:", {role, content, conversationId});
    if (!role || !content || !conversationId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    await storeNewMessages({ role, content, conversationId });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to store message:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
