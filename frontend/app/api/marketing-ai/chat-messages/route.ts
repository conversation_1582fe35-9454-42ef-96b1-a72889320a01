import { NextResponse } from "next/server";
import { getChatMessages } from "@/lib/db/queries";
//for getting the conversation's history
export async function GET(request:Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get("chatId");
  if (!chatId){
    return NextResponse.json({ error: "Missing chatId parameter" }, { status: 400 })
  }
  if(chatId){
  const messages = await getChatMessages(chatId);
  return NextResponse.json(messages)
  }
  ;
}
