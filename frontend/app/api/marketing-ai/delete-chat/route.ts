import { NextResponse } from "next/server";
import { getUser, deleteChat } from "@/lib/db/queries";
import { aiChats, chatMessages } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { db } from '@/lib/db/drizzle';
//for deleting a  chat
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }


    const resolvedParams = await params;
    const { id: chatId } = resolvedParams;

    // Check if conversation exists
    const [chat] = await db
          .select()
          .from(aiChats)
          .where(
            and(
              eq(aiChats.id, chatId),
            )
          );

    if (!chat) {
            return NextResponse.json(
              { message: 'Conversation not found' },
              { status: 404 }
            );
          }

    await deleteChat( chatId );
      
    return NextResponse.json(
      { message: 'Chat deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting video project:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}