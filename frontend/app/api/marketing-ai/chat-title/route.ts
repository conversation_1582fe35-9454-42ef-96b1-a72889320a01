import { NextResponse } from 'next/server';
import { updateAiChat } from "@/lib/db/queries"; 
//for storing the generated conversation's title 
export async function POST(req: Request) {
    try{
    const {conversationId, generatedTitle} = await req.json();
    console.log("conversation id:", conversationId);
    if (!conversationId){
        return NextResponse.json({error:'ConversationId is missing.'}, {status:400});
    }
    await updateAiChat(conversationId, {title: generatedTitle});
    return NextResponse.json({title: generatedTitle});
    }catch (error){
     console.log('Failed to set chat title:', error);
     return NextResponse.json({error: 'Server error'}, {status: 500});

    }
    
}