import NextAuth, { SessionStrategy } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { db } from "@/lib/db/drizzle";
import { eq } from "drizzle-orm";
import { users, ActivityType, activityLogs } from "@/lib/db/schema";
import bcrypt from "bcryptjs";
import { setSession } from "@/lib/auth/session";
import { JWT } from "next-auth/jwt";
import { Session } from "next-auth";

// First, let's add a helper to determine if we should use secure cookies
// Near the top of the file, before the authOptions definition

// Determine if we're in a secure environment (HTTPS) or not
// For EC2 without HTTPS, we need to set secure: false to allow cookies to be set
const isSecureEnvironment = process.env.NODE_ENV === 'production' && 
                           !process.env.ALLOW_INSECURE_COOKIES;

declare module "next-auth" {
  interface Session {
    user: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  }
}

async function generateRandomPasswordHash() {
  const randomPassword = Math.random().toString(36).slice(-10);
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(randomPassword, salt);
}

async function logUserActivity(userId: string, action: ActivityType,teamId?: string, ipAddress: string = "") {
  try {
    await db.insert(activityLogs).values({
      userId: userId,
      teamId: teamId,
      action: action,
      timestamp: new Date(),
      ipAddress: ipAddress,
    });
    
    console.log("Activity log recorded successfully");
  } catch (error) {
    console.error("Failed to record activity log:", error);
  }
}

const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  debug: true,
  // ban next-auth session cookie
  cookies: {
    sessionToken: {
      name: `__Secure-next-auth.session-token-disabled`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: isSecureEnvironment, // Only set secure flag in production with HTTPS
        maxAge: -1 
      }
    }
  },
  session: {
    strategy: 'jwt' as SessionStrategy,
    maxAge: 0,
    updateAge: 0,
  },
  callbacks: {
    async jwt({ token, user, account, profile }: { 
      token: JWT; 
      user?: any; 
      account?: any; 
      profile?: any 
    }) {
      console.log("JWT callback called", { 
        tokenId: token.id, 
        userId: user?.id, 
        accountProvider: account?.provider
      });
      
      if (account && account.provider === "google" && profile) {
        try {
          console.log("Processing Google OAuth login", { email: profile.email });
          
          const existingUsers = await db
            .select()
            .from(users)
            .where(eq(users.email, profile.email as string))
            .limit(1);
          
          console.log("Query existing user result", { 
            found: existingUsers.length > 0,
            userEmail: profile.email
          });
          
          let dbUser = existingUsers.length > 0 ? existingUsers[0] : null;
          let newUserCreated = false;
          
          if (!dbUser) {
            console.log("Creating new user", { email: profile.email });
            
            const passwordHash = await generateRandomPasswordHash();
            
            const newUsers = await db
              .insert(users)
              .values({
                name: profile.name || "",
                email: profile.email as string,
                googleId: profile.sub as string,
                avatar: profile.image || null,
                passwordHash: passwordHash,
                role: "Owner",
                emailVerified: true,
                credits: 100,
                createdAt: new Date(),
                updatedAt: new Date(),
              })
              .returning();
            
            dbUser = newUsers[0];
            newUserCreated = true;
            console.log("New user created successfully", { id: dbUser.id });
            
            await logUserActivity(dbUser.id, ActivityType.SIGN_UP);
          } 
          else if (!dbUser.googleId) {
            console.log("Updating existing user's Google ID", { 
              userId: dbUser.id, 
              googleId: profile.sub 
            });
            
            await db
              .update(users)
              .set({
                googleId: profile.sub as string,
                avatar: profile.image || dbUser.avatar,
                emailVerified: true,
                updatedAt: new Date(),
              })
              .where(eq(users.id, dbUser.id));
            
            console.log("User's Google ID updated successfully");
          }
          
          if (!newUserCreated) {
            await logUserActivity(dbUser.id, ActivityType.SIGN_IN);
          }
          
          console.log("Updating JWT token", { userId: dbUser.id });
          return {
            ...token,
            id: dbUser.id,
            email: dbUser.email,
            name: dbUser.name,
            picture: dbUser.avatar,
          };
        } catch (error) {
          console.error("Error processing OAuth login:", error);
          return token;
        }
      }
      
      // If not initial login, return unmodified token
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      console.log("Session callback called", { sessionUserId: session.user?.id, tokenId: token.id });
      if (token.id && session.user) {
        session.user.id = String(token.id);
      }
      return session;
    },
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      console.log("Redirect callback called", { url, baseUrl });
      
      // If URL is a relative path or from the same domain, allow redirect
      if (url.startsWith('/') || url.startsWith(baseUrl)) {
        return url;
      }
      
      // Default redirect to homepage
      return `${baseUrl}/`;
    },
    async signIn({ user, account, profile }: { user: any; account: any; profile?: any }) {
      console.log("SignIn callback called", { 
        userId: user?.id, 
        accountProvider: account?.provider,
        userEmail: user?.email
      });
      
      // If Google login, need to set custom session
      if (account?.provider === "google" && user?.email) {
        try {
          // Find user in database
          const dbUsers = await db
            .select()
            .from(users)
            .where(eq(users.email, user.email))
            .limit(1);
          
          if (dbUsers.length > 0) {
            const dbUser = dbUsers[0];
            console.log("Setting custom session for Google login user", { userId: dbUser.id });
            
            // Set custom session - ensure this operation completes successfully
            try {
              await setSession(dbUser);
              console.log("Custom session set successfully");
            } catch (sessionError) {
              console.error("Failed to set custom session:", sessionError);
              // Continue login process even if custom session setting fails
            }
          }
          
          // Return true to ensure login process continues
          return true;
        } catch (error) {
          console.error("Error processing Google login:", error);
          // Add more error logs for debugging
          console.error("Error details:", JSON.stringify(error, null, 2));
          // Still return true to attempt to complete login
          return true;
        }
      }
      
      return true;
    },
  },
  pages: {
    signIn: "/sign-in",
    error: "/sign-in",
    newUser: "/",
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
