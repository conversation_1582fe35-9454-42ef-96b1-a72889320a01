import { NextResponse } from 'next/server';
import { db } from '@/lib/db/drizzle';
import { videoProjects, projectDisplaySettings } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { 
  hasEnoughCredits, 
  deductCredits, 
  calculateVideoCampaignCredits 
} from '@/lib/auth/permissions';
import { eq } from 'drizzle-orm';

// Default logo URL fallback
const DEFAULT_LOGO_URL = `${process.env.NEXT_PUBLIC_APP_URL || ''}/Fylow.png`;

// Default settings for intro screen
const defaultIntroSettings = {
  backgroundColor: '#ffffff',
  logoUrl: DEFAULT_LOGO_URL,
  logoWidth: 30,
  logoHeight: 70,
  logoX: 50,
  logoY: 50,
  isEnabled: true,
};

// Default settings for end screen
const defaultEndSettings = {
  backgroundColor: '#ffffff',
  logoUrl: DEFAULT_LOGO_URL,
  logoWidth: 30,
  logoHeight: 80,
  logoX: 50,
  logoY: 25,
  centerText: 'Thank You For Watching',
  centerTextColor: '#000000',
  centerTextSize: 32,
  centerTextX: 50,
  centerTextY: 60,
  bottomText: 'Visit our website for more information',
  bottomTextColor: '#000000',
  bottomTextSize: 18,
  bottomTextX: 50,
  bottomTextY: 85,
  isEnabled: false,
};

/**
 * API endpoint to create a new video campaign with credit consumption
 */
export async function POST(request: Request) {
  try {
    // Check if user is authenticated
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { 
      title, brandName, adLength, summary, description, 
      productUrl, introLogoUrl, endLogoUrl 
    } = body;

    // Validate required fields
    if (!title || !brandName || !adLength) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Calculate required credits based on video duration
    const requiredCredits = calculateVideoCampaignCredits(adLength);
    
    // Check if user has enough credits
    if (!(await hasEnoughCredits(user.id, requiredCredits))) {
      return NextResponse.json(
        { message: 'Insufficient credits', requiredCredits },
        { status: 403 }
      );
    }
    
    // Create the video project
    const [newProject] = await db.insert(videoProjects).values({
      userId: user.id,
      title,
      brandName,
      adLength,
      summary: summary || null,
      description: description || null,
      productUrl: productUrl || null, // Store product URL if provided
      status: 'pending',
    }).returning();
    
    if (!newProject) {
      return NextResponse.json(
        { message: 'Failed to create project' },
        { status: 500 }
      );
    }
    
    // Create initial display settings with custom logos if provided
    try {
      // Prepare intro and end settings by cloning the defaults
      const introSettings = { ...defaultIntroSettings };
      const endSettings = { ...defaultEndSettings };
      
      // Override with custom logo URLs if provided
      if (introLogoUrl) {
        introSettings.logoUrl = introLogoUrl;
      }
      
      if (endLogoUrl) {
        endSettings.logoUrl = endLogoUrl;
      }
      
      // Insert the display settings record
      await db.insert(projectDisplaySettings).values({
        projectId: newProject.id,
        introSettings,
        endSettings,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    } catch (settingsError) {
      // Log error but don't fail the project creation
      console.error('Error creating initial display settings:', settingsError);
    }
    
    // Deduct credits for successful creation
    const deducted = await deductCredits(user.id, requiredCredits, 'video_campaign');
    if (!deducted) {
      // If credits deduction failed, roll back project creation
      await db.delete(videoProjects).where(eq(videoProjects.id, newProject.id));
      
      return NextResponse.json(
        { message: 'Failed to deduct credits' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      message: 'Video project created successfully',
      project: newProject,
      creditsDeducted: requiredCredits
    });
    
  } catch (error) {
    console.error('Error creating video project:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 