import { NextResponse } from 'next/server';
import { getUser } from '@/lib/db/queries';
import { OpenAI } from 'openai';
import { 
  CREDIT_COSTS, 
  hasEnoughCredits, 
  deductCredits 
} from '@/lib/auth/permissions';

/**
 * API endpoint to generate audience insights using AI
 */
export async function POST(request: Request) {
  try {
    // Check if user is authenticated
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { brandName, industry, targetAudience } = body;

    // Validate required fields
    if (!brandName || !industry) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Check if user has enough credits
    const requiredCredits = CREDIT_COSTS.GENERATE_AUDIENCE_INSIGHT;
    if (!(await hasEnoughCredits(user.id, requiredCredits))) {
      return NextResponse.json(
        { message: 'Insufficient credits', requiredCredits },
        { status: 403 }
      );
    }
    
    // Create OpenAI client
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    // Make the API call to generate insights
    const response = await openai.chat.completions.create({
      model: "gpt-4.1",
      messages: [
        {
          role: "system",
          content: "You are an expert marketing consultant. Generate audience insights and recommendations."
        },
        {
          role: "user",
          content: `Generate audience insights for a ${industry} brand named ${brandName}. 
                    Target audience: ${targetAudience || 'General population'}.
                    Provide 5 key insights about this audience and marketing recommendations.`
        }
      ],
    });
    
    // Get the generated insights
    const insights = response.choices[0].message.content;
    
    if (!insights) {
      return NextResponse.json(
        { message: 'Failed to generate insights' },
        { status: 500 }
      );
    }
    
    // Deduct credits for successful generation
    const deducted = await deductCredits(user.id, requiredCredits, 'audience_insight');
    if (!deducted) {
      return NextResponse.json(
        { message: 'Failed to deduct credits' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      insights: insights,
      creditsDeducted: requiredCredits
    });
    
  } catch (error) {
    console.error('Error generating audience insights:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 