import { AbsoluteFill, Video } from 'remotion';

export const TestVideoComposition = ({ videoSrc }: { videoSrc?: string }) => {
  if (!videoSrc) {
    return (
      <AbsoluteFill style={{ background: 'black', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ color: 'white', fontSize: '2rem' }}>
          No video source provided
        </div>
      </AbsoluteFill>
    );
  }

  return (
    <>
      <AbsoluteFill style={{ backgroundColor: 'black' }}>
        <Video
          src={videoSrc}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain'
          }}
        />
      </AbsoluteFill>
    </> 
  );
}; 