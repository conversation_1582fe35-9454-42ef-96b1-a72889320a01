import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function SignUpSuccessPage() {
  return (
    <div className="min-h-[100dvh] flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <img className="h-6 w-auto object-contain" src="/Fylow-copy.png" alt="Logo" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-semibold text-gray-900 font-sans">
          Registration Successful
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <svg className="mx-auto h-12 w-12 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
            
            <h3 className="mt-3 text-lg font-medium text-gray-900">Thank you for registering!</h3>
            
            <div className="mt-4 space-y-4">
              <p className="text-gray-600">
                We've sent a verification email to your inbox. Please check your email and click on the verification link.
              </p>
              <p className="text-gray-600">
                You can continue to use the application in the meantime.
              </p>
            </div>

            <div className="mt-6 flex flex-col space-y-3">
              <Link href="/dashboard" prefetch={false} replace>
                <Button className="w-full">
                  Go to Dashboard
                </Button>
              </Link>
              <Link href="/sign-in" prefetch={false} replace>
                <Button variant="outline" className="w-full">
                  Sign In
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 