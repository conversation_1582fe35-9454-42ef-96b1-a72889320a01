'use server';

import { eq, and, sql } from 'drizzle-orm';
import { db } from '@/lib/db/drizzle';
import { users, verificationTokens } from '@/lib/db/schema';
import { generateVerificationToken, sendVerificationEmail } from '@/lib/email';
import { getUser } from '@/lib/db/queries';

interface VerifyEmailResult {
  success: boolean;
  error?: string;
}

/**
 * Verify an email verification token
 * @param token The verification token to verify
 * @returns Object with success flag and optional error message
 */
export async function verifyEmail(token: string): Promise<VerifyEmailResult> {
  try {
    // Check if token exists and is not expired
    const tokenRecords = await db
      .select()
      .from(verificationTokens)
      .where(
        and(
          eq(verificationTokens.token, token),
          sql`${verificationTokens.expiresAt} > NOW()`
        )
      )
      .limit(1);
    
    if (tokenRecords.length === 0) {
      return {
        success: false,
        error: 'Invalid or expired verification token.',
      };
    }
    
    const verificationToken = tokenRecords[0];
    
    // Update user's emailVerified status
    await db
      .update(users)
      .set({ emailVerified: true, credits: 100})
      .where(eq(users.id, verificationToken.userId));
    
    // Remove used token
    await db
      .delete(verificationTokens)
      .where(eq(verificationTokens.id, verificationToken.id));
    
    return { success: true };
  } catch (error) {
    console.error('Error verifying email:', error);
    return {
      success: false,
      error: 'An error occurred during verification. Please try again.',
    };
  }
}

/**
 * Resend verification email to the current user
 * @returns Object with success flag and optional error message
 */
export async function resendVerificationEmail(): Promise<VerifyEmailResult> {
  try {
    // Get current user
    const user = await getUser();
    
    if (!user) {
      return {
        success: false,
        error: 'No authenticated user found.',
      };
    }
    
    // Check if email is already verified
    if (user.emailVerified) {
      return {
        success: false,
        error: 'Email is already verified.',
      };
    }
    
    // Delete any existing tokens for this user
    await db
      .delete(verificationTokens)
      .where(eq(verificationTokens.userId, user.id));
    
    // Generate new token and send email
    const token = await generateVerificationToken(user.id);
    const emailSent = await sendVerificationEmail(user.email, token);
    
    if (!emailSent) {
      return {
        success: false,
        error: 'Failed to send verification email. Please try again later.',
      };
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error resending verification email:', error);
    return {
      success: false,
      error: 'An error occurred. Please try again later.',
    };
  }
} 