'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import { verifyEmail } from '@/app/verify-email/actions';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

// Define the interface for the verification result
interface VerifyEmailResult {
  success: boolean;
  error?: string;
}

export default function PageWrapper(props: any) {
  return (
    <Suspense>
      <Page {...props} />
    </Suspense>
  );
}

// Rename the original default export component to Page
function Page(props: any) {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Verifying your email...');

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setMessage('Invalid verification link. No token provided.');
      return;
    }

    verifyEmail(token)
      .then((result: VerifyEmailResult) => {
        if (result.success) {
          setStatus('success');
          setMessage('Your email has been verified successfully!');
        } else {
          setStatus('error');
          setMessage(result.error || 'Verification failed. Please try again.');
        }
      })
      .catch((error: unknown) => {
        setStatus('error');
        setMessage('An error occurred during verification. Please try again.');
        console.error('Verification error:', error);
      });
  }, [token]);

  return (
    <div className="min-h-[100dvh] flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <img className="h-6 w-auto object-contain" src="/Fylow-copy.png" alt="Logo" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-semibold text-gray-900 font-sans">
          Email Verification
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {status === 'loading' && (
              <div className="animate-pulse">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <p className="mt-2 text-gray-600">{message}</p>
              </div>
            )}

            {status === 'success' && (
              <div>
                <svg className="mx-auto h-12 w-12 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                <p className="mt-2 text-gray-600">{message}</p>
                <div className="mt-6">
                  <Link href="/sign-in">
                    <Button className="w-full">
                      Go to Sign In
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            {status === 'error' && (
              <div>
                <svg className="mx-auto h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                <p className="mt-2 text-gray-600">{message}</p>
                <div className="mt-6">
                  <Link href="/sign-in">
                    <Button className="w-full" variant="outline">
                      Go to Sign In
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 