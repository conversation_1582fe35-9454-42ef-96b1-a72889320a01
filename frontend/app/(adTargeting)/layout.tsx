'use client';

import Link from 'next/link';
import { use, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CircleIcon, Home, LogOut, CircleDollarSign, PanelLeft } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import Sidebar from './components/Sidebar'
import VideoAdCampaignForm from './components/VideoAdCampaignForm'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useUser } from '@/lib/auth';
import { signOut } from '@/app/(login)/actions';
import { useRouter } from 'next/navigation';
import { Footer } from '@/components/ui/Footer';
import { Divider } from '@/components/ui/Divider';

function Header({ activeNavItem, onNavItemClick, isMobileMenuOpen, toggleMobileMenu }: { 
  activeNavItem?: string;
  onNavItemClick?: (item: string) => void;
  isMobileMenuOpen: boolean; 
  toggleMobileMenu: () => void 
}) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { userPromise } = useUser();
  const user = userPromise ? use(userPromise) : null;
  const router = useRouter();

  async function handleSignOut() {
    await signOut();
    router.refresh();
    router.push('/');
  }
  
  return (
    <header className="border-b-transparent border-gray-200 bg-white shadow-md z-10">
      <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <button 
            onClick={toggleMobileMenu}
            className="md:hidden text-cyan-700 hover:text-cyan-800 cursor-pointer focus:outline-none mr-4"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-6 w-6" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 6h16M4 12h16M4 18h16" 
              />
            </svg>
          </button>
          <Link 
            href="/adTargeting" 
            className="flex items-center"
            onClick={(e) => {
              e.preventDefault();
              window.location.href = '/adTargeting';
            }}
          >
            <span className="ml-2">
              <img className="h-6 w-auto object-contain" src="/Fylow-copy4.png" alt="Logo"/>
            </span>
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          <Link
            href="/pricing"
            className="text-md font-sans text-cyan-700 hover:text-cyan-800"
          >
            Pricing
          </Link>
          {user ? (
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <DropdownMenuTrigger>
                <Avatar className="cursor-pointer size-9">
                  <AvatarImage alt={user.name || ''} />
                  <AvatarFallback>
                    {user.email
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="flex flex-col gap-1">
                <DropdownMenuItem className="cursor-pointer text-cyan-700 hover:text-cyan-800">
                  <Link href="/dashboard" className="flex w-full items-center">
                  <PanelLeft className="mr-2 h-4 w-4" />
                    <span>Dashboard</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem  className="cursor-pointer text-cyan-700 hover:text-cyan-800">
                <Link
            href="/pricing"
            className="flex w-full items-center"
          ><span><CircleDollarSign className="mr-2 h-4 w-4"/></span>
            Pricing
          </Link>
                </DropdownMenuItem>
                <form action={handleSignOut} className="w-full">
                  <button type="submit" className="flex w-full">
                    <DropdownMenuItem className="w-full flex-1 cursor-pointer text-cyan-700 hover:text-cyan-800">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign out</span>
                    </DropdownMenuItem>
                  </button>
                </form>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              asChild
              className="bg-cyan-700 hover:bg-cyan-800 text-white text-sm px-4 py-2 rounded-md"
            >
              <Link href="/sign-up">Sign Up</Link>
            </Button>
          )}
        </div>
      </div>
    </header>
  );
}

export default function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  const [activeNavItem, setActiveNavItem] = useState<string | undefined>(undefined);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { userPromise } = useUser();
  const user = use(userPromise ?? Promise.resolve(null));
  const router = useRouter();
  
  
  // Removed login redirect to allow all users to access the home page
  // regardless of their login status. This supports generating audience
  // without requiring login.

  const handleNavItemClick = (item: string) => {
    setActiveNavItem(item);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header 
        activeNavItem={activeNavItem} 
        onNavItemClick={handleNavItemClick}
        isMobileMenuOpen={isMobileMenuOpen}
        toggleMobileMenu={toggleMobileMenu}
      />
      <div className="flex flex-1 overflow-hidden">
          <>
            <Sidebar 
              activeItem={activeNavItem}
              isMobileOpen={isMobileMenuOpen}
              onCloseMobile={closeMobileMenu}
              onNavItemClick={handleNavItemClick}
            />
            <main className="flex-1 overflow-auto">
              <div className="flex-1 h-full p-6">
                {children}
              </div>
            </main>
          </>
      </div>
      <Divider />
      <Footer />
    </div>
  );
}
