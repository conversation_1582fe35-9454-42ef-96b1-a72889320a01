"use client";

import ProductForm from '../components/ProductForm';
import ResultComponent from '../components/ResultComponent';
import PastAudiences from '../components/PastAudiences';
import { useState, use, useEffect } from 'react';
import CampaignList from '../components/CampaignList';
import { useUser } from '@/lib/auth';
import { User } from '@/lib/db/schema';
import VideoAdCampaignForm from "../components/VideoAdCampaignForm";
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';




interface CampaignProps {
  campaign: {
    id: number;
    title: string;
    brandName: string;
    summary: string;
    status: string;
    adLength: string;
    description: string;
    createdAt: Date;
  } | null;
}

export default function HomePage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [showPastAudiences, setShowPastAudiences] = useState(false);
  const [resultData, setResultData] = useState<any>(null);
  //state for showing video projects
  const [showVideoCampaigns, setShowCampaigns] = useState(false);
  //state for showing the product form
  const [showProductForm,setShowProductForm] = useState(false);
  //state for the marketing AI agent page
  //const [showAiChat, setShowAiChat] = useState(false);
  //this one is because while refreshing every page the video campaigns component would pop up momentarily while loading, and I added this to prevent that
  const [hasMounted, setHasMounted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resultSource, setResultSource] = useState<'form' | 'pastAudience'>('form');
  const { userPromise } = useUser();
  const currentUser = userPromise ? use(userPromise) : null;
  const router = useRouter();
  const [campaign, setCampaign] = useState<CampaignProps["campaign"]>(null);


  // Removed login redirect to allow all users to access the home page
  // This enables users to generate audience without requiring login

  // Listen for URL hash changes to handle sidebar navigation
  useEffect(() => {
    // Check if the hash is #past-audiences
    if (window.location.hash === '#past-audiences') {
      showPastAudiencesComponent();
    }
    else if (window.location.hash === '#videoCampaigns') {
      showVideoCampaignsComponent();
    }
    else{
      setShowProductForm(true);
    }
    
    // Add event listener for hash changes
    const handleHashChange = () => {
      console.log("Hash changed to:", window.location.hash)
      if (window.location.hash === '#past-audiences') {
        showPastAudiencesComponent();
      } else if (window.location.hash === '#videoCampaigns') {
        showVideoCampaignsComponent();
      }else{
        showProductFormComponent();
      }
    };
    handleHashChange();
    window.addEventListener('hashchange', handleHashChange);
    setHasMounted(true);
    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);
  //managing states for shoowing the video campaigns list
  const showVideoCampaignsComponent = () => {
    setShowCampaigns(true);
    setShowResults(false);
    setShowPastAudiences(false);
    setShowProductForm(false);
  };

//managing states for showing the product form component
  const showProductFormComponent = () => {
    setShowCampaigns(false);
    setShowResults(false);
    setShowPastAudiences(false);
    setShowProductForm(true);
  };

  const handleSubmit = async (formData: { description: string }) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/ad-targeting/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description: formData.description }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error generating results');
      }
      
      const data = await response.json();
      setResultData(data);
      setShowResults(true);
      setShowPastAudiences(false);
      setResultSource('form');
    } catch (err) {
      console.error('Error generating results:', err);
      setError((err as Error).message || 'Error generating results, please try again later');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectPastAudience = (data: any) => {
    setResultData(data);
    setShowResults(true);
    setShowPastAudiences(false);
    setResultSource('pastAudience');
  };
 // managing states for showing the past audiences component
  const showPastAudiencesComponent = () => {
    setShowPastAudiences(true);
    setShowResults(false);
    setShowProductForm(false);
    setShowCampaigns(false);
  };

  const handleResultBack = () => {
    if (resultSource === 'pastAudience') {
      setShowResults(false);
      setShowPastAudiences(true);
    } else {
      setShowResults(false);
      setShowPastAudiences(false);
    }
  };
 
  if (!hasMounted) return null;
  return (
    <main className="w-full h-full flex justify-center items-center">
       {!showPastAudiences && !showResults && !showVideoCampaigns  && showProductForm ?(
        <>
          <ProductForm onSubmit={handleSubmit} isLoading={isLoading}/>
          {error && (
            <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md">
              {error}
            </div>
          )}
        </>
      ):showResults ? (
        <div className="w-full max-w-2xl">
          <ResultComponent 
            data={resultData} 
            onBack={handleResultBack}
            resultSource={resultSource}
          />
        </div>
      ) : showPastAudiences ? (
        <PastAudiences onSelectResult={handleSelectPastAudience} />
      ): showVideoCampaigns && currentUser ? (
        <CampaignList campaign={campaign}/>
      ): !currentUser && (showPastAudiences || showVideoCampaigns) ? (
        <div className="text-center p-8">
          <p>Please sign in to view this content</p>
          <Button
            onClick={() => router.push('/sign-in')}
            className="mt-4 bg-cyan-700 hover:bg-cyan-800 text-white"
          >
            Sign In
          </Button>
        </div>
      ) : (
        <ProductForm onSubmit={handleSubmit} isLoading={isLoading}/>
      )}
    </main>
  );
}