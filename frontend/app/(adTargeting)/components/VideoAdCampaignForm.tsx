import React, { use, useState, useEffect, useRef} from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useUser } from '@/lib/auth';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { HelpCircle, Upload, AlertCircle, Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { getUserCredits, checkCredits, createVideoCampaign } from "@/lib/api-client/credit-api";

//import { API_BASE_URL } from "@/config";

interface VideoAdCampaignFormProps {
  isOpen: boolean;
  onClose: () => void;
  summary?: string;
  projectTitle?: string;
  description?: string;
  isSummaryReadOnly?: boolean;
  isInfo?: boolean;
  TargetField?: boolean;

}

interface FormData {
  title: string;
  brandName: string;
  summary: string;
  description: string;
  adLength: string;
  productUrl?: string;    // Product image/video URL
  introLogoUrl?: string;  // Logo for intro screen
  endLogoUrl?: string;    // Logo for end screen
}

// Credit information interface
interface CreditInfo {
  balance: number;
  requiredCredits: number;
  hasEnoughCredits: boolean;
}

const truncateText = (text: string, maxLength: number = 50): string => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return `${text.slice(0, maxLength)}...`;
};

const isFormValid = (formData: FormData): boolean => {
  return (
    formData.title.trim()!== "" &&
    formData.brandName.trim() !== "" &&
    formData.summary.trim() !== "" &&
    formData.description.trim() !== "" &&
    formData.adLength !== ""
  );
};

const VideoAdCampaignForm: React.FC<VideoAdCampaignFormProps> = ({ 
  isOpen, 
  onClose, 
  summary,
  projectTitle,
  description,
  isSummaryReadOnly,
  isInfo=false,
  TargetField=false
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState<FormData>({
    title: projectTitle || "",
    brandName: "",
    summary: summary || "",
    description: description || "",
    adLength: "",
  });
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  {/* Initialize tooltip's and info's state */}
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const [isInfoOpen, setIsInfoOpen] = useState(false);
  
  // Credit system states
  const [creditInfo, setCreditInfo] = useState<CreditInfo | null>(null);
  const [isLoadingCredits, setIsLoadingCredits] = useState<boolean>(false);
  const [creditWarning, setCreditWarning] = useState<string | null>(null);
  
  // Logo upload states
  const [isUploadingProduct, setIsUploadingProduct] = useState(false);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [productPreviewUrl, setProductPreviewUrl] = useState<string | null>(null);
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const productFileInputRef = useRef<HTMLInputElement>(null);
  const logoFileInputRef = useRef<HTMLInputElement>(null);
  const [showUploadSection, setShowUploadSection] = useState(false);

  const { userPromise } = useUser();
  const user = userPromise ? use(userPromise) : null;

  useEffect(() => {
    if (summary) {
      setFormData(prev => ({
        ...prev,
        summary: summary
      }));
    }
  }, [summary]);

  useEffect(() => {
    if (description) {
      setFormData(prev => ({
        ...prev,
        description: description
      }));
    }
  }, [description]);

  useEffect(() => {
    if (projectTitle) {
      setFormData(prev => ({
        ...prev,
        title: projectTitle
      }));
    }
  }, [projectTitle]);

  // Fetch user's credit balance
  useEffect(() => {
    const fetchCreditInfo = async () => {
      setIsLoadingCredits(true);
      try {
        // Get user's credit balance
        const creditsResponse = await getUserCredits();
        
        let requiredCredits = 0;
        let hasEnoughCredits = true;
        
        // If ad length is selected, check required credits
        if (formData.adLength) {
          const adLengthSeconds = parseInt(formData.adLength);
          // Check credits needed for this feature
          const checkResponse = await checkCredits('video_campaign', { durationInSeconds: adLengthSeconds });
          requiredCredits = checkResponse.requiredCredits;
          hasEnoughCredits = checkResponse.hasEnoughCredits;
        }
        
        setCreditInfo({
          balance: creditsResponse.credits,
          requiredCredits,
          hasEnoughCredits
        });
        
      } catch (error) {
        console.error('Error fetching credit information:', error);
      } finally {
        setIsLoadingCredits(false);
      }
    };

    if (isOpen) {
      fetchCreditInfo();
    }
  }, [isOpen, formData.adLength]);

  // When ad length changes, calculate required credits
  useEffect(() => {
    const updateCreditRequirement = async () => {
      if (formData.adLength) {
        const adLengthSeconds = parseInt(formData.adLength);
        try {
          // Check credits needed for this feature
          const checkResponse = await checkCredits('video_campaign', { durationInSeconds: adLengthSeconds });
          
          if (creditInfo) {
            setCreditInfo(prev => ({
              ...prev!,
              requiredCredits: checkResponse.requiredCredits,
              hasEnoughCredits: checkResponse.hasEnoughCredits
            }));
            
            // Set warning if not enough credits
            if (!checkResponse.hasEnoughCredits) {
              setCreditWarning(`This project requires ${checkResponse.requiredCredits} credits, but you only have ${checkResponse.currentBalance} credits available.`);
            } else {
              setCreditWarning(null);
            }
          }
        } catch (error) {
          console.error('Error checking credits:', error);
        }
      } else {
        // Reset credit requirement when ad length is not selected
        if (creditInfo) {
          setCreditInfo(prev => ({
            ...prev!,
            requiredCredits: 0,
            hasEnoughCredits: true
          }));
        }
        setCreditWarning(null);
      }
    };
    
    if (isOpen && creditInfo) {
      updateCreditRequirement();
    }
  }, [formData.adLength, creditInfo?.balance, isOpen]);

  const handleNavigation = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsTooltipOpen(false); // Close tooltip
    onClose(); // Close dialog
    // Use setTimeout to ensure navigation happens after modal is closed
    setTimeout(() => {
      router.push("/adtargeting/raghome");
    }, 100);
  };

  // Handle product file upload
  const handleProductUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setIsUploadingProduct(true);
    setUploadError(null);
    
    try {
      // Create a local URL for immediate preview
      const localPreviewUrl = URL.createObjectURL(file);
      setProductPreviewUrl(localPreviewUrl);
      
      // Prepare form data for upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'product');
      
      // Upload to S3 via API
      const response = await fetch('/api/video-editing/upload-logo', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload product');
      }
      
      const data = await response.json();
      
      // Update form data with S3 URL
      setFormData(prev => ({
        ...prev,
        productUrl: data.url
      }));
      
      // Clean up local preview URL
      URL.revokeObjectURL(localPreviewUrl);
      setProductPreviewUrl(data.url);
      
      // Reset input
      e.target.value = '';
    } catch (error) {
      console.error("Error uploading product:", error);
      setUploadError("Failed to upload product. Please try again.");
    } finally {
      setIsUploadingProduct(false);
    }
  };
  
  // Handle logo file upload - now sets the same logo for both intro and end screens
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setIsUploadingLogo(true);
    setUploadError(null);
    
    try {
      // Create a local URL for immediate preview
      const localPreviewUrl = URL.createObjectURL(file);
      setLogoPreviewUrl(localPreviewUrl);
      
      // Prepare form data for upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'end'); // Use 'end' type for logo
      
      // Upload to S3 via API
      const response = await fetch('/api/video-editing/upload-logo', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload logo');
      }
      
      const data = await response.json();
      
      // Update form data with S3 URL for both intro and end logos
      setFormData(prev => ({
        ...prev,
        introLogoUrl: data.url, // Set same logo for intro screen
        endLogoUrl: data.url    // Set same logo for end screen
      }));
      
      // Clean up local preview URL
      URL.revokeObjectURL(localPreviewUrl);
      setLogoPreviewUrl(data.url);
      
      // Reset input
      e.target.value = '';
    } catch (error) {
      console.error("Error uploading logo:", error);
      setUploadError("Failed to upload logo. Please try again.");
    } finally {
      setIsUploadingLogo(false);
    }
  };
  
  // Trigger file input click
  const triggerProductFileUpload = () => {
    productFileInputRef.current?.click();
  };
  
  const triggerLogoFileUpload = () => {
    logoFileInputRef.current?.click();
  };
  
  // Toggle upload section visibility
  const toggleUploadSection = () => {
    setShowUploadSection(!showUploadSection);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Use the createVideoCampaign function from credit-api.ts
      const result = await createVideoCampaign({
        title: formData.title,
        brandName: formData.brandName,
        summary: formData.summary,
        description: formData.description,
        adLength: parseInt(formData.adLength),
        productUrl: formData.productUrl,   // Product URL
        introLogoUrl: formData.introLogoUrl, // Logo for intro screen
        endLogoUrl: formData.endLogoUrl,    // Logo for end screen
      });
      
      // Make sure the result contains the project ID
      if (!result || !result.project || !result.project.id) {
        throw new Error('Invalid response from server');
      }
      
      // Execute the navigation after the video project is created
      onClose();
      const params = new URLSearchParams();
      params.set('projectId', result.project.id);
      router.push(`/display?${params.toString()}`);
      
    } catch (error) {
      console.error('Error creating video project:', error);
      
      // Check for unauthorized error (user not logged in)
      // This happens when the API returns a 401 status code
      if (error instanceof Error && 
         (error.message.includes('Unauthorized') || 
          error.message.includes('401') || 
          error.message.includes('unauthenticated'))) {
        
        // Save form data to session storage to potentially restore after login
        sessionStorage.setItem('pendingVideoCampaignData', JSON.stringify({
          title: formData.title,
          brandName: formData.brandName,
          summary: formData.summary,
          description: formData.description,
          adLength: formData.adLength,
        }));
        
        // Redirect to sign-in page with return URL
        // After login, user will be redirected back to the home page
        const returnUrl = encodeURIComponent('/');
        router.push(`/sign-in?redirect=${returnUrl}`);
        return;
      }
      
      // Handle the specific case of insufficient credits
      if (error instanceof Error && error.message.includes('Insufficient credits')) {
        setError('You do not have enough credits to create this video project.');
      } else {
        setError(error instanceof Error ? error.message : 'Create Video Project Failed, please try again.');
      }
      // Don't close the form after an error
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
{/* tooltip text (beside upload file button) */}
  const TooltipText = () => (
    <div className="max-w-[250px] text-left text-gray-700 font-sans text-[12px]">
      <p>We use this uploaded image/video of your product or brand to create an on-brand video campaign for you.</p>
    </div>
  );
 {/* "add summary" tooltip */}
  const TooltipTextAddSummary = () => (
    <div className="max-w-[250px] text-left text-gray-700 font-sans text-[12px]">
      <p>
      If you need help defining your campaign targets,
        please return to{" "}
        <button 
          onClick={handleNavigation}
          className="text-blue-400 hover:text-blue-300 underline focus:outline-none"
        >
          homepage
        </button>
        {" "} and use our generate audience analysis & targets system.
      </p>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-md font-semibold">
            Create a Video Campaign
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Project Title field */}
          <div className="space-y-3">
            <Input
              id="title"
              name="title"
              placeholder="Project Title"
              value={formData.title}
              onChange={handleChange}
              className="h-8 w-full font-sans text-xs"
            />
          </div>
          {/* Brand Name field */}
          <div className="space-y-3">
            <Input
              id="brandName"
              name="brandName"
              placeholder="Brand/Product Name"
              value={formData.brandName}
              onChange={handleChange}
              className="h-8 w-full font-sans text-xs"
            />
          </div>
          {/* Campaign Target Summary field */}
          <div className="flex flex-row">
            <Input
              id="summary"
              name="summary"
              placeholder="Campaign Target Summary"
              value={formData.summary}
              title={formData.summary}
              onChange={handleChange}
              className="h-8 w-5/6 font-sans text-xs"
            />
            {/* "add summary" info */}
            {!isInfo && (
                <TooltipProvider>
                  <Tooltip open={isInfoOpen} onOpenChange={setIsInfoOpen}>
                    <TooltipTrigger asChild>
                    <Info className="h-3 w-3 mt-2.5 ml-3.5 text-gray-500 hover:text-gray-700 cursor-pointer"/>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="bg-white rounded-md shadow-lg">
                      <TooltipTextAddSummary />
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              </div>
          {/* Short Description field */}
          <div className="space-y-2">
            <Input
              id="description"
              name="description"
              placeholder="Short Description"
              value={formData.description}
              onChange={handleChange}
              className="h-8 w-full font-sans text-xs"
            />
          </div>
          {/* Select duration dropdown field */}
          <div className="space-y-2">
            <Select
              value={formData.adLength}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, adLength: value }))
              }
            >
              <SelectTrigger className="h-8 w-full font-sans text-xs rounded-md border border-gray-200 bg-white px-3 py-2 ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-neutral-950 placeholder:text-neutral-500   focus:border-cyan-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neutral-400  focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-neutral-800 dark:bg-neutral-950 dark:ring-offset-neutral-950 dark:file:text-neutral-50 dark:placeholder:text-neutral-400 dark:focus-visible:ring-neutral-300">
                <SelectValue placeholder="Select duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 seconds</SelectItem>
                <SelectItem value="10">10 seconds</SelectItem>
                <SelectItem value="15">15 seconds</SelectItem>
                <SelectItem value="30">30 seconds</SelectItem>
                <SelectItem value="60">60 seconds</SelectItem>
                <SelectItem value="120">2 minutes</SelectItem>
                <SelectItem value="180">3 minutes</SelectItem>
                <SelectItem value="240">4 minutes</SelectItem>
                <SelectItem value="300">5 minutes</SelectItem>
              </SelectContent>
            </Select>
            
            {/* Display credit information */}
            {isLoadingCredits ? (
              <div className="mt-2 text-xs text-gray-500">Loading credit information...</div>
            ) : creditInfo ? (
              <div className="mt-2 text-xs">
                <div className="flex justify-between mb-1">
                  <span className="text-gray-600">Available credits:</span>
                  <span className="font-medium">{creditInfo.balance || 0}</span>
                </div>
                {creditInfo.requiredCredits > 0 && (
                  <>
                    <Progress 
                      value={(creditInfo.requiredCredits / Math.max(creditInfo.balance || 1, 1)) * 100} 
                      className="h-1.5" 
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-gray-500 text-[10px]">Required for this project</span>
                      <span className="text-gray-500 text-[10px]">{creditInfo.requiredCredits} credits</span>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="mt-2 text-xs text-gray-500">Credit information unavailable</div>
            )}
            
            {/* Show credit warning if applicable */}
            {creditWarning && (
              <div className="mt-1 text-red-500 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">{creditWarning}</span>
              </div>
            )}
            
          <div className="space-y-2">        
            <div className="flex">
            <p className="font-sans text-xs mt-1">
              Upload Brand Assets (Optional)
            </p>
            {/* tooltip beside upload button */}
            <TooltipProvider>
              <Tooltip open={isTooltipOpen} onOpenChange={setIsTooltipOpen}>
                <TooltipTrigger asChild>
                  <Info className="h-3 w-3 mt-2 ml-2 text-gray-500 hover:text-gray-700 cursor-pointer"/>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-white rounded-md shadow-lg">
                  <TooltipText />
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {/* Upload/Expand button */}
            <div 
              onClick={toggleUploadSection} 
              className="h-8 w-8 ml-2 bg-cyan-50 hover:bg-cyan-100 transition cursor-pointer stroke-cyan-700 p-1 border border-transparent rounded-md flex items-center justify-center"
            >
              <Upload className="h-5 w-5" />
            </div>
          </div>
          
          {/* Expandable Upload Section */}
          {showUploadSection && (
            <div className="mt-3 space-y-4 border p-3 rounded-md">
              {/* Product Upload */}
              <div>
                <p className="text-xs font-medium mb-2">The product</p>
                <div 
                  onClick={triggerProductFileUpload}
                  className={`group border-2 border-dashed rounded-lg p-2 text-center cursor-pointer transition-colors 
                    border-gray-300 hover:border-cyan-500 ${isUploadingProduct && "opacity-50 pointer-events-none"}`}
                >
                  {productPreviewUrl ? (
                    <div className="flex flex-col items-center">
                      {productPreviewUrl.includes('.mp4') || productPreviewUrl.includes('.mov') ? (
                        <video 
                          src={productPreviewUrl} 
                          className="max-h-20 max-w-full object-contain mb-1"
                          controls
                        />
                      ) : (
                        <img 
                          src={productPreviewUrl} 
                          alt="Product preview" 
                          className="max-h-20 max-w-full object-contain mb-1"
                        />
                      )}
                      <p className="text-xs text-gray-500 group-hover:text-cyan-500">
                        Click to change
                      </p>
                    </div>
                  ) : (
                    <div className="py-2">
                      {isUploadingProduct ? (
                        <div className="h-4 w-4 border-2 border-t-cyan-500 border-gray-200 rounded-full animate-spin mx-auto"></div>
                      ) : (
                        <p className="text-xs text-gray-500 group-hover:text-cyan-500">
                          Click to upload product
                        </p>
                      )}
                    </div>
                  )}
                  <input 
                    ref={productFileInputRef}
                    type="file" 
                    className="hidden" 
                    accept="image/*,video/*"
                    onChange={handleProductUpload}
                    disabled={isUploadingProduct}
                  />
                </div>
              </div>
              
              {/* Logo Upload (common for both intro and end screens) */}
              <div>
                <p className="text-xs font-medium mb-2">Logo</p>
                <div 
                  onClick={triggerLogoFileUpload}
                  className={`group border-2 border-dashed rounded-lg p-2 text-center cursor-pointer transition-colors 
                    border-gray-300 hover:border-cyan-500 ${isUploadingLogo && "opacity-50 pointer-events-none"}`}
                >
                  {logoPreviewUrl ? (
                    <div className="flex flex-col items-center">
                      <img 
                        src={logoPreviewUrl} 
                        alt="Logo preview" 
                        className="max-h-20 max-w-full object-contain mb-1"
                      />
                      <p className="text-xs text-gray-500 group-hover:text-cyan-500">
                        Click to change
                      </p>
                    </div>
                  ) : (
                    <div className="py-2">
                      {isUploadingLogo ? (
                        <div className="h-4 w-4 border-2 border-t-cyan-500 border-gray-200 rounded-full animate-spin mx-auto"></div>
                      ) : (
                        <p className="text-xs text-gray-500 group-hover:text-cyan-500">
                          Click to upload logo
                        </p>
                      )}
                    </div>
                  )}
                  <input 
                    ref={logoFileInputRef}
                    type="file" 
                    className="hidden" 
                    accept="image/*"
                    onChange={handleLogoUpload}
                    disabled={isUploadingLogo}
                  />
                </div>
              </div>
              
              {/* Upload error message */}
              {uploadError && (
                <div className="text-red-500 text-xs">
                  {uploadError}
                </div>
              )}
            </div>
          )}
            </div>
          </div>
          {error && (
            <div className="text-red-500 text-sm">
              {error}
            </div>
          )}
          <button
            type="submit"
            disabled={!isFormValid(formData) || isSubmitting || isLoadingCredits || (creditInfo && !creditInfo.hasEnoughCredits) || false}
            className={`h-7 w-full px-4 text-xs font-semibold
              bg-cyan-700 hover:bg-cyan-800 cursor-pointer text-center text-white rounded-md transition-opacity ${
              !isFormValid(formData) || isSubmitting || isLoadingCredits || (creditInfo && !creditInfo.hasEnoughCredits)
                ? "opacity-50 cursor-not-allowed"
                : "hover:opacity-90"
            }`}
          >
            {isSubmitting ? "Creating..." : isLoadingCredits ? "Loading..." : "Create"}
          </button>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default VideoAdCampaignForm;