"use client";

import React, { useState, useEffect } from 'react';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Info} from "lucide-react";
import { useUser } from '@/lib/auth';
import { use } from 'react';
// Form data type
interface FormData {
  description: string;
}

// Component props type
interface ProductFormProps {
  onSubmit?: (data: FormData) => void;
  isLoading?: boolean;
}

const ProductForm: React.FC<ProductFormProps> = ({ 
  onSubmit, 
  isLoading = false 
}) => {
  // Form state
  const [description, setDescription] = useState<string>('');
  {/*Initialize  tooltip's and info's state */}
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const [isInfoOpen, setIsInfoOpen] = useState(false);
  const [showWelcomePopup, setShowWelcomePopup] = useState(false);
  const [hasClickedSubmit, setHasClickedSubmit] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  
  // Check if user is logged in
  const { userPromise } = useUser();
  const currentUser = userPromise ? use(userPromise) : null;

  // Check localStorage on component mount
  useEffect(() => {
    const hasSeenWelcome = localStorage.getItem('hasSeenWelcomePopup') === 'true';
    if (!hasSeenWelcome) {
      // If they haven't seen it yet, we'll show it on first submit click
      setHasClickedSubmit(false);
    }
  }, []);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Ensure description is not empty and onSubmit callback exists
    if (description.trim() && onSubmit) {
      const hasSeenWelcome = localStorage.getItem('hasSeenWelcomePopup') === 'true';
      
      if (!hasSeenWelcome && !hasClickedSubmit) {
        // First time clicking submit and hasn't seen welcome
        setShowWelcomePopup(true);
        setHasClickedSubmit(true);
      } else {
        // Either has seen welcome or this is a subsequent click
        // Call the onSubmit callback passed from parent component
        onSubmit({ description });
      }
    }
  };

  // Handle view results (proceed with submission)
  const handleViewResults = () => {
    setShowWelcomePopup(false);
    if (dontShowAgain) {
      localStorage.setItem('hasSeenWelcomePopup', 'true');
    }
    if (description.trim() && onSubmit) {
      onSubmit({ description });
    }
  };

  // tooltip text
  const TooltipText = () => (
    <div className="max-w-[230px] text-left text-gray-700 font-sans text-[12px]">
      <p>We use a line description or image of the product to generate a precise marketing audience and insight for you.</p>
    </div>
  );
  return (
    <div className="bg-white rounded-lg shadow-md p-6 w-full max-w-2xl">
      <h2 className="text-xl font-sans text-gray-700 mb-3">
        Describe your product
      </h2>
      
      {/* Show message for anonymous users */}
      {!currentUser && (
        <div className="mb-4 p-3 bg-blue-50 text-blue-600 rounded-md text-sm">
          Try our audience generation tool for free! Sign in to save your results for later.
        </div>
      )}
      
      {/* Form submission will call handleSubmit function */}
      <form onSubmit={handleSubmit}>
        <div className="mb-3 flex items-center space-x-3">
          <div className="flex-grow">
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter a one-line description..."
              className="w-full px-4 py-3 border font-sans border-gray-200 rounded-md ring-offset-white focus:border-cyan-700 focus:ring-1 focus:ring-cyan-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neutral-400  focus-visible:ring-offset-2 placeholder:text-neutral-500 "
              required
            />
          </div>
          <button 
            type="button" 
            className="bg-cyan-50 hover:bg-cyan-100 transition cursor-pointer  text-cyan-700 p-2 rounded-md h-10 w-10 flex items-center justify-center"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" 
              />
            </svg>
          </button>
          <TooltipProvider>
                            <Tooltip open={isTooltipOpen} onOpenChange={setIsTooltipOpen}>
                              <TooltipTrigger asChild>
                              <Info className="h-4 w-4 mt-1 mx-2 text-gray-500 hover:text-gray-700 cursor-pointer"/>
                              </TooltipTrigger>
                              <TooltipContent side="bottom" className="bg-white rounded-md shadow-lg">
                                <TooltipText />
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
        </div>
        
        {/* 
          Submit button - triggers form submission when clicked
          When form is submitted, it will call handleSubmit function
          handleSubmit function will call onSubmit callback
          onSubmit callback is the handleSubmit function passed from ad-targeting/page.tsx
          That function will send API request and eventually display ResultComponent
        */}
        <button
          type="submit"
          disabled={isLoading || !description.trim()}
          className={`w-full py-3 px-4 bg-cyan-700 text-white font-sans rounded-md transition-colors ${
            isLoading || !description.trim() 
              ? 'opacity-70 cursor-not-allowed' 
              : 'hover:bg-cyan-800'
          }`}
        >
          {isLoading ? 'Processing...' : 'Generate Audience & Insight'}
        </button>
      </form>

      {/* Welcome Popup */}
      {showWelcomePopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to the Early Access Release</h3>
            <p className="text-gray-600 mb-4">
              This AI model for profile generation, audience analysis, and targeting is in its initial launch phase. 
              Results may vary as the system continues to evolve. Hence, your feedback is very important to its refinement.
            </p>
            <div className="flex flex-col space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="dontShowAgain"
                  checked={dontShowAgain}
                  onChange={(e) => setDontShowAgain(e.target.checked)}
                  className="rounded border-gray-300 text-cyan-700 focus:ring-cyan-700"
                />
                <label htmlFor="dontShowAgain" className="text-sm text-gray-600">
                  Don't see this again
                </label>
              </div>
              <div className="flex justify-end">
                <button
                  onClick={handleViewResults}
                  className="px-4 py-2 bg-cyan-700 text-white rounded-md hover:bg-cyan-800"
                >
                  View Results
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductForm;
