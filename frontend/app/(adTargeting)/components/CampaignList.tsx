"use-client"
import Link from 'next/link';
import { useState, useEffect, useCallback } from 'react';
import { useVideoProjectStore } from '@/lib/store/videoProjectStore';
import {Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent} from "@/components/ui/card"
import {Button} from "@/components/ui/button"
import {Divider} from "@/components/ui/Divider"
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { deleteVideoProject, getVideoScriptsByProjectId } from '@/lib/db/queries';
import { useRouter } from 'next/navigation';
import { Trash2,Video, SquareArrowOutUpRight,Clock, CalendarDays, Youtube } from 'lucide-react';
import DeleteDialog from '@/components/ui/Delete';
interface CampaignProps {
  campaign: {
    id: number;
    title: string;
    brandName: string;
    summary: string;
    status: string;
    adLength: string;
    description: string;
    createdAt: Date;
  } | null;
}

const CampaignList: React.FC<CampaignProps>= ()=> {
    const { 
        projects, 
        currentProject, 
        isLoading = true, 
        isCreating, 
        isUpdating, 
        isDeleting,
        fetchProjects, 
        fetchProject, 
        createProject, 
        updateProject, 
        deleteProject, 
        setCurrentProject 
      } = useVideoProjectStore();
    const router = useRouter();
    const [isError, setError] = useState<string | null>(null);
    const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
    //state for delete dialog
      const [isDelete, setIsDelete] = useState(false);
    //for redirecting to the display page with the project id passed 
    const redirectToDisplay= async (projectId: string) =>{
      try {
        const response = await fetch('/api/video-editing/video-projects', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
  
        if (!response.ok) {
          throw new Error('Failed to fetch video project');
        }
  
        const result = await response.json();
        const project = Array.isArray(result) ? result[0] : result;

        if (!project|| !project.id) {
          throw new Error('Invalid response from server');
        }

        const params = new URLSearchParams();
        params.set('projectId', project.id);
        router.push(`/display?projectId=${projectId}`);
        
      } catch (error) {
        console.error('Error fetching video project:', error);
        setError(error instanceof Error ? error.message : 'Fetching a Video Project Failed, please try again.');
      } finally {
        isLoading;
      }
    }
     
    // open delete pop up
  const DeletePopUp = (projectId: string)=>{
    setSelectedProjectId(projectId);
    setIsDelete(true);
  }
  //close the delete pop up
  const CloseDelete = ()=>{
    setIsDelete(false);
  }
      // Format date
  const formatDate = (date:string| Date) => {
    return new Date(date).toISOString().split('T')[0];
  };
  //for fetching projects  
  useEffect(() => {
    try{
    fetchProjects();
  }finally{
    !isLoading;
  }
  }, []);
  
    return(
      <div className="w-full max-w-2xl flex flex-col h-[calc(100vh-200px)] bg-white p-4 rounded-md shadow-md">
      <div className="overflow-y-auto flex-grow items-center justify-between">
      <div className="bg-gray-100 rounded-lg p-2 h-24 mb-4">
        <h2 className="text-xl font-sans text-gray-800 mb-4 text-center mt-6">
          Your Video Campaigns
        </h2>
      </div>

      {isLoading && (
        <div className="text-center py-8">
          <p className="text-gray-600">Loading...</p>
        </div>
      )}

      {isError && (
        <div className="text-center py-8">
          <p className="text-red-500">{isError}</p>
        </div>
      )}

      {!isLoading && !isError && projects.length === 0 && (
        <div className="text-center font-sans py-8">
          <p className="text-gray-600">No video campaigns available</p>
        </div>
      )}
      <div className="space-y-3">
        {!isLoading && projects.map((project) => (
          <div 
            key={project.id}
            className="flex flex-col bg-gray-50 rounded-lg shadow-sm py-4 hover:shadow-md transition-shadow"
          >{/*Project's title, status and date */}
            <div className="flex justify-between items-end px-3">
                <h3 className="font-medium text-gray-800">{project.title}</h3>
                <div className='flex flex-col space-y-1'>
              <p className="text-sm  font-sans bg-amber-100 rounded-xl py-1 px-2 text-amber-900  text-center">{project.status}</p>
              <div className="text-xs text-gray-400 flex flex-row space-x-1"><CalendarDays  className='h-3.5 w-3.5 text-gray-500'/> {formatDate(project.createdAt)}</div>
              </div></div>
              <div className="w-full flex flex-col">
  <Divider />
</div>{/*Project details */}
              <div className='px-3'><p className="text-sm font-sans mt-1"><span className='text-gray-500 font-semibold '>Brand: </span><span className='text-cyan-700'>{project.brandName}</span></p>
                          <p className="text-sm font-sans mt-1 line-clamp-3"><span className='text-gray-500 font-semibold '>Summary:</span><span className='text-gray-600 '> {project.summary}</span></p>
                         <div className='flex flex-row justify-between'> <div className="flex flex-row mt-1 space-x-1"><span><Clock className='h-3.5 w-3.5 stroke-gray-500 mt-1'/></span><span className='text-gray-500 text-sm font-sans'> {Number(project.adLength) > 60  ? `${Number(project.adLength) / 60} minutes` : `${Number(project.adLength)} seconds`}</span></div>{/*first convert project.adLength from string to integer*/}
                         <div className="flex flex-row mt-1 space-x-1"> <Youtube className='h-3.5 w-3.5 stroke-gray-500 mt-1' /> <span className=" font-sans text-sm text-gray-500">segments</span></div></div>
                          <div className="flex flex-row mt-1 justify-between items-center">
                          <Button onClick={() => redirectToDisplay(project.id)}  className="h-8 bg-white border hover:border-none border-cyan-700 hover:cursor-pointer hover:bg-cyan-700 text-black hover:text-white font-sans text-sm">Edit Project</Button>
                          <Trash2  onClick={() => DeletePopUp(project.id)}  className="h-4 w-4 stroke-red-700 hover:stroke-red-800  cursor-pointer mt-2" />
                            </div></div> 
          </div>
        ))}
      </div>
      </div>
      {isDelete && selectedProjectId && (
      <DeleteDialog Open={isDelete} onClose={CloseDelete} projectId={selectedProjectId}/>
    )} 
    </div>  
    );
};

export default CampaignList;
