"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ResultComponent from './ResultComponent';
import { Trash2 } from 'lucide-react';
import DeleteDialog from '@/components/ui/DeletePastAudiences';
// Define the type for ad campaign item
interface AdCampaign {
  id: number;
  description: string;
  createdAt: Date;
  responseData: any;
  status: string;
}

interface PastAudiencesProps {
  onSelectResult: (data: any) => void;
}

const PastAudiences: React.FC<PastAudiencesProps> = ({ onSelectResult }) => {
  const [campaigns, setCampaigns] = useState<AdCampaign[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCampaignId, setSelectedCampaignId] = useState<number | null>(null);
  //state for delete dialog
  const [isDelete, setIsDelete] = useState(false);


    const fetchCampaigns = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/ad-targeting/campaigns');
        
        if (!response.ok) {
          throw new Error('Failed to fetch campaigns');
        }
        
        const data = await response.json();
        setCampaigns(data);
      } catch (err) {
        console.error('Error fetching campaigns:', err);
        setError('Unable to load historical ad campaign data');
      } finally {
        setLoading(false);
      }
    };

    
  //for fetching the ad campaigns
  useEffect(() => {
    fetchCampaigns();
  }, []);

  const handleItemClick = (campaign: AdCampaign) => {
    onSelectResult(campaign.responseData);
  };
   // open delete pop up
   const DeletePopUp = (adCampaignId: number)=>{
    setSelectedCampaignId(adCampaignId);
    setIsDelete(true);
  }
  //close the delete pop up
  const CloseDelete = ()=>{
    setIsDelete(false);
  }
  // Extract interest tags from responseData
  const extractInterests = (responseData: any) => {
    if (!responseData?.targetingResults?.sections) return [];
    
    const interestSection = responseData.targetingResults.sections.find(
      (section: any) => section.id === 'int-001'
    );
    
    if (!interestSection?.data || !Array.isArray(interestSection.data)) return [];
    
    return interestSection.data.map((item: string) => {
      const [category, values] = item.split(': ');
      if (!values) return '';
      return values.split(',')[0].trim();
    }).filter(Boolean);
  };

  // Extract summary from responseData
  const extractSummary = (responseData: any) => {
    if (!responseData) return 'No summary available';
    
    // Adjust extraction logic based on actual data structure
    if (responseData.summary) return responseData.summary;
    if (responseData.description) return responseData.description;
    if (responseData.audience_description) return responseData.audience_description;
    
    return 'No summary available';
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toISOString().split('T')[0];
  };

  return (
    <div className="w-full max-w-2xl flex flex-col h-[calc(100vh-200px)] bg-white p-4 rounded-md shadow-md">
      <div className="overflow-y-auto flex-grow items-center justify-between">
      <div className="bg-gray-100 rounded-lg p-2 mb-4">
        <h2 className="text-xl font-sans text-gray-800 mb-4 text-center">
          Past Audiences & Insights
        </h2>
        <p className="text-sm font-sans text-gray-600 mb-2 text-center">
          View previously analyzed audience groups and insights
        </p>
      </div>

      {loading && (
        <div className="text-center py-8">
          <p className="text-gray-600">Loading...</p>
        </div>
      )}

      {error && (
        <div className="text-center py-8">
          <p className="text-red-500">{error}</p>
        </div>
      )}

      {!loading && !error && campaigns.length === 0 && (
        <div className="text-center font-sans py-8">
          <p className="text-gray-600">No historical ad campaign data available</p>
        </div>
      )}

      <div className="space-y-3">
        {campaigns.map((campaign) => (
<div className="bg-gray-50 rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-shadow" key={campaign.id}>
          <div 
            onClick={() => handleItemClick(campaign)}
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium text-gray-800">{campaign.description}</h3>
                <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                  {extractSummary(campaign.responseData)}
                </p>
              </div>
              <span className="text-xs text-gray-400 whitespace-nowrap">{formatDate(campaign.createdAt)}</span>

            </div>
            
            {extractInterests(campaign.responseData).length > 0 && (
              <div className="mt-3 flex flex-wrap gap-2">
                {extractInterests(campaign.responseData).map((interest: string, index: number) => (
                  <span 
                    key={index} 
                    className="px-2 py-1 bg-cyan-50 text-cyan-700 font-sans text-xs rounded-md"
                  >
                    {interest}
                  </span>
                ))}
              </div>
            )}   
          </div>
          <div className='place-items-end'>
          <Trash2  onClick={(e) =>{ e.stopPropagation(); DeletePopUp(campaign.id)}}  className="h-4 w-4 stroke-red-700 hover:stroke-red-800  cursor-pointer mt-2" />
          {isDelete && selectedCampaignId && (
    <DeleteDialog Open={isDelete} onClose={CloseDelete} adCampaignId={selectedCampaignId} onDeleted={()=>{ setIsDelete(false); fetchCampaigns();}}/>)}
    </div>
          </div>
        ))}

      </div>
      </div>
    </div>
  );
};

export default PastAudiences;
