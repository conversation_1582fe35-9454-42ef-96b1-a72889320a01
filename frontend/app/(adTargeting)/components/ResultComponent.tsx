"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { TargetingResults } from '@/lib/models/TargetingResult';
import ResultDetail from './ResultDetail';
import VideoAdCampaignForm from "./VideoAdCampaignForm";
interface ResultComponentProps {
  data: {
    summary: string;
    projectTitle: string;
    description: string;
    targetingResults: TargetingResults;
  };
  onBack: () => void;
  resultSource?: 'form' | 'pastAudience';
}

const ResultComponent: React.FC<ResultComponentProps> = ({ data, onBack, resultSource = 'form' }) => {
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({});
  const [isVideoFormOpen, setIsVideoFormOpen] = useState(false);
  const router = useRouter();
  const handleToggle = (id: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };
  
  // Open video ad campaign form with data from API response
  const handleCreateAd = () => {
    console.log("Create Video Campaign clicked");
    setIsVideoFormOpen(true);
  };

    //to clear the conversation cookie when clicking on marketing ai 
    const startNewChat = () =>{
      document.cookie = 'conversation_id=; Max-Age=0; path=/;';
      router.push('/marketing-ai');
    }
  return (
    <>
    <div className="w-full max-w-2xl flex flex-col h-[calc(100vh-200px)] bg-white p-4 rounded-md shadow-md">
      <div className="overflow-y-auto flex-grow items-center justify-between">
      <h2 className="text-xl text-center font-bold font-sans text-gray-800 mb-4">
            Result for Audience Analysis, Marketing Profile, and Insights
          </h2>
        <div className="bg-gray-100 rounded-md p-2 mb-4">
          
          <div className="my-4">
            <p className="text-md text-gray-600 font-sans text-center md:text-start">
              Generate a Video Campaign for this audience profile 
              <button 
                onClick={handleCreateAd}
                className="text-cyan-700 font-sans hover:text-cyan-800 ml-1 underline cursor-pointer bg-transparent border-none"
              >
                here
              </button>.
            </p>
            <p className="text-md text-gray-600 font-sans text-center md:text-start">
              Interested in going granular on this result? Use our agent 
              <Link href="/marketing-ai" className="text-cyan-700 font-sans hover:text-cyan-800 ml-1" onClick={()=>{startNewChat()}}>here</Link>.
            </p>
          </div>
        </div>

        <div className="bg-gray-100 rounded-lg p-6 mb-4">
          <h3 className="text-lg font-sans font-bold text-center text-gray-700 mb-2">Summary</h3>
          <p className="text-gray-600 font-sans text-center md:text-start">{data.summary}</p>
        </div>

        <div className="bg-gray-200 rounded-lg shadow-md p-6 mb-4">
          <h3 className="text-lg font-sans text-gray-700 mb-2">Detailed Insights</h3>
          <ResultDetail 
            targetingResults={data.targetingResults}
            expandedSections={expandedSections}
            handleToggle={handleToggle}
          />
        </div>
      </div>

      <div className="sticky bottom-0 pt-4 bg-gray-50">
        <button
          onClick={onBack}
          className="w-full py-3 px-4 bg-cyan-700 text-white  font-sans rounded-md hover:bg-cyan-800 cursor-pointer transition-colors"
        >
          {resultSource === 'pastAudience' ? 'Back to History Audiences' : 'Back to Form'}
        </button>
      </div>
    </div>
    {isVideoFormOpen && (
      <VideoAdCampaignForm
        isOpen={isVideoFormOpen}
        onClose={() => setIsVideoFormOpen(false)}
        summary={data.summary}
        projectTitle={data.projectTitle}
        description={data.description}
      />
    )}
    </>
  );
};

export default ResultComponent;
