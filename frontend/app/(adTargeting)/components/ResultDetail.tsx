import React from "react";
import {
  TargetingResults as TargetingResultsType,
  Section,
  SubSection
} from "@/lib/models/TargetingResult";

interface ResultDetailProps {
  targetingResults: TargetingResultsType | null;
  expandedSections: { [key: string]: boolean };
  handleToggle: (id: string) => void;
}

const ResultDetail: React.FC<ResultDetailProps> = ({
  targetingResults,
  expandedSections,
  handleToggle
}) => {
  const renderTags = (data: string[]) => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800">
        Reach your audience through these platforms and channels in order of priority:
      </h3>

      <div className="flex flex-wrap gap-2">
      {data.map((item, idx) => (
        <span
          key={idx}
          className="relative"
        >
          <span className="absolute -top-2 -left-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold shadow-sm">
              {idx + 1}
            </span>
            <span className="bg-blue-500 text-white px-4 py-1 rounded-full shadow-md inline-block">
              {item}
            </span>
        </span>
      ))}
    </div>
    </div>
  );

  const renderList = (data: string[]) => (
    <ul className="space-y-2 list-disc list-inside">
      {data.map((item, idx) => {
        const highlightedText = item.replace(
          /^(.*?):/g,
          "<span style='font-weight: 550;'>$1</span>:" 
        );
  
        return (
          <li key={idx} className="text-gray-700">
            <span
              dangerouslySetInnerHTML={{ __html: highlightedText }}
            />
          </li>
        );
      })}
    </ul>
  );
  

  const renderStructured = (data: SubSection[]) => (
    <div className="space-y-6">
      {data.map((subSection) => (
        <div key={subSection.id} className="bg-gray-50 p-4 rounded-lg">
          <h5 className="font-semibold text-lg mb-3">{subSection.subTitle}</h5>
          {subSection.descriptions && (
            <>
              <div className="space-y-4">
                {subSection.descriptions.map((desc, idx) => (
                  <div key={idx}>
                    <h5 className="font-semibold text-gray-800">
                      {desc.subTitle}
                    </h5>
                    <div className="bg-white p-3 rounded shadow-sm">
                      {desc.description}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
          {subSection.reviewGroups && (
            <div className="space-y-4 mt-4">
              {subSection.reviewGroups.map((group, idx) => (
                <div key={idx}>
                  <h5 className="font-semibold mb-2">{group.subTitle}</h5>
                  <div className="bg-white p-3 rounded shadow-sm">
                    <ul className="space-y-2 list-disc list-inside">
                      {group.reviews.map((review, reviewIdx) => (
                        <li key={reviewIdx} className="text-gray-800">
                          <span
                            dangerouslySetInnerHTML={{
                              __html: review.description.replace(
                                /^(.*?):/g,
                                "<span style='font-weight: 550;'>$1</span>:"
                              ),
                            }}
                          />
                          {review.reviewer && (
                            <span className="text-sm text-gray-600">
                              <span> reviews.</span>
                              <a
                                href={review.link ? review.link : undefined}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-500 hover:underline"
                              >
                                {` [${review.reviewer}]`}
                              </a>
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );

  const renderSectionContent = (section: Section) => {
    switch (section.type) {
      case "tags":
        return renderTags(section.data as string[]);
      case "list":
        return renderList(section.data as string[]);
      case "structured":
        return renderStructured(section.data as SubSection[]);
      default:
        return null;
    }
  };

  if (!targetingResults) {
    return <div className="text-center text-gray-500">No data available.</div>;
  }

  return (
    <>
      <div className="flex flex-wrap -mx-3">
        {/* Left Column */}
        <div className="w-full md:w-1/2 px-3 mb-6">
          {targetingResults.sections
            .filter((_, index) => index % 2 === 0)
            .map((section) => (
              <div
                key={section.id}
                className="bg-white rounded-lg shadow-md overflow-hidden mb-4"
              >
                <div
                  className="cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors p-4"
                  onClick={() => handleToggle(section.id)}
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-xl text-gray-800">
                      {section.title}
                    </h3>
                    <span className="text-gray-500">
                      {expandedSections[section.id] ? "▼" : "▶"}
                    </span>
                  </div>
                </div>
                {expandedSections[section.id] && (
                  <div className="p-4">{renderSectionContent(section)}</div>
                )}
              </div>
            ))}
        </div>

        {/* Right Column */}
        <div className="w-full md:w-1/2 px-3 mb-6">
          {targetingResults.sections
            .filter((_, index) => index % 2 !== 0)
            .map((section) => (
              <div
                key={section.id}
                className="bg-white rounded-lg shadow-md overflow-hidden mb-4"
              >
                <div
                  className="cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors p-4"
                  onClick={() => handleToggle(section.id)}
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-xl text-gray-800">
                      {section.title}
                    </h3>
                    <span className="text-gray-500">
                      {expandedSections[section.id] ? "▼" : "▶"}
                    </span>
                  </div>
                </div>
                {expandedSections[section.id] && (
                  <div className="p-4">{renderSectionContent(section)}</div>
                )}
              </div>
            ))}
        </div>
      </div>
    </>
  );
};

export default ResultDetail;
