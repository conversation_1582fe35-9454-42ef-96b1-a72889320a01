"use client";
import React, { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import { useRouter,useSearchParams } from 'next/navigation';
import VideoAdCampaignForm from "./VideoAdCampaignForm";
import {Dialog,DialogContent,
  DialogHeader,
  DialogTitle} from "@/components/ui/dialog"
import  ComingSoon  from "@/components/ui/ComingSoon";
type NavItem = {
  title: string;
  href: string;
  onClick?: () => void;
  hasDropdown?: boolean;
  dropdownItems?: { title: string; href: string }[];
};

interface SidebarProps {
  activeItem?: string;
  isMobileOpen?: boolean;
  isVideoFormOpen?: boolean;
  onCloseMobile?: () => void;
  onNavItemClick?: (item: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  activeItem, 
  isMobileOpen = false,
  onCloseMobile,
  onNavItemClick
}) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [isVideoFormOpen, setIsVideoFormOpen] = useState(false);
  const [targetingSummary, setTargetingSummary] = useState<string>("");
  //state for coming soon 
  const [isComingSoon, setIsComingSoon] = useState(false);
  const sidebarRef = useRef<HTMLDivElement | null>(null); 
  const router = useRouter();
  const params = useSearchParams()
  // Close sidebar if clicked outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node) &&
        isMobileOpen && 
        onCloseMobile
      ) {
        onCloseMobile();
      }
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileOpen, onCloseMobile]);
  
  useEffect(()=>{
    if (typeof document !== 'undefined'){
      const previousRoute = document.referrer; //set the previous route
      const currentRoute = window.location.pathname; //set the current route
      if (currentRoute === '/adTargeting' && params.get('VideoForm') === 'true' ){
        setIsVideoFormOpen(true); //set the video campaign form open by default when  redirect to the '/adTargeting'
      }
    }
  }, []);

  // 打开视频广告活动表单
  const handleCreateAd = () => {
    console.log("Create Video Campaign clicked");
    setIsVideoFormOpen(true);
  };

  // open coming soon pop up
  const ComingSoonPopUp = ()=>{
    setIsComingSoon(true);
  }
  const navItems: NavItem[] = [
    { title: 'Create a Video Campaign', href: " ", onClick: () => { handleCreateAd(); }},
    //{ title: 'Edit a Video Campaign', href:"#", onClick: () => { ComingSoonPopUp(); }},
    //{ title: 'Campaign Manager', href:"#", onClick: () => { ComingSoonPopUp(); } },
    { title: 'Marketing AI Agent', href: '/marketing-ai', onClick: () => { startNewChat(); } },
    { 
      title: 'History', 
      href: '#', 
      hasDropdown: true,
      dropdownItems: [
        { title: 'Video Campaigns', href: '#videoCampaigns' },
        { title: 'Past Audience & Insights', href: '#past-audiences' },
        { title: 'Past Chats', href: '/history/chats' }
      ]
    },
  ];

  const handleLinkClick = () => {
    if (onCloseMobile) {
      onCloseMobile();
    }
  };
  
  //to clear the conversation cookie and id when clicking on marketing ai 
  const startNewChat = () =>{
    document.cookie = 'conversation_id=; Max-Age=0; path=/;';
    router.push('/marketing-ai');
    if (onCloseMobile){
       onCloseMobile();
    }
  }

  // Handle dropdown click 
  const handleDropdownClick = (title: string) => {
    setOpenDropdown((prev) => (prev === title ? null : title));
  };

  const handleDropdownItemClick = (item: string, href: string) => {
    if (href.startsWith('#') && onNavItemClick) {
      onNavItemClick(item);
      if (window.location.pathname !== '/adTargeting') {
        window.location.href = '/adTargeting' + href; 
      } else {
      // Update the URL hash
      window.location.hash = href.substring(1);
      }
    }
  };
  

  //close the coming soon pop up
  const CloseComingSoon = ()=>{
    setIsComingSoon(false);
  }
  return (
    <>
      {/* Sidebar */}
      <aside ref={sidebarRef}
        className={`
          md:sticky mt-3 md:mt-6
          w-64 md:w-64 
          h-[calc(100vh-8rem)]
          bg-white shadow-md rounded-md
          z-30 
          transition-all duration-200 ease-out
          ${isMobileOpen ? 'fixed top-16 left-0 h-[calc(100vh-4rem)]' : 'fixed -left-80 md:relative md:left-0'}
          overflow-y-auto
        `}>
        <div className="p-4">
        {navItems.map((item, index) => (
          <div key={index} className="mb-4 rounded-md">
            {item.hasDropdown ? (
              <div>
                <button
                  onClick={() => handleDropdownClick(item.title)}
                  className={`w-full text-left py-2 px-3 rounded-md transition-colors text-sm ${
                    activeItem === item.title ? 'text-cyan-700' : 'text-cyan-700 hover:text-cyan-800'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="truncate whitespace-nowrap">{item.title}</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-4 w-4 flex-shrink-0 ml-1 transition-transform ${openDropdown === item.title ? 'transform rotate-180' : ''}`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>
                {openDropdown === item.title && item.dropdownItems && (
                  <div className="ml-4 mt-2 space-y-1">
                    {item.dropdownItems.map((dropdownItem, dropdownIndex) => (
                      dropdownItem.href.startsWith('#') ? (
                        <button
                          key={dropdownIndex}
                          onClick={() => handleDropdownItemClick(dropdownItem.title, dropdownItem.href)}
                          className="block w-full text-left py-1.5 px-2 text-sm text-cyan-700 hover:cursor-pointer hover:text-cyan-800 hover:bg-cyan-50 rounded-md transition-colors truncate whitespace-nowrap"
                        >
                          {dropdownItem.title}
                        </button>
                      ) : (
                        <Link
                          key={dropdownIndex}
                          href={dropdownItem.href}
                          className="block py-1.5 px-2 text-sm text-cyan-700 hover:bg-cyan-50 hocer:cursor-pointer hover:text-cyan-800 rounded-md transition-colors truncate whitespace-nowrap"
                          onClick={handleLinkClick}
                        >
                          {dropdownItem.title}
                        </Link>
                      )
                    ))}
                  </div>
                )}
              </div>
            ) : (
              item.onClick &&  item.href.startsWith('#') ? (
                <button
                  onClick={item.onClick}
                  className="block w-full text-left py-2 px-3 rounded-md transition-colors text-sm  text-cyan-900  truncate whitespace-nowrap"
                >
                  {item.title}
                </button>
              ):item.onClick ? (
                <button
                  onClick={item.onClick}
                  className="block w-full text-left py-2 px-3 rounded-md transition-colors text-sm hover:bg-cyan-50 cursor-pointer text-cyan-700  truncate whitespace-nowrap"
                >
                  {item.title}
                </button>
              ) : (
                <Link
                  href={item.href}
                  className="block py-2 px-3 rounded-md transition-colors text-sm hover:bg-cyan-50 cursor-pointer text-cyan-700 hover:text-cyan-800 truncate whitespace-nowrap"
                  onClick={handleLinkClick}
                >
                  {item.title}
                </Link>
              )
            )}
          </div>
        ))}
      </div>
    </aside>
      {isVideoFormOpen && (
        <VideoAdCampaignForm
          isOpen={isVideoFormOpen}
          onClose={() => setIsVideoFormOpen(false)}
          summary={targetingSummary}
          isSummaryReadOnly={false}
        />
      )}
      {isComingSoon && (
        <ComingSoon Open={isComingSoon} onClose={CloseComingSoon}/>
      )}
    </>
  );
};

export default Sidebar;