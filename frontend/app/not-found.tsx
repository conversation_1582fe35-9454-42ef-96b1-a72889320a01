import Link from 'next/link';
import { CircleIcon } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="flex items-center justify-center min-h-[100dvh]">
      <div className="max-w-md space-y-8 p-4 text-center">
        <div className="flex justify-center">
          {/*<CircleIcon className="size-12 text-orange-500" />*/}
          {/*<img className="h-6 w-auto object-contain" src="/Fylow-copy4.png" alt="Logo"/>*/}
          <h1 className="text-8xl font-semibold font-sans text-cyan-700 ">
          404
        </h1>
        </div>
        <h1 className="text-2xl  font-sans text-gray-900 tracking-tight">
     Ops! Page Not Found
        </h1>
        <p className="text-base text-gray-500 font-sans">
          The page you are looking for might have been removed, had its name
          changed, or is temporarily unavailable.
        </p>
        <Link
          href="/adTargeting"
          className="max-w-48 mx-auto flex justify-center py-2 px-4 border border-gray-300 rounded-full shadow-sm text-sm font-sans text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-700"
        >
          Back to Home
        </Link>
      </div>
    </div>
  );
}