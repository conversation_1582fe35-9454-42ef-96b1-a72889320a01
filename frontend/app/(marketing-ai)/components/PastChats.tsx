"use-client"
import Link from 'next/link';
import { useState, useEffect, useCallback } from 'react';
import {<PERSON>, CardHeader, CardFooter, CardTitle, CardDescription, CardContent} from "@/components/ui/card"
import {Button} from "@/components/ui/button"
import {Divider} from "@/components/ui/Divider"
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from 'next/navigation';
import { Trash2 } from 'lucide-react';
import DeleteaChat from '@/components/ui/DeleteChat';
import { useSearchParams } from 'next/navigation';


const PastChats = ()=> {

    const router = useRouter();
    const [isError, setError] = useState<string | null>(null);
    const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
    const [isLoading, setisLoading] = useState(true);
    const [chats, setChats] = useState<any[]>([]);
    const searchParams = useSearchParams();
    const chatId = searchParams.get('chatId');
    //state for delete dialog
      const [isDelete, setIsDelete] = useState(false);
    //for fetching user's chats
    const fetchUserChats = async () =>{
      //setisLoading(true);
      try {
        const response = await fetch('/api/marketing-ai/chats/', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
  
        if (!response.ok) {
          throw new Error('Failed to fetch chats');
        }
  
        const result = await response.json();
        setChats(result);



      } catch (error) {
        console.error('Error fetching the chats:', error);
        setError(error instanceof Error ? error.message : 'Fetching the chats Failed, please try again.');
      } finally {
        setisLoading(false);
      };
    }
    

  

  const fetchChatMessages = async (chatId: string) =>{
    try {
      const response = await fetch(`/api/marketing-ai/chat-messages?chatId=${chatId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result = await response.json();
      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }
      router.push(`/marketing-ai?chatId=${chatId}`);



    } catch (error) {
      console.error('Error fetching the conversation:', error);
      setError(error instanceof Error ? error.message : 'Fetching the messages failed, please try again.');
    } finally {
      isLoading;
    };
  }
    // open delete pop up
  const DeletePopUp = (chatId: string)=>{
    setSelectedChatId(chatId);
    setIsDelete(true);
  }
  //close the delete pop up
  const CloseDelete = ()=>{
    setIsDelete(false);
  }
      // Format date
  const formatDate = (date:string| Date) => {
    return new Date(date).toISOString().split('T')[0];
  };
  //for fetching chats  
  useEffect(() => {
    try{
    fetchUserChats();
  }finally{
   !isLoading;
  }
  }, []);
  
    return(
      <div className="w-full max-w-2xl flex flex-col h-[calc(100vh-130px)] bg-white p-4 rounded-md shadow-md mx-auto">
      <div className="overflow-y-auto flex-grow items-center justify-between">
      <div className="bg-gray-100 rounded-lg p-2 h-24 mb-4">
        <h2 className="text-xl font-sans text-gray-800 mb-4 text-center mt-6">
          Your AI Chats
        </h2>
      </div>

      {isLoading && (
        <div className="text-center py-8">
          <p className="text-gray-600">Loading...</p>
        </div>
      )}

      {isError && (
        <div className="text-center py-8">
          <p className="text-red-500">{isError}</p>
        </div>
      )}

      {!isLoading && !isError && chats.length === 0 && (
        <div className="text-center font-sans py-8">
          <p className="text-gray-600">No conversations available</p>
        </div>
      )}
      <div className="space-y-3">
        {!isLoading && chats.map((chat) => (
          <div className='bg-gray-50 rounded-lg shadow-sm py-4 hover:shadow-md transition-shadow cursor-pointer'>
          <div 
            key={chat.id}
            className="flex flex-col"
            onClick={() => fetchChatMessages(chat.id)}
          >{/*chats */}
            <div className="flex justify-between items-end px-3">
                <h3 className="font-medium text-gray-800">{chat.title}</h3>
                <div className='flex flex-col space-y-1'>
              <div className="text-xs text-gray-400 flex flex-row space-x-1">{formatDate(chat.createdAt)}</div>
              </div></div>
              <div className="w-full flex flex-col">
</div></div>
              <div className='px-3'>
                          <div className="flex flex-row mt-1 justify-end items-end">
                          <Trash2  onClick={() => DeletePopUp(chat.id)}  className="h-4 w-4 stroke-red-700 hover:stroke-red-800  cursor-pointer mt-2" />
                            </div></div> 
          </div>
        ))}
      </div>
      </div>
      {isDelete && selectedChatId && (
      <DeleteaChat Open={isDelete} onClose={CloseDelete} chatId={selectedChatId} onDeleted={()=>{ setIsDelete(false); fetchUserChats();}}/>
    )} 
    </div>  
    );
};

export default PastChats;