"use client";
import { NextResponse } from 'next/server';
import { useState, useRef, useEffect } from "react";
import { useSearchParams } from 'next/navigation';
import { useRouter } from "next/navigation";
import { getUser } from '@/lib/db/queries';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {Tooltip,TooltipContent,TooltipProvider,TooltipTrigger} from "@/components/ui/tooltip";
import {Input} from "@/components/ui/input"
import { ChevronRight, Loader2 } from "lucide-react";
import {Upload,Info,ChevronDown,Menu, X, User, Send, FileText, AlertCircle} from "lucide-react";
import ReactMarkdown from "react-markdown";

interface ChatLogEntry {
  type: "user" | "bot" | "error";
  text: string;
}

interface ApiMessage {
  role: "user" | "assistant";
  content: string;
}

  const MarketingAiAgent = () => {
  const [audience, setAudience] = useState("");
  const [showAlert, setShowAlert] = useState(false);
  const [isVideoFormOpen, setIsVideoFormOpen] = useState(false);
  const [targetingSummary, setTargetingSummary] = useState<string>("");
  const [showPopup, setShowPopup] = useState(false);
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const [isMenuOpen,setMenuOpen]=useState(false)
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [userInput, setUserInput] = useState('');
  const [conversationId, setConversationId] = useState('');
  const [loading, setLoading] = useState(false);
  const [chatLog, setChatLog] = useState<ChatLogEntry[]>([]);
  const [titleExists, setTitleExists] = useState(false);
  const [chatTitle, setChatTitle] = useState('');
  const [userId, setUserId] = useState('');
  const searchParams = useSearchParams();
  const chatId = searchParams.get('chatId');
  const [messages, setMessages] = useState<ApiMessage[]>([]);

  // add file upload related states
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string>('');
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUserInput(event.target.value);
  };

  // file validation function
  const validateFile = (file: File): string => {
    const allowedTypes = ['text/csv', 'application/json', '.csv', '.json'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (file.size > maxSize) {
      return 'File size must be less than 10MB';
    }

    const fileExtension = file.name.toLowerCase().split('.').pop();
    if (!fileExtension || !['csv', 'json'].includes(fileExtension)) {
      return 'Only CSV and JSON files are supported';
    }

    return '';
  };

  // handle file selection
  const handleFileSelect = (file: File) => {
    const error = validateFile(file);
    if (error) {
      setFileError(error);
      setSelectedFile(null);
    } else {
      setFileError('');
      setSelectedFile(file);
    }
  };

  // handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // remove file
  const handleRemoveFile = () => {
    setSelectedFile(null);
    setFileError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
    
  
    
    useEffect(() => {
      const fetchMessages = async () => {
        if (!chatId) {
          setChatLog([]);
          setChatTitle('');
          setTitleExists(false);
          return;
        }
        else{
          setConversationId(chatId);
        }
  
        try {
          //fetch the conversation messages 
          const res = await fetch(`/api/marketing-ai/chat-messages?chatId=${chatId}`);
          if (!res.ok) throw new Error('Failed to load messages');
          const data = await res.json();
          setMessages(data);
          const formattedLog = data.map((msg: { role: string; content: string }) => ({
            type: msg.role === 'user' ? 'user' : 'bot',
            text: msg.content,
          }));
          
          setChatLog(formattedLog);
        } catch (err) {
          console.error('Fetch error:', err);
        }
      };
  
      fetchMessages();
    }, [chatId]);


    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      if (!userInput.trim()) return;

      // validate if file is selected
      if (!selectedFile) {
        setFileError('Please select a file to upload');
        return;
      }
    
      setLoading(true);
      const currentUserInput = userInput; // save current input
      setUserInput(''); 
    
      try {
        let convId = conversationId;
        let title = null;
           //fetches the api to create and save the new chat but with a null title at first
           if (!convId) {
            const res = await fetch('/api/marketing-ai/new-chat', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
            });
      
            if (!res.ok) throw new Error("Failed to create chat");
      
            const chat = await res.json();
            convId = chat.id;
            setConversationId(convId);
          }
        //fetches the api to save the new user message
        await fetch('/api/marketing-ai/store-message', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            role: 'user',
            content: currentUserInput,
            conversationId: convId
          })
        });

        // create FormData object to support file upload
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('question', currentUserInput);

        //fetches the backend to generate the agent's responses and title
        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL_BASE}/marketing-ai/chat`, {
          method: 'POST',
          credentials: "include",
          body: formData, // use FormData instead of JSON
        });
    
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    
        const data = await response.json();
        const botReply = data.response;
        const conversationTitle = data.conversation_title;
        //fetches the api to save the bot response
        await fetch('/api/marketing-ai/store-message', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            role: 'assistant',
            content: botReply,
            conversationId: convId
          })
        });
    
       //fetch the api to update the chat record in the aiChats table to replace the initial null title with the generated one
        if (conversationTitle) {
          const res = await fetch('/api/marketing-ai/chat-title', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              conversationId: convId,
              generatedTitle: conversationTitle
            })
          });
    
          if (!res.ok) throw new Error("Failed to add the title");
    
          const chat = await res.json();
          title = chat.title;
          setChatTitle(title);
          console.log("Chat title:", chatTitle)
          setTitleExists(true);
        }
        setChatLog(prev => [...prev, { type: 'user', text: currentUserInput }, { type: 'bot', text: botReply }]);
        if (botReply === ""){
          setChatLog(prev => [...prev, { type: 'bot', text: 'Something went wrong.' }]);
        }

    
      } catch (err) {
        console.error('Error:', err);
        setChatLog(prev => [...prev, { type: 'error', text: 'Something went wrong.' }]);
      } finally {
        setLoading(false);
      }
    };
    

  return (
    <>
 <div className="w-full max-w-2xl flex flex-col mx-auto  h-[calc(100vh-8rem)] bg-white p-6 rounded-md shadow-md">
 <div className=" items-center justify-between space-y-4">
      {/*Chat here */}
      <h2 className="sm:text-sm md:text-xl font-bold font-sans text-center mb-2">
          Marketing AI Agent Chat
        </h2>
        {/*Conversation Title */}
        { chatTitle ? (<h3 className="sm:text-xs md:text-lg font-sans text-center mb-4"> {chatTitle} </h3>):(<div></div>)}
        
        {/* file upload area */}
        <div className="mb-4">
          <div
            className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
              isDragOver
                ? 'border-cyan-500 bg-cyan-50'
                : selectedFile
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 bg-gray-50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-medium text-green-700">
                    {selectedFile.name}
                  </span>
                  <span className="text-xs text-gray-500">
                    ({(selectedFile.size / 1024).toFixed(1)} KB)
                  </span>
                </div>
                <button
                  type="button"
                  onClick={handleRemoveFile}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <div>
                <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600 mb-2">
                  Drag & drop a CSV or JSON file here, or click to select
                </p>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 text-sm"
                >
                  Choose File
                </button>
              </div>
            )}
          </div>
          
          {fileError && (
            <div className="flex items-center space-x-2 mt-2 text-red-600">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">{fileError}</span>
            </div>
          )}
          
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv,.json"
            onChange={handleFileInputChange}
            className="hidden"
          />
        </div>

        {/*messages area */}
        <div className="flex-grow min-w-full bg-gray-100  rounded-sm mb-4 overflow-hidden p-2">
              <div className={`overflow-y-auto ${chatTitle ? 'h-[calc(100vh-430px)]' : 'h-[calc(100vh-390px)]'} flex-grow items-center space-y-3`}>
              { chatLog.length === 0 && !loading && !chatId ?(<p className="font-sans text-gray-700 md:text-sm text-left ml-2">Upload a file and start chatting with our AI agent...</p>): chatLog.length === 0 && !loading && chatId? (<div className="h-4 w-4 border-2 border-t-cyan-700 border-gray-200 rounded-full animate-spin mx-auto"></div>): (<p> </p>)}
                {chatLog.map((message, index) => (
                    <div key={index} className={`flex w-full mb-2 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                      { message.type === 'user'?(
                        <div className='rounded-md text-cyan-700 bg-cyan-50  items-start font-sans text-sm p-2 max-w-[75%] w-fit break-words self-end overflow-hidden whitespace-pre-line'>
                        {message.text}
                        </div>)
                        : (
                          <div className='rounded-md text-black bg-white  items-end font-sans text-sm py-2 px-4 max-w-[75%] w-fit  self-start overflow-hidden whitespace-normal '>
                          <div className='prose prose-sm max-w-none  list-disc space-y-4' >
                          <ReactMarkdown  components={{ ul: ({ children }) => <ul className="list-disc pl-5 mb-4">{children}</ul>, ol: ({ children }) => <ol className="list-decimal pl-5 mb-4">{children}</ol>, li: ({ children }) => <li className="mb-1">{children}</li>,}}>{message.text}</ReactMarkdown>
                          </div>
                          </div>
                        )}
                    </div>
                ))}
                {loading && <div className="h-4 w-4 border-2 border-t-cyan-700 border-gray-200 rounded-full animate-spin mx-auto"></div>}
                </div>
                </div>
           
         <form className="w-full flex flex-row items-center" onSubmit={handleSubmit}>
          <Input
            value={userInput}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="Type a message about your uploaded file..."
          />
          <button 
            type="submit" 
            disabled={loading || !selectedFile || !userInput.trim()}
            className={`ml-2 p-2 rounded-md ${
              loading || !selectedFile || !userInput.trim()
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-cyan-700 hover:bg-cyan-800'
            }`}
          >
          <Send className="w-5 h-6 stroke-white"/>
          </button>
          </form>
</div>
</div>     
          </>
          );
          };
export default MarketingAiAgent;