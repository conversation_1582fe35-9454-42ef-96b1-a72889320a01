//import React, { useState} from "react";
import { checkoutAction } from '@/lib/payments/actions';
import { Check,Info, Pin } from 'lucide-react';
import { getStripePrices, getStripeProducts } from '@/lib/payments/stripe';
import  SubmitButton  from './submit-button';
import {planCredits} from '@/lib/db/schema'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Prices are fresh for one hour max
export const revalidate = 3600;

export default async function PricingPage() {
  const [prices, products] = await Promise.all([
    getStripePrices(),
    getStripeProducts(),
  ]);
  const basicPlan = products.find((product) => product.name === 'Basic');
  const standardPlan = products.find((product) => product.name === 'Standard');
  const proPlan = products.find((product) => product.name === 'Pro');
  const enterprisePlan = products.find((product) => product.name === 'Enterprise');
  //console.log('Enterprise plan:',enterprisePlan)
  const basicPrice = prices.find((price) => price.productId === basicPlan?.id);
  //console.log('basic price:',basicPrice);
  const standardPrice = prices.find((price) => price.productId === standardPlan?.id);
  const proPrice = prices.find((price) => price.productId === proPlan?.id);
  const enterprisePrice = prices.find((price) => price.productId === enterprisePlan?.id);
  //voice cloning tooltip text (basic plan)
  const VoiceTooltipText = () => (
    <div className="max-w-[250px] text-left text-gray-700 font-sans text-[12px] whitespace-normal">
      <p>Add any voice to your campaign — your own, your client's, or an impression. Just upload or record a short sample.</p>
    </div>
  );
  //watermark tooltip text (basic plan)
  const WaterMarkTooltipText = () => (
    <div className="max-w-[250px] text-left text-gray-700 font-sans text-[12px]">
      <p>Watermark visible on Free Trial videos to protect content and promote attribution. All paid plans render videos clean, with no watermark.</p>
    </div>
  );
  return (
    <main className="max-w-7xl mx-auto sm:px-6 py-12">
      <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4 justify-between mx-auto bg-gray-50">
        {/*Basic plan */}
        <PricingCard
          name={basicPlan?.name || 'Basic'}
          price={basicPrice?.unitAmount || 1990}
          interval={basicPrice?.interval || 'month'}
          //trialDays={basicPrice?.trialPeriodDays || 14}
          features={[
            <>
            <ul className='space-y-4 mb-2 '>
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />⏱️ Campaign Time: 120 seconds </li>              
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" /><div className='flex flex-row whitespace-nowrap'>👤 Best for: Solo creators & testing</div></li>
            <li className='flex flex-row'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" /><div className='flex flex-row whitespace-nowrap'>🔊 Voice Cloning: ✅ Included
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                    <Info className="h-3 w-3 mt-2 ml-1 text-gray-500 hover:text-gray-700 cursor-pointer"/>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="w-fit break-words bg-white rounded-md shadow-lg">
                      <VoiceTooltipText/>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider></div> 
              </li>
            <li className='flex flex-row'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />💧 Watermark: ❌ None
            <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                    <Info className="h-3 w-3 mt-2 ml-1.5 text-gray-500 hover:text-gray-700 cursor-pointer"/>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="bg-white rounded-md shadow-lg">
                      <WaterMarkTooltipText/>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider></li>
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />Email support</li>
      </ul>
      </>
          ]}
          priceId={basicPrice?.id}
          credits={planCredits['Basic']}
        />
       
        {/*Stnadard plan */}
        <PricingCard
          name={standardPlan?.name || 'Standard'}
          price={standardPrice?.unitAmount || 7500}
          interval={standardPrice?.interval || 'month'}
          //trialDays={standardPrice?.trialPeriodDays || 14}
          features={[
            <>
            <ul className='space-y-4'>
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />⏱️ Campaign Time: 506 seconds </li>              
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />👤 Great for: Scaling brands & teams</li>
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />Everything in Basic, and:</li>
            <li className='flex flex-row items-start ml-7 text-sm'><Pin className="h-3 w-3  mt-1.5 stroke-cyan-700 mr-2  flex-shrink-0"/>Early Access to New Features</li>
            <li className='flex flex-row items-start ml-7 text-sm'><Pin className="h-3 w-3 mt-1.5 stroke-cyan-700 mr-2  flex-shrink-0"/>24/7 Support</li>
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />🌟 Most Popular Plan!</li>
      </ul>
      </>
          ]}
          priceId={standardPrice?.id}
          credits={planCredits['Standard']}
        />
        {/*Pro plan */}
        <PricingCard
          name={proPlan?.name || 'Pro'}
          price={proPrice?.unitAmount || 16900}
          interval={proPrice?.interval || 'month'}
          //trialDays={proPrice?.trialPeriodDays || 14}
          features={[
            <>
             <>
            <ul className='space-y-4 lg:mb-[50px] md:mb-[100px] '>
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />⏱️ Campaign Time: 1,249 seconds</li>              
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />👤 Built for: Agencies & high-volume</li>
            <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />Everything in Pro, and:</li>
            <li className='flex flex-row items-start ml-7 text-sm'><Pin className="h-3 w-3  mt-1.5 stroke-cyan-700 mr-2  flex-shrink-0"/>&#128184; Best Value per Second</li>
      </ul>
      </>
            </>
          ]}
          priceId={proPrice?.id}
          credits={planCredits['Pro']}

        />
        {/*Enterprise plan */}
         <PricingCard
          name={enterprisePlan?.name || 'Enterprise'}
          price={enterprisePrice?.unitAmount || 0}
          interval={enterprisePrice?.interval || 'month'}
          //trialDays={enterprisePrice?.trialPeriodDays || 14}
          features={[
            <>
            <>
           <ul className='space-y-4 mb-[50px] md:mb-[50px]'>
           <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />Pay per usage</li>              
           <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />Unlimited Workspace Members</li>
           <li className='flex flex-row items-start'><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" />24/7 Support</li>
     </ul>
     </>
           </>

          ]}
          priceId={enterprisePrice?.id}
        />
      </div>
    </main>
  );
}

function PricingCard({
  name,
  price,
  interval,
  //trialDays,
  features,
  priceId,
  credits,
}: {
  name: string;
  price: number;
  interval: string;
  //trialDays: number;
  //features: string[];
  features: React.ReactNode[];
  priceId?: string;
  credits?: number;
})
 { console.log("PricingCard priceId:", priceId);
  return (
    <div className=" bg-white shadow-md rounded-md p-2 ">
      <h2 className="text-2xl font-medium text-gray-900 mb-2 ">{name}</h2>
      <p className="text-sm text-gray-600 mb-4 font-sans ">
        with {planCredits['freeTrial']} credits free trial
      </p>
      {price && price > 0 ? (
  <p className="text-4xl font-medium text-gray-900 mb-6  ">
    ${price / 100}{' '}
    <span className="text-xl font-normal text-gray-600">
      per user / {interval}
    </span>
  </p>
) : (
  <p></p>
)}
     
      {price && price > 0 ? (
        <ul className="flex flex-col space-y-4 mb-8 min-h-[310px]">
        { credits !== undefined && (<li className="flex items-start"><Check className="h-5 w-5 stroke-cyan-700 mr-2 mt-0.5 flex-shrink-0" /><span className="text-gray-700 font-sans">🎯{credits.toLocaleString()} credits</span></li>)}
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <span className="text-gray-700 font-sans">{feature}</span>
          </li>
        ))}
      </ul>
      ):(
        <ul className="flex flex-col space-y-4 mt-20 min-h-[340px]">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <span className="text-gray-700 font-sans">{feature}</span>
          </li>
        ))}
      </ul>)}
      <div className="flex-grow"></div>
{/*Contact sales or Get Started button depending on the plan */}
      {price > 0 ? (
        <form action={checkoutAction} >
        <input type="hidden" name="priceId" value={priceId} />
        <SubmitButton price={price} />
      </form>
      ):(
      <form action={checkoutAction}>
        <input type="hidden" name="priceId" value={priceId} />
        <SubmitButton price={price}/>
      </form>)}
    </div>
  );
}