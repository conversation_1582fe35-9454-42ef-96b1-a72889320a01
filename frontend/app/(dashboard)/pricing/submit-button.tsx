'use client';

import { Button } from '@/components/ui/button';
import { ArrowRight, Loader2 } from 'lucide-react';
import { useFormStatus } from 'react-dom';

interface submitProps{
  price:number
}
const SubmitButton: React.FC<submitProps> = ({price})=> {
  const { pending } = useFormStatus();

  return (
    <Button
      type="submit"
      disabled={pending}
      className="w-full bg-cyan-700 hover:cursor-pointer hover:bg-cyan-800 text-white border rounded-full flex items-center justify-center font-sans"
    >
      {pending ? (
        <>
          <Loader2 className="animate-spin mr-2 h-4 w-4" />
          Loading...
        </>
      ) : !pending && price >0? (
        <>
          Get Started
          <ArrowRight className="ml-2 h-4 w-4" />
        </>
      ):(
        <>
          Contact Sales
          <ArrowRight className="ml-2 h-4 w-4" />
        </>
      )}
    </Button>
  );
}
export default SubmitButton;