import { redirect } from 'next/navigation';
import { getUser } from '@/lib/db/queries';
import { CreditsCard } from './components/CreditsCard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { customerPortalAction } from '@/lib/payments/actions';

export default async function OverviewPage() {
  // Get authenticated user
  const user = await getUser();

  if (!user) {
    redirect('/sign-in');
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">Overview</h1>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Credits summary card */}
        <div className="md:col-span-1">
          <CreditsCard />
        </div>
        
        {/* Welcome card */}
        <div className="md:col-span-1 lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Welcome, {user.name || 'User'}</CardTitle>
              <CardDescription>
                Your content creation platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Create engaging ads and video content using AI technology. Monitor your credits and usage from this overview.
              </p>
              
              <div className="grid gap-2 md:grid-cols-2">
                <Card className="bg-cyan-50 border border-cyan-200">
                  <CardContent className="p-4">
                    <h3 className="font-medium mb-1">Create Video</h3>
                    <p className="text-xs text-gray-600">
                      Create professional video campaigns
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="bg-cyan-50 border border-cyan-200">
                  <CardContent className="p-4">
                    <h3 className="font-medium mb-1">Audience Insights</h3>
                    <p className="text-xs text-gray-600">
                      Generate audience insights for your brand
                    </p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* User information section - combined from settings */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* User Subscription Card */}
        <Card>
          <CardHeader>
            <CardTitle>User Subscription</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <div className="mb-4 sm:mb-0">
                  <p className="font-medium">
                    Current Plan: {user.planName || 'Free'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {user.subscriptionStatus === 'active'
                      ? 'Billed monthly'
                      : user.subscriptionStatus === 'trialing'
                        ? 'Trial period'
                        : 'No active subscription'}
                  </p>
                </div>
                <form action={customerPortalAction}>
                  <Button type="submit" variant="outline">
                    Manage Subscription
                  </Button>
                </form>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Profile Card */}
        <Card>
          <CardHeader>
            <CardTitle>User Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-6">
              <Avatar>
                <AvatarImage
                  src={user.avatar || `/placeholder.svg?height=32&width=32`}
                  alt={user.name || user.email}
                />
                <AvatarFallback>
                  {(user.name || user.email || 'User')
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{user.name || 'No Name'}</p>
                <p className="text-sm text-muted-foreground">{user.email}</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium mb-1">Email</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium mb-1">Role</p>
                  <p className="text-sm text-muted-foreground capitalize">{user.role}</p>
                </div>
                <div>
                  <p className="text-sm font-medium mb-1">Member Since</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(user.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium mb-1">Subscription Status</p>
                  <p className="text-sm text-muted-foreground capitalize">
                    {user.subscriptionStatus || 'Not subscribed'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Activity section - placeholder for future implementation */}
      {/* This section can be used to display recent activity or projects */}
    </div>
  );
}
