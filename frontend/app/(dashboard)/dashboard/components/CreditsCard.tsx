'use client';

import { useState, useEffect } from 'react';
import { getUserCredits } from '@/lib/api-client/credit-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, RefreshCw, AlertCircle } from 'lucide-react';

// Interface for credit balance response
interface CreditBalance {
  credits: number;
  planName: string;
  history: Array<{
    id: number;
    feature: string;
    count: number;
    createdAt: string;
    updatedAt: string;
  }>;
  featureUsage: Array<{
    feature: string;
    totalUsage: number;
  }>;
  subscriptionStatus: string;
}

/**
 * A component to display user's credit information in the dashboard
 * Shows current balance, plan info, and recent usage
 */
export function CreditsCard() {
  // State for credit data and UI states
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch credit data on component mount
  useEffect(() => {
    fetchCreditBalance();
  }, []);

  // Function to get user's credit information
  const fetchCreditBalance = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getUserCredits();
      setCreditBalance(data);
    } catch (err) {
      console.error('Failed to load credit balance:', err);
      setError('Unable to load credit information');
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh credit data
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchCreditBalance();
    setRefreshing(false);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Credits</CardTitle>
            <CardDescription>Your current credits and usage</CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={loading || refreshing}
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span className="sr-only">Refresh</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-6">
            <Loader2 className="h-5 w-5 animate-spin mr-2" />
            <span>Loading credit information...</span>
          </div>
        ) : error ? (
          <div className="flex items-center text-red-500 py-4">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
        ) : creditBalance ? (
          <div className="space-y-4">
            {/* Credit Balance */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-500">Available Credits:</span>
              <span className="text-2xl font-bold">{creditBalance.credits.toLocaleString()}</span>
            </div>
            
            {/* Subscription Info */}
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-500">Current Plan:</span>
                <span className="ml-2 font-medium">{creditBalance.planName || 'Free'}</span>
              </div>
              <div>
                <span className="text-gray-500">Status:</span>
                <span className="ml-2 font-medium capitalize">{creditBalance.subscriptionStatus || 'inactive'}</span>
              </div>
            </div>
            
            {/* Recent Usage History - if available */}
            {creditBalance.history && creditBalance.history.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Recent Activity</h4>
                <div className="max-h-32 overflow-y-auto">
                  <table className="w-full text-xs">
                    <thead className="text-gray-500">
                      <tr>
                        <th className="text-left pb-1">Date</th>
                        <th className="text-left pb-1">Feature</th>
                        <th className="text-right pb-1">Credits</th>
                      </tr>
                    </thead>
                    <tbody>
                      {creditBalance.history.slice(0, 5).map((item) => (
                        <tr key={item.id} className="border-t border-gray-100">
                          <td className="py-1">{formatDate(item.createdAt)}</td>
                          <td className="py-1">{item.feature.replace('_', ' ')}</td>
                          <td className="text-right py-1">-{item.count}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500">No credit information available</div>
        )}
      </CardContent>
      <CardFooter className="text-xs text-gray-500">
        Credits are consumed when you use AI features such as generating scripts or videos.
      </CardFooter>
    </Card>
  );
} 