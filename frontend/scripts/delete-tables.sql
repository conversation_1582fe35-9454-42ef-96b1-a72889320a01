-- Disable foreign key constraint checks
SET session_replication_role = 'replica';

-- Drop all tables
DROP TABLE IF EXISTS activity_logs CASCADE;
DROP TABLE IF EXISTS invitations CASCADE;
DROP TABLE IF EXISTS team_members CASCADE;
DROP TABLE IF EXISTS teams CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS ad_campaigns CASCADE;
DROP TABLE IF EXISTS ad_history CASCADE;
DROP TABLE IF EXISTS __drizzle_migrations CASCADE;

-- Enable foreign key constraint checks
SET session_replication_role = 'origin';

-- Confirm
SELECT 'Database tables have been dropped.' AS result;