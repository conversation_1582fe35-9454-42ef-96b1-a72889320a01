import { db } from '@/lib/db/drizzle';
import { migrateTeamSubscriptionsToUsers } from '@/lib/db/queries';

/**
 * Migration Script: Migrate team subscription data to user records
 * 
 * This script performs the following operations:
 * 1. Find all teams and their members
 * 2. For each team, locate the owner (owner role)
 * 3. Copy the team's Stripe subscription information to the owner's user record
 * 
 * Usage:
 * pnpm tsx scripts/migrate-team-to-user.ts
 */
async function main() {
  console.log('Starting migration of team subscription data to users...');
  
  try {
    await migrateTeamSubscriptionsToUsers();
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Error occurred during migration:', error);
    process.exit(1);
  }
  
  process.exit(0);
}

main();
