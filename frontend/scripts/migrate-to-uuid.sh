#!/bin/bash

# UUID Migration Script
# This script safely migrates the database from serial IDs to UUIDs
# It creates a backup before running the migration

set -e  # Exit on any error

echo "🚀 Starting UUID migration process..."

# Set POSTGRES_URL directly
POSTGRES_URL="postgres://postgres:<EMAIL>:5432/postgres?sslmode=require"

echo "📊 Checking database connection..."
if ! psql "$POSTGRES_URL" -c "SELECT 1;" > /dev/null 2>&1; then
    echo "❌ Error: Cannot connect to database"
    echo "Please check your POSTGRES_URL in .env file"
    exit 1
fi

echo "✅ Database connection successful"

echo "⚠️  ATTENTION: This migration will convert all serial IDs to UUIDs"
echo "📁 Backup file: ${BACKUP_FILE}"
echo "🔄 Migration files:"
echo "   - 0022_migrate_to_uuid.sql"
echo "   - 0023_complete_uuid_migration.sql"
echo ""
read -p "Do you want to proceed with the migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled by user"
    exit 1
fi

echo ""
echo "🔄 Running UUID migration part 1..."
psql "$POSTGRES_URL" -f "../lib/db/migrations/0022_migrate_to_uuid.sql"

if [ $? -eq 0 ]; then
    echo "✅ Part 1 completed successfully"
else
    echo "❌ Error: Part 1 failed"
    echo "💡 You can restore from backup: psql $POSTGRES_URL < ${BACKUP_FILE}"
    exit 1
fi

echo "🔄 Running UUID migration part 2..."
psql "$POSTGRES_URL" -f "../lib/db/migrations/0023_complete_uuid_migration.sql"

if [ $? -eq 0 ]; then
    echo "✅ Part 2 completed successfully"
else
    echo "❌ Error: Part 2 failed"
    echo "💡 You can restore from backup: psql $POSTGRES_URL < ${BACKUP_FILE}"
    exit 1
fi

echo ""
echo "🎉 UUID migration completed successfully!"
echo "📊 Running verification checks..."

# Verify that UUIDs are working
echo "🔍 Checking UUID columns..."
UUID_CHECK=$(psql "$POSTGRES_URL" -t -c "
SELECT COUNT(*) FROM information_schema.columns 
WHERE table_schema = 'public' 
AND column_name = 'id' 
AND data_type = 'uuid';
")

echo "Found $UUID_CHECK tables with UUID id columns"

# Check for any remaining serial columns
SERIAL_CHECK=$(psql "$POSTGRES_URL" -t -c "
SELECT COUNT(*) FROM information_schema.columns 
WHERE table_schema = 'public' 
AND column_name = 'id' 
AND data_type = 'integer';
")

if [ "$SERIAL_CHECK" -gt 0 ]; then
    echo "⚠️  Warning: Found $SERIAL_CHECK tables still using integer id columns"
    psql "$POSTGRES_URL" -c "
    SELECT table_name, column_name, data_type 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND column_name = 'id' 
    AND data_type = 'integer';
    "
else
    echo "✅ All id columns successfully converted to UUID"
fi

echo ""
echo "🎯 Migration Summary:"
echo "   ✅ Backup created: ${BACKUP_FILE}"
echo "   ✅ UUID extension enabled"
echo "   ✅ All tables converted to UUID"
echo "   ✅ Foreign key relationships preserved"
echo "   ✅ Data integrity maintained"
echo ""
echo "🚀 Your database is now using UUIDs!"
echo "💡 Keep the backup file for safety: ${BACKUP_FILE}"
echo ""
echo "📋 Next steps:"
echo "   1. Test your application thoroughly"
echo "   2. Run 'npm run db:generate' to sync Drizzle schema"
echo "   3. Update any hardcoded references to serial IDs in your code" 