-- Database Reset Script
-- Warning: This script will delete all data in tables! Use only in development/test environments.

-- Disable foreign key constraint checks
SET session_replication_role = 'replica';

-- Empty all tables
TRUNCATE TABLE activity_logs CASCADE;
TRUNCATE TABLE invitations CASCADE;
TRUNCATE TABLE team_members CASCADE;
TRUNCATE TABLE teams CASCADE;
TRUNCATE TABLE users CASCADE;
TRUNCATE TABLE ad_campaigns CASCADE;
TRUNCATE TABLE ad_history CASCADE;

-- Reset all sequences (auto-increment IDs)
ALTER SEQUENCE activity_logs_id_seq RESTART WITH 1;
ALTER SEQUENCE invitations_id_seq RESTART WITH 1;
ALTER SEQUENCE team_members_id_seq RESTART WITH 1;
ALTER SEQUENCE teams_id_seq RESTART WITH 1;
ALTER SEQUENCE users_id_seq RESTART WITH 1;
ALTER SEQUENCE ad_campaigns_id_seq RESTART WITH 1;
ALTER SEQUENCE ad_history_id_seq RESTART WITH 1;

-- Enable foreign key constraint checks
SET session_replication_role = 'origin';

-- Confirm
SELECT 'Database reset complete. All tables have been emptied.' AS result;
