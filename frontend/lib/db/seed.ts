import { stripe } from '../payments/stripe';
import { db } from './drizzle';
import { users, teams, teamMembers } from './schema';
import { hashPassword } from '@/lib/auth/session';

async function createStripeProducts() {
  console.log('Creating Stripe products and prices...');

  const basicProduct = await stripe.products.create({
    name: 'Basic',
    description: 'Basic subscription plan',
  });

  await stripe.prices.create({
    product: basicProduct.id,
    unit_amount: 1990, // $19.90 in    cents
    currency: 'usd',
    recurring: {
      interval: 'month',
      trial_period_days: 14,
    },
  });
  //standard plan
  const standardProduct = await stripe.products.create({
    name: 'Standard',
    description: 'Standard subscription plan',
  });

  await stripe.prices.create({
    product: standardProduct.id,
    unit_amount: 7500, // $75 in cents
    currency: 'usd',
    recurring: {
      interval: 'month',
      trial_period_days: 14,
    },
  });


//Pro plan
const proProduct = await stripe.products.create({
  name: 'Pro',
  description: 'Pro subscription plan',
});

await stripe.prices.create({
  product: proProduct.id,
  unit_amount: 16900, // $169 in cents
  currency: 'usd',
  recurring: {
    interval: 'month',
    trial_period_days: 14,
  },
});

//Enterprise plan
const enterpriseProduct = await stripe.products.create({
  name: 'Enterprise',
  description: 'Enterprise subscription plan',
});

await stripe.prices.create({
  product: enterpriseProduct.id,
  unit_amount:0, // contact sales
  currency: 'usd',
  recurring: {
    interval: 'month',
    trial_period_days: 14,
  },
});

  console.log('Stripe products and prices created successfully.');
}

async function seed() {
  const email = '<EMAIL>';
  const password = 'admin123';
  const passwordHash = await hashPassword(password);

  const [user] = await db
    .insert(users)
    .values([
      {
        email: email,
        passwordHash: passwordHash,
        role: "owner",
      },
    ])
    .returning();

  console.log('Initial user created.');

  const [team] = await db
    .insert(teams)
    .values({
      name: 'Test Team',
    })
    .returning();

  await db.insert(teamMembers).values({
    teamId: team.id,
    userId: user.id,
    role: 'owner',
  });

  await createStripeProducts();
}

seed()
  .catch((error) => {
    console.error('Seed process failed:', error);
    process.exit(1);
  })
  .finally(() => {
    console.log('Seed process finished. Exiting...');
    process.exit(0);
  });
