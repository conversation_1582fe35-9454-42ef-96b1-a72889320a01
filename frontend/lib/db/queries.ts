import { desc, and, eq, isNull, gte, sql, asc } from 'drizzle-orm';
import { db } from './drizzle';
import { activityLogs, teamMembers, teams, users, adCampaigns, adHistory, videoProjects, videoScripts, videoSegments, aiChats, chatMessages } from './schema';
import { cookies } from 'next/headers';
import { verifyToken } from '@/lib/auth/session';

// Flag to configure whether to use user-level subscriptions
// Keep consistent with the flag in stripe.ts
export const USE_USER_LEVEL_SUBSCRIPTIONS = true;

export async function getUser() {
  const sessionCookie = (await cookies()).get('session');
  if (!sessionCookie || !sessionCookie.value) {
    return null;
  }

  const sessionData = await verifyToken(sessionCookie.value);
  if (
    !sessionData ||
    !sessionData.user ||
    typeof sessionData.user.id !== 'string'
  ) {
    return null;
  }

  if (new Date(sessionData.expires) < new Date()) {
    return null;
  }

  const user = await db
    .select()
    .from(users)
    .where(and(eq(users.id, sessionData.user.id), isNull(users.deletedAt)))
    .limit(1);

  if (user.length === 0) {
    return null;
  }

  return user[0];
}

export async function getTeamByStripeCustomerId(customerId: string) {
  const result = await db
    .select()
    .from(teams)
    .where(eq(teams.stripeCustomerId, customerId))
    .limit(1);

  return result.length > 0 ? result[0] : null;
}

// New: Get user by Stripe customer ID
export async function getUserByStripeCustomerId(customerId: string) {
  const result = await db
    .select()
    .from(users)
    .where(eq(users.stripeCustomerId, customerId))
    .limit(1);

  return result.length > 0 ? result[0] : null;
}

export async function updateTeamSubscription(
  teamId: string,
  subscriptionData: {
    stripeSubscriptionId: string | null;
    stripeProductId: string | null;
    planName: string | null;
    subscriptionStatus: string;
    credits: number | null ;
  }
) {
  await db
    .update(teams)
    .set({
      ...subscriptionData,
      updatedAt: new Date(),
    })
    .where(eq(teams.id, teamId));
}

// Update user subscription information
export async function updateUserSubscription(
  userId: string,
  subscriptionData: {
    stripeSubscriptionId: string | null;
    stripeProductId: string | null;
    planName: string | null;
    subscriptionStatus: string;
    credits: number | null;
  }
) {
  await db
    .update(users)
    .set({
      ...subscriptionData,
      updatedAt: new Date(),
    })
    .where(eq(users.id, userId));
}

export async function getUserWithTeam(userId: string) {
  const result = await db
    .select({
      user: users,
      teamId: teamMembers.teamId,
    })
    .from(users)
    .leftJoin(teamMembers, eq(users.id, teamMembers.userId))
    .where(eq(users.id, userId))
    .limit(1);

  return result[0];
}

export async function getActivityLogs() {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Determine query method based on configuration
  if (USE_USER_LEVEL_SUBSCRIPTIONS) {
    // User-level mode: directly query activity logs associated with the user
    return await db
      .select({
        id: activityLogs.id,
        action: activityLogs.action,
        timestamp: activityLogs.timestamp,
        ipAddress: activityLogs.ipAddress,
        userName: users.name,
      })
      .from(activityLogs)
      .leftJoin(users, eq(activityLogs.userId, users.id))
      .where(eq(activityLogs.userId, user.id))
      .orderBy(desc(activityLogs.timestamp))
      .limit(10);
  } else {
    // Team-level mode: query activity logs associated with the user's team
    return await db
      .select({
        id: activityLogs.id,
        action: activityLogs.action,
        timestamp: activityLogs.timestamp,
        ipAddress: activityLogs.ipAddress,
        userName: users.name,
      })
      .from(activityLogs)
      .leftJoin(users, eq(activityLogs.userId, users.id))
      .where(eq(activityLogs.userId, user.id))
      .orderBy(desc(activityLogs.timestamp))
      .limit(10);
  }
}

export async function getTeamForUser(userId: string) {
  const result = await db.query.users.findFirst({
    where: eq(users.id, userId),
    with: {
      teamMembers: {
        with: {
          team: {
            with: {
              teamMembers: {
                with: {
                  user: {
                    columns: {
                      id: true,
                      name: true,
                      email: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  });

  return result?.teamMembers[0]?.team || null;
}

// New: Get current user's subscription information
export async function getCurrentUserSubscription() {
  const user = await getUser();
  if (!user) {
    return null;
  }

  if (USE_USER_LEVEL_SUBSCRIPTIONS) {
    // User-level mode: directly return user's subscription information
    return {
      stripeCustomerId: user.stripeCustomerId,
      stripeSubscriptionId: user.stripeSubscriptionId,
      stripeProductId: user.stripeProductId,
      planName: user.planName,
      subscriptionStatus: user.subscriptionStatus,
      credits: user.credits
    };
  } else {
    // Team-level mode: return user's team subscription information
    const team = await getTeamForUser(user.id);
    if (!team) {
      return null;
    }
    
    return {
      stripeCustomerId: team.stripeCustomerId,
      stripeSubscriptionId: team.stripeSubscriptionId,
      stripeProductId: team.stripeProductId,
      planName: team.planName,
      subscriptionStatus: team.subscriptionStatus,
      credits: team.credits
    };
  }
}

// New: Smart entity retrieval (team or user)
export async function getSmartEntity(user: any) {
  if (!user) {
    return null;
  }

  if (USE_USER_LEVEL_SUBSCRIPTIONS) {
    // User-level mode: return user
    return user;
  } else {
    // Team-level mode: return user's team
    return await getTeamForUser(user.id);
  }
}

// New: Migrate team subscription data to users
export async function migrateTeamSubscriptionsToUsers() {
  // Get all teams and their members
  const teamsWithMembers = await db.query.teams.findMany({
    with: {
      teamMembers: {
        with: {
          user: true,
        },
      },
    },
  });

  for (const team of teamsWithMembers) {
    // Find team owner
    const owner = team.teamMembers.find(member => member.role === 'owner');
    if (owner && owner.user) {
      // Copy team subscription data to owner's user record
      await db
        .update(users)
        .set({
          stripeCustomerId: team.stripeCustomerId,
          stripeSubscriptionId: team.stripeSubscriptionId,
          stripeProductId: team.stripeProductId,
          planName: team.planName,
          subscriptionStatus: team.subscriptionStatus,
          updatedAt: new Date(),
          credits:team.credits
        })
        .where(eq(users.id, owner.user.id));
    }
  }
}

// Ad Campaign Related Queries

// Create new ad campaign
export async function createAdCampaign(description: string, responseData: any) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // Create new ad campaign
  const [campaign] = await db
    .insert(adCampaigns)
    .values({
      userId: user.id,
      description,
      responseData,
      status: 'completed',
    })
    .returning();
  
  // Record history
  await db.insert(adHistory).values({
    campaignId: campaign.id,
    userId: user.id,
    action: 'create',
    data: campaign,
  });
  
  return campaign;
}

export async function getUserAdCampaigns() {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  return await db
    .select()
    .from(adCampaigns)
    .where(eq(adCampaigns.userId, user.id))
    .orderBy(desc(adCampaigns.updatedAt));
}

export async function getAdCampaignById(campaignId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const result = await db
    .select()
    .from(adCampaigns)
    .where(
      and(
        eq(adCampaigns.id, campaignId),
        eq(adCampaigns.userId, user.id)
      )
    )
    .limit(1);

  return result.length > 0 ? result[0] : null;
}

export async function updateAdCampaign(
  campaignId: string,
  updateData: {
    description?: string;
    status?: string;
    responseData?: any;
  }
) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const oldCampaign = await getAdCampaignById(campaignId);
  if (!oldCampaign) {
    throw new Error('Ad campaign not found');
  }

  const result = await db
    .update(adCampaigns)
    .set({
      ...updateData,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(adCampaigns.id, campaignId),
        eq(adCampaigns.userId, user.id)
      )
    )
    .returning();

  if (result.length > 0) {
    await db.insert(adHistory).values({
      campaignId: campaignId,
      userId: user.id,
      action: 'update',
      timestamp: new Date(),
      data: {
        old: oldCampaign,
        new: result[0],
      },
    });
    
    return result[0];
  }
  
  return null;
}

export async function deleteAdCampaign(campaignId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const oldCampaign = await getAdCampaignById(campaignId);
  if (!oldCampaign) {
    throw new Error('Ad campaign not found');
  }

  await db
    .delete(adCampaigns)
    .where(
      and(
        eq(adCampaigns.id, campaignId),
        eq(adCampaigns.userId, user.id)
      )
    );

  await db.insert(adHistory).values({
    campaignId: campaignId,
    userId: user.id,
    action: 'delete',
    timestamp: new Date(),
    data: oldCampaign,
  });
  
  return true;
}

export async function getAdCampaignHistory(campaignId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  return await db
    .select()
    .from(adHistory)
    .where(
      and(
        eq(adHistory.campaignId, campaignId),
        eq(adHistory.userId, user.id)
      )
    )
    .orderBy(desc(adHistory.timestamp));
}

export async function getUserDailyAdCampaignCount(userId: string) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const result = await db
    .select({ count: sql`count(*)` })
    .from(adCampaigns)
    .where(
      and(
        eq(adCampaigns.userId, userId),
        sql`${adCampaigns.createdAt} >= ${today.toISOString()}`
      )
    );
  
  return Number(result[0]?.count || 0);
}




//create a new chat
export async function createAiChat(userId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // add the new chat in the database
  const [chat] = await db
    .insert(aiChats)
    .values({
      userId: user.id,
    })
    .returning();
  
  
  return chat;
}


//update the ai chat with the generated conversation title
export async function updateAiChat(conversationId: string,conversationData: {
  title: string;
}){
  const [chat] =  await db
  .update(aiChats)
  .set({
    ...conversationData,
  })
  .where(eq(aiChats.id, conversationId));
};


//add meassages to the new chat
export const storeNewMessages = async({
  role,
  content,
  conversationId,
}: {
  role: "user" | "assistant";
  content: string;
  conversationId: string;
}) => {
  await db.insert(chatMessages)
  .values({
    role,
    content,
    chatId: conversationId,
    //timeStamp: new Date().toISOString(),
  })
};

//delete an existing chat based on chat id
export async function deleteChat(conversationId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  await db
    .delete(chatMessages)
    .where(eq(chatMessages.chatId, conversationId));


  await db
    .delete(aiChats)
    .where(eq(aiChats.id, conversationId)); 
  return true;
}

//get all user's chats
export async function getUserChats(userId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  return await db
    .select()
    .from(aiChats)
    .where(eq(aiChats.userId, user.id))
    .orderBy(desc(aiChats.createdAt));
}

//get chat messages based on conversation id
export async function getChatMessages(conversationId:string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  return await db
    .select()
    .from(chatMessages)
    .where(eq(chatMessages.chatId, conversationId))
    .orderBy(asc(chatMessages.timeStamp));
}
// Video Project Related Queries

// create video project
export async function createVideoProject(projectData: {
  title: string;
  brandName: string;
  summary?: string;
  description?: string;
  adLength: number;
}) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  const [project] = await db
    .insert(videoProjects)
    .values({
      userId: user.id,
      ...projectData,
      status: 'pending',
    })
    .returning();
  
  return project;
}

// get user video projects
export async function getUserVideoProjects() {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  return await db
    .select()
    .from(videoProjects)
    .where(
      and(
        eq(videoProjects.userId, user.id),
        isNull(videoProjects.deletedAt)
      )
    )
    .orderBy(desc(videoProjects.updatedAt));
}

// get video project by id
export async function getVideoProjectById(projectId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const result = await db
    .select()
    .from(videoProjects)
    .where(
      and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id),
        isNull(videoProjects.deletedAt)
      )
    )
    .limit(1);

  return result.length > 0 ? result[0] : null;
}

// update video project
export async function updateVideoProject(
  projectId: string,
  updateData: {
    title?: string;
    brandName?: string;
    summary?: string;
    description?: string;
    adLength?: number;
    status?: string;
  }
) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const result = await db
    .update(videoProjects)
    .set({
      ...updateData,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id),
        isNull(videoProjects.deletedAt)
      )
    )
    .returning();
  
  return result.length > 0 ? result[0] : null;
}

// soft delete video project
export async function deleteVideoProject(projectId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  await db
    .update(videoProjects)
    .set({
      deletedAt: new Date(),
    })
    .where(
      and(
        eq(videoProjects.id, projectId),
        eq(videoProjects.userId, user.id),
        isNull(videoProjects.deletedAt)
      )
    );
  
  return true;
}

// create video script
export async function createVideoScript(
  projectId: string,
  scriptData: {
    segmentNumber: number;
    scriptText: string;
    narratorText?: string;
    defaultVoice?: string;
    textOverlays?: any;
  }
) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // verify project exists and belongs to current user
  const project = await getVideoProjectById(projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  const [script] = await db
    .insert(videoScripts)
    .values({
      projectId,
      ...scriptData,
      status: 'pending',
    })
    .returning();
  
  return script;
}

// get project scripts
export async function getVideoScriptsByProjectId(projectId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // verify project exists and belongs to current user
  const project = await getVideoProjectById(projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  return await db
    .select()
    .from(videoScripts)
    .where(
      and(
        eq(videoScripts.projectId, projectId),
        eq(videoScripts.isCurrentVersion, true)
      )
    )
    .orderBy(videoScripts.segmentNumber);
}

// update video script
export async function updateVideoScript(
  scriptId: string,
  updateData: {
    scriptText?: string;
    narratorText?: string;
    defaultVoice?: string;
    textOverlays?: any;
    status?: string;
    narratorAudio?: string;
    audioId?: string;
    elevenlabsVoiceIds?: any;
  }
) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // get script info
  const script = await db
    .select()
    .from(videoScripts)
    .where(eq(videoScripts.id, scriptId))
    .limit(1);
  
  if (script.length === 0) {
    throw new Error('Script not found');
  }
  
  // verify project exists and belongs to current user
  const project = await getVideoProjectById(script[0].projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  const result = await db
    .update(videoScripts)
    .set({
      ...updateData,
      updatedAt: new Date(),
    })
    .where(eq(videoScripts.id, scriptId))
    .returning();
  
  return result.length > 0 ? result[0] : null;
}

// create video segment
export async function createVideoSegment(
  scriptId: string,
  segmentData: {
    generationId?: string;
  }
) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // get script info
  const script = await db
    .select()
    .from(videoScripts)
    .where(eq(videoScripts.id, scriptId))
    .limit(1);
  
  if (script.length === 0) {
    throw new Error('Script not found');
  }
  
  // verify project exists and belongs to current user
  const project = await getVideoProjectById(script[0].projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  const [segment] = await db
    .insert(videoSegments)
    .values({
      scriptId,
      ...segmentData,
      status: 'pending',
    })
    .returning();
  
  return segment;
}

// update video segment
export async function updateVideoSegment(
  segmentId: string,
  updateData: {
    videoUrl?: string;
    status?: string;
    completedAt?: Date;
  }
) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // get segment info
  const segment = await db
    .select({
      segment: videoSegments,
      scriptId: videoSegments.scriptId,
      script: videoScripts,
      projectId: videoScripts.projectId,
    })
    .from(videoSegments)
    .leftJoin(videoScripts, eq(videoSegments.scriptId, videoScripts.id))
    .where(eq(videoSegments.id, segmentId))
    .limit(1);
  
  if (segment.length === 0) {
    throw new Error('Segment not found');
  }
  
  // verify project exists and belongs to current user
  // add null check
  if (segment[0].projectId === null) {
    throw new Error('Project ID not found for this segment');
  }
  
  const project = await getVideoProjectById(segment[0].projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  const result = await db
    .update(videoSegments)
    .set(updateData)
    .where(eq(videoSegments.id, segmentId))
    .returning();
  
  return result.length > 0 ? result[0] : null;
}

// get all video segments by script id
export async function getVideoSegmentsByScriptId(scriptId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // get script info
  const script = await db
    .select()
    .from(videoScripts)
    .where(eq(videoScripts.id, scriptId))
    .limit(1);
  
  if (script.length === 0) {
    throw new Error('Script not found');
  }
  
  // verify project exists and belongs to current user
  const project = await getVideoProjectById(script[0].projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  return await db
    .select()
    .from(videoSegments)
    .where(
      and(
        eq(videoSegments.scriptId, scriptId),
        eq(videoSegments.isCurrentVersion, true)
      )
    );
}

// get complete video project data (including scripts and segments)
export async function getCompleteVideoProject(projectId: string) {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // verify project exists and belongs to current user
  const project = await getVideoProjectById(projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  // Get all current version scripts for this project
  const scripts = await db
    .select()
    .from(videoScripts)
    .where(
      and(
        eq(videoScripts.projectId, projectId),
        eq(videoScripts.isCurrentVersion, true)
      )
    )
    .orderBy(videoScripts.segmentNumber);
  
  // Get all segments for these scripts
  const segments = [];
  for (const script of scripts) {
    const scriptSegments = await db
      .select()
      .from(videoSegments)
      .where(
        and(
          eq(videoSegments.scriptId, script.id),
          eq(videoSegments.isCurrentVersion, true)
        )
      );
    segments.push(...scriptSegments);
  }
  
  return {
    project,
    scripts,
    segments
  };
}

/**
 * Cleans up duplicate script entries for a project
 * Ensures only one active version exists per segment
 * 
 * @param projectId - ID of the project to clean up
 * @returns Number of scripts fixed
 */
export async function cleanupDuplicateScripts(projectId: string): Promise<{ fixedCount: number }> {
  const user = await getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // verify project exists and belongs to current user
  const project = await getVideoProjectById(projectId);
  if (!project) {
    throw new Error('Video project not found');
  }
  
  // Get all scripts for this project
  const scripts = await db
    .select()
    .from(videoScripts)
    .where(eq(videoScripts.projectId, projectId));
  
  if (scripts.length === 0) {
    return { fixedCount: 0 };
  }
  
  // Group scripts by segment number
  const scriptsBySegment = new Map<number, typeof videoScripts.$inferSelect[]>();
  
  for (const script of scripts) {
    if (!scriptsBySegment.has(script.segmentNumber)) {
      scriptsBySegment.set(script.segmentNumber, []);
    }
    scriptsBySegment.get(script.segmentNumber)?.push(script);
  }
  
  let fixedCount = 0;
  
  // Fix each segment that has multiple current versions
  await db.transaction(async (tx) => {
    for (const [segmentNumber, segmentScripts] of scriptsBySegment.entries()) {
      // Find scripts marked as current version
      const currentVersionScripts = segmentScripts.filter(s => s.isCurrentVersion);
      
      // If more than one script is marked as current version, fix it
      if (currentVersionScripts.length > 1) {
        console.log(`Found ${currentVersionScripts.length} current versions for segment ${segmentNumber}`);
        
        // Sort by creation date descending to keep the most recent one
        const sortedScripts = [...currentVersionScripts].sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        
        // Keep the first one (most recent) as current
        const keepScript = sortedScripts[0];
        const scriptsToFix = sortedScripts.slice(1);
        
        // Update the others to not be current
        for (const script of scriptsToFix) {
          await tx
            .update(videoScripts)
            .set({ isCurrentVersion: false })
            .where(eq(videoScripts.id, script.id));
          
          fixedCount++;
        }
      }
    }
  });
  
  return { fixedCount };
}
