ALTER TABLE "video_scripts" ADD COLUMN "segment_number" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "video_scripts" ADD COLUMN "version" integer DEFAULT 1 NOT NULL;--> statement-breakpoint
ALTER TABLE "video_scripts" ADD COLUMN "is_current_version" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "video_scripts" ADD COLUMN "script_text" text NOT NULL;--> statement-breakpoint
ALTER TABLE "video_scripts" ADD COLUMN "narrator_text" text;--> statement-breakpoint
ALTER TABLE "video_scripts" ADD COLUMN "narrator_audio" text;--> statement-breakpoint
ALTER TABLE "video_segments" ADD COLUMN "version" integer DEFAULT 1 NOT NULL;--> statement-breakpoint
ALTER TABLE "video_segments" ADD COLUMN "is_current_version" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "video_segments" DROP COLUMN "segment_number";--> statement-breakpoint
ALTER TABLE "video_segments" DROP COLUMN "script";--> statement-breakpoint
ALTER TABLE "video_segments" DROP COLUMN "narrator";--> statement-breakpoint
ALTER TABLE "video_segments" DROP COLUMN "narrator_audio";