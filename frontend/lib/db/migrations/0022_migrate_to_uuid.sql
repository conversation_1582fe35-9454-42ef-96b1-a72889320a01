-- Migration to safely convert serial IDs to UUIDs
-- This migration preserves all existing data and relationships

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 1: Add temporary UUID columns to all tables
ALTER TABLE "users" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "teams" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "team_members" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "activity_logs" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "invitations" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "ad_campaigns" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "ad_history" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "video_projects" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "project_text_overlays" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "video_scripts" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "video_segments" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "audio_resources" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "project_audio_resources" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "usage_tracking" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "verification_tokens" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();
ALTER TABLE "project_display_settings" ADD COLUMN "uuid_id" uuid DEFAULT uuid_generate_v4();

-- Step 2: Populate UUID values for all existing records
UPDATE "users" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "teams" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "team_members" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "activity_logs" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "invitations" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "ad_campaigns" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "ad_history" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "video_projects" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "project_text_overlays" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "video_scripts" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "video_segments" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "audio_resources" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "project_audio_resources" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "usage_tracking" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "verification_tokens" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;
UPDATE "project_display_settings" SET "uuid_id" = uuid_generate_v4() WHERE "uuid_id" IS NULL;

-- Step 3: Add temporary UUID foreign key columns
-- For team_members
ALTER TABLE "team_members" ADD COLUMN "user_uuid_id" uuid;
ALTER TABLE "team_members" ADD COLUMN "team_uuid_id" uuid;

-- For activity_logs
ALTER TABLE "activity_logs" ADD COLUMN "user_uuid_id" uuid;
ALTER TABLE "activity_logs" ADD COLUMN "team_uuid_id" uuid;

-- For invitations
ALTER TABLE "invitations" ADD COLUMN "team_uuid_id" uuid;
ALTER TABLE "invitations" ADD COLUMN "invited_by_uuid_id" uuid;

-- For ad_campaigns
ALTER TABLE "ad_campaigns" ADD COLUMN "user_uuid_id" uuid;

-- For ad_history
ALTER TABLE "ad_history" ADD COLUMN "campaign_uuid_id" uuid;
ALTER TABLE "ad_history" ADD COLUMN "user_uuid_id" uuid;


-- For video_projects
ALTER TABLE "video_projects" ADD COLUMN "user_uuid_id" uuid;
ALTER TABLE "video_projects" ADD COLUMN "team_uuid_id" uuid;

-- For project_text_overlays
ALTER TABLE "project_text_overlays" ADD COLUMN "project_uuid_id" uuid;

-- For video_scripts
ALTER TABLE "video_scripts" ADD COLUMN "project_uuid_id" uuid;

-- For video_segments
ALTER TABLE "video_segments" ADD COLUMN "script_uuid_id" uuid;

-- For audio_resources
ALTER TABLE "audio_resources" ADD COLUMN "user_uuid_id" uuid;

-- For project_audio_resources
ALTER TABLE "project_audio_resources" ADD COLUMN "project_uuid_id" uuid;
ALTER TABLE "project_audio_resources" ADD COLUMN "audio_resource_uuid_id" uuid;

-- For usage_tracking
ALTER TABLE "usage_tracking" ADD COLUMN "user_uuid_id" uuid;

-- For verification_tokens
ALTER TABLE "verification_tokens" ADD COLUMN "user_uuid_id" uuid;

-- For project_display_settings
ALTER TABLE "project_display_settings" ADD COLUMN "project_uuid_id" uuid;

-- Step 4: Populate UUID foreign key relationships
-- team_members
UPDATE "team_members" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "team_members"."user_id" = u."id";
UPDATE "team_members" SET "team_uuid_id" = t."uuid_id" FROM "teams" t WHERE "team_members"."team_id" = t."id";

-- activity_logs
UPDATE "activity_logs" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "activity_logs"."user_id" = u."id";
UPDATE "activity_logs" SET "team_uuid_id" = t."uuid_id" FROM "teams" t WHERE "activity_logs"."team_id" = t."id";

-- invitations
UPDATE "invitations" SET "team_uuid_id" = t."uuid_id" FROM "teams" t WHERE "invitations"."team_id" = t."id";
UPDATE "invitations" SET "invited_by_uuid_id" = u."uuid_id" FROM "users" u WHERE "invitations"."invited_by" = u."id";

-- ad_campaigns
UPDATE "ad_campaigns" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "ad_campaigns"."user_id" = u."id";

-- ad_history
UPDATE "ad_history" SET "campaign_uuid_id" = c."uuid_id" FROM "ad_campaigns" c WHERE "ad_history"."campaign_id" = c."id";
UPDATE "ad_history" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "ad_history"."user_id" = u."id";

-- video_projects
UPDATE "video_projects" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "video_projects"."user_id" = u."id";
UPDATE "video_projects" SET "team_uuid_id" = t."uuid_id" FROM "teams" t WHERE "video_projects"."team_id" = t."id";

-- project_text_overlays
UPDATE "project_text_overlays" SET "project_uuid_id" = p."uuid_id" FROM "video_projects" p WHERE "project_text_overlays"."project_id" = p."id";

-- video_scripts
UPDATE "video_scripts" SET "project_uuid_id" = p."uuid_id" FROM "video_projects" p WHERE "video_scripts"."project_id" = p."id";

-- video_segments
UPDATE "video_segments" SET "script_uuid_id" = s."uuid_id" FROM "video_scripts" s WHERE "video_segments"."script_id" = s."id";

-- audio_resources
UPDATE "audio_resources" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "audio_resources"."user_id" = u."id";

-- project_audio_resources
UPDATE "project_audio_resources" SET "project_uuid_id" = p."uuid_id" FROM "video_projects" p WHERE "project_audio_resources"."project_id" = p."id";
UPDATE "project_audio_resources" SET "audio_resource_uuid_id" = a."uuid_id" FROM "audio_resources" a WHERE "project_audio_resources"."audio_resource_id" = a."id";

-- usage_tracking
UPDATE "usage_tracking" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "usage_tracking"."user_id" = u."id";

-- verification_tokens
UPDATE "verification_tokens" SET "user_uuid_id" = u."uuid_id" FROM "users" u WHERE "verification_tokens"."user_id" = u."id";

-- project_display_settings
UPDATE "project_display_settings" SET "project_uuid_id" = p."uuid_id" FROM "video_projects" p WHERE "project_display_settings"."project_id" = p."id"; 