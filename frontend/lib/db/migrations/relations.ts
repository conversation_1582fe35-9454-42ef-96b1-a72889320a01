import { relations } from "drizzle-orm/relations";
import { teams, activityLogs, users, invitations, teamMembers, adCampaigns, adHistory } from "./schema";

export const activityLogsRelations = relations(activityLogs, ({one}) => ({
	team: one(teams, {
		fields: [activityLogs.teamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [activityLogs.userId],
		references: [users.id]
	}),
}));

export const teamsRelations = relations(teams, ({many}) => ({
	activityLogs: many(activityLogs),
	invitations: many(invitations),
	teamMembers: many(teamMembers),
}));

export const usersRelations = relations(users, ({many}) => ({
	activityLogs: many(activityLogs),
	invitations: many(invitations),
	teamMembers: many(teamMembers),
	adCampaigns: many(adCampaigns),
}));

export const invitationsRelations = relations(invitations, ({one}) => ({
	team: one(teams, {
		fields: [invitations.teamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [invitations.invitedBy],
		references: [users.id]
	}),
}));

export const teamMembersRelations = relations(teamMembers, ({one}) => ({
	user: one(users, {
		fields: [teamMembers.userId],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [teamMembers.teamId],
		references: [teams.id]
	}),
}));

export const adCampaignsRelations = relations(adCampaigns, ({one, many}) => ({
	user: one(users, {
		fields: [adCampaigns.userId],
		references: [users.id]
	}),
	history: many(adHistory),
}));

export const adHistoryRelations = relations(adHistory, ({one}) => ({
	campaign: one(adCampaigns, {
		fields: [adHistory.campaignId],
		references: [adCampaigns.id]
	}),
	user: one(users, {
		fields: [adHistory.userId],
		references: [users.id]
	}),
}));