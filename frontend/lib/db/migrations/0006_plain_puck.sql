CREATE TABLE "project_text_overlays" (
	"id" serial PRIMARY KEY NOT NULL,
	"project_id" integer NOT NULL,
	"text" text NOT NULL,
	"font_size" real NOT NULL,
	"color" varchar(50) NOT NULL,
	"position_x" real NOT NULL,
	"position_y" real NOT NULL,
	"alignment" varchar(10) NOT NULL,
	"start_time" real NOT NULL,
	"duration" real NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "project_text_overlays" ADD CONSTRAINT "project_text_overlays_project_id_video_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."video_projects"("id") ON DELETE no action ON UPDATE no action;