-- Add emailVerified column to users table
ALTER TABLE "users" ADD COLUMN "email_verified" BOOLEAN DEFAULT FALSE;

-- Create verification_tokens table
CREATE TABLE "verification_tokens" (
    "id" SERIAL PRIMARY KEY NOT NULL,
    "token" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,
    "expires_at" TIMESTAMP NOT NULL,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "verification_tokens_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
);

-- Create index on token for faster lookups
CREATE UNIQUE INDEX "verification_tokens_token_unique" ON "verification_tokens"("token"); 