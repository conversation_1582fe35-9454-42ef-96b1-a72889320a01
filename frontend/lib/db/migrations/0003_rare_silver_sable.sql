CREATE TABLE "project_audio_resources" (
	"id" serial PRIMARY KEY NOT NULL,
	"project_id" integer NOT NULL,
	"audio_resource_id" integer NOT NULL
);
--> statement-breakpoint
ALTER TABLE "audio_resources" DROP CONSTRAINT "audio_resources_project_id_video_projects_id_fk";
--> statement-breakpoint
ALTER TABLE "audio_resources" ADD COLUMN "user_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "project_audio_resources" ADD CONSTRAINT "project_audio_resources_project_id_video_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."video_projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project_audio_resources" ADD CONSTRAINT "project_audio_resources_audio_resource_id_audio_resources_id_fk" FOREIGN KEY ("audio_resource_id") REFERENCES "public"."audio_resources"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audio_resources" ADD CONSTRAINT "audio_resources_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "audio_resources" DROP COLUMN "project_id";