-- Part 2: Complete UUID migration by dropping old constraints and columns
-- This completes the migration from serial IDs to UUIDs

-- Step 5: Drop all foreign key constraints that reference serial IDs
ALTER TABLE "activity_logs" DROP CONSTRAINT "activity_logs_team_id_teams_id_fk";
ALTER TABLE "activity_logs" DROP CONSTRAINT "activity_logs_user_id_users_id_fk";
ALTER TABLE "ad_campaigns" DROP CONSTRAINT "ad_campaigns_user_id_users_id_fk";
ALTER TABLE "ad_history" DROP CONSTRAINT "ad_history_campaign_id_ad_campaigns_id_fk";
ALTER TABLE "ad_history" DROP CONSTRAINT "ad_history_user_id_users_id_fk";
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_team_id_teams_id_fk";
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_invited_by_users_id_fk";
ALTER TABLE "team_members" DROP CONSTRAINT "team_members_user_id_users_id_fk";
ALTER TABLE "team_members" DROP CONSTRAINT "team_members_team_id_teams_id_fk";

-- Drop foreign key constraints for video editing tables (if they exist)
-- Note: Some of these might not exist if they were added later
DO $$
BEGIN
    -- video_projects constraints
    BEGIN
        ALTER TABLE "video_projects" DROP CONSTRAINT IF EXISTS "video_projects_user_id_users_id_fk";
        ALTER TABLE "video_projects" DROP CONSTRAINT IF EXISTS "video_projects_team_id_teams_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- project_text_overlays constraints
    BEGIN
        ALTER TABLE "project_text_overlays" DROP CONSTRAINT IF EXISTS "project_text_overlays_project_id_video_projects_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- video_scripts constraints
    BEGIN
        ALTER TABLE "video_scripts" DROP CONSTRAINT IF EXISTS "video_scripts_project_id_video_projects_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- video_segments constraints
    BEGIN
        ALTER TABLE "video_segments" DROP CONSTRAINT IF EXISTS "video_segments_script_id_video_scripts_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- audio_resources constraints
    BEGIN
        ALTER TABLE "audio_resources" DROP CONSTRAINT IF EXISTS "audio_resources_user_id_users_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- project_audio_resources constraints
    BEGIN
        ALTER TABLE "project_audio_resources" DROP CONSTRAINT IF EXISTS "project_audio_resources_project_id_video_projects_id_fk";
        ALTER TABLE "project_audio_resources" DROP CONSTRAINT IF EXISTS "project_audio_resources_audio_resource_id_audio_resources_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- usage_tracking constraints
    BEGIN
        ALTER TABLE "usage_tracking" DROP CONSTRAINT IF EXISTS "usage_tracking_user_id_users_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- verification_tokens constraints
    BEGIN
        ALTER TABLE "verification_tokens" DROP CONSTRAINT IF EXISTS "verification_tokens_user_id_users_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
    -- project_display_settings constraints
    BEGIN
        ALTER TABLE "project_display_settings" DROP CONSTRAINT IF EXISTS "project_display_settings_project_id_video_projects_id_fk";
    EXCEPTION WHEN undefined_object THEN
        NULL;
    END;
    
END $$;

-- Step 6: Drop old serial ID columns and their sequences
ALTER TABLE "users" DROP COLUMN "id";
ALTER TABLE "teams" DROP COLUMN "id";
ALTER TABLE "team_members" DROP COLUMN "id", DROP COLUMN "user_id", DROP COLUMN "team_id";
ALTER TABLE "activity_logs" DROP COLUMN "id", DROP COLUMN "team_id", DROP COLUMN "user_id";
ALTER TABLE "invitations" DROP COLUMN "id", DROP COLUMN "team_id", DROP COLUMN "invited_by";
ALTER TABLE "ad_campaigns" DROP COLUMN "id", DROP COLUMN "user_id";
ALTER TABLE "ad_history" DROP COLUMN "id", DROP COLUMN "campaign_id", DROP COLUMN "user_id";
ALTER TABLE "video_projects" DROP COLUMN "id", DROP COLUMN "user_id", DROP COLUMN "team_id";
ALTER TABLE "project_text_overlays" DROP COLUMN "id", DROP COLUMN "project_id";
ALTER TABLE "video_scripts" DROP COLUMN "id", DROP COLUMN "project_id";
ALTER TABLE "video_segments" DROP COLUMN "id", DROP COLUMN "script_id";
ALTER TABLE "audio_resources" DROP COLUMN "id", DROP COLUMN "user_id";
ALTER TABLE "project_audio_resources" DROP COLUMN "id", DROP COLUMN "project_id", DROP COLUMN "audio_resource_id";
ALTER TABLE "usage_tracking" DROP COLUMN "id", DROP COLUMN "user_id";
ALTER TABLE "verification_tokens" DROP COLUMN "id", DROP COLUMN "user_id";
ALTER TABLE "project_display_settings" DROP COLUMN "id", DROP COLUMN "project_id";

-- Step 7: Rename UUID columns to become the primary ID columns
ALTER TABLE "users" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "teams" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "team_members" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "team_members" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "team_members" RENAME COLUMN "team_uuid_id" TO "team_id";
ALTER TABLE "activity_logs" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "activity_logs" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "activity_logs" RENAME COLUMN "team_uuid_id" TO "team_id";
ALTER TABLE "invitations" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "invitations" RENAME COLUMN "team_uuid_id" TO "team_id";
ALTER TABLE "invitations" RENAME COLUMN "invited_by_uuid_id" TO "invited_by";
ALTER TABLE "ad_campaigns" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "ad_campaigns" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "ad_history" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "ad_history" RENAME COLUMN "campaign_uuid_id" TO "campaign_id";
ALTER TABLE "ad_history" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "video_projects" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "video_projects" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "video_projects" RENAME COLUMN "team_uuid_id" TO "team_id";
ALTER TABLE "project_text_overlays" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "project_text_overlays" RENAME COLUMN "project_uuid_id" TO "project_id";
ALTER TABLE "video_scripts" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "video_scripts" RENAME COLUMN "project_uuid_id" TO "project_id";
ALTER TABLE "video_segments" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "video_segments" RENAME COLUMN "script_uuid_id" TO "script_id";
ALTER TABLE "audio_resources" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "audio_resources" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "project_audio_resources" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "project_audio_resources" RENAME COLUMN "project_uuid_id" TO "project_id";
ALTER TABLE "project_audio_resources" RENAME COLUMN "audio_resource_uuid_id" TO "audio_resource_id";
ALTER TABLE "usage_tracking" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "usage_tracking" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "verification_tokens" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "verification_tokens" RENAME COLUMN "user_uuid_id" TO "user_id";
ALTER TABLE "project_display_settings" RENAME COLUMN "uuid_id" TO "id";
ALTER TABLE "project_display_settings" RENAME COLUMN "project_uuid_id" TO "project_id";

-- Step 8: Add primary key constraints for UUID columns
ALTER TABLE "users" ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");
ALTER TABLE "teams" ADD CONSTRAINT "teams_pkey" PRIMARY KEY ("id");
ALTER TABLE "team_members" ADD CONSTRAINT "team_members_pkey" PRIMARY KEY ("id");
ALTER TABLE "activity_logs" ADD CONSTRAINT "activity_logs_pkey" PRIMARY KEY ("id");
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_pkey" PRIMARY KEY ("id");
ALTER TABLE "ad_campaigns" ADD CONSTRAINT "ad_campaigns_pkey" PRIMARY KEY ("id");
ALTER TABLE "ad_history" ADD CONSTRAINT "ad_history_pkey" PRIMARY KEY ("id");
ALTER TABLE "video_projects" ADD CONSTRAINT "video_projects_pkey" PRIMARY KEY ("id");
ALTER TABLE "project_text_overlays" ADD CONSTRAINT "project_text_overlays_pkey" PRIMARY KEY ("id");
ALTER TABLE "video_scripts" ADD CONSTRAINT "video_scripts_pkey" PRIMARY KEY ("id");
ALTER TABLE "video_segments" ADD CONSTRAINT "video_segments_pkey" PRIMARY KEY ("id");
ALTER TABLE "audio_resources" ADD CONSTRAINT "audio_resources_pkey" PRIMARY KEY ("id");
ALTER TABLE "project_audio_resources" ADD CONSTRAINT "project_audio_resources_pkey" PRIMARY KEY ("id");
ALTER TABLE "usage_tracking" ADD CONSTRAINT "usage_tracking_pkey" PRIMARY KEY ("id");
ALTER TABLE "verification_tokens" ADD CONSTRAINT "verification_tokens_pkey" PRIMARY KEY ("id");
ALTER TABLE "project_display_settings" ADD CONSTRAINT "project_display_settings_pkey" PRIMARY KEY ("id");

-- Step 9: Re-create foreign key constraints with UUID references
ALTER TABLE "activity_logs" ADD CONSTRAINT "activity_logs_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "activity_logs" ADD CONSTRAINT "activity_logs_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "ad_campaigns" ADD CONSTRAINT "ad_campaigns_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "ad_history" ADD CONSTRAINT "ad_history_campaign_id_ad_campaigns_id_fk" FOREIGN KEY ("campaign_id") REFERENCES "public"."ad_campaigns"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "ad_history" ADD CONSTRAINT "ad_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_invited_by_users_id_fk" FOREIGN KEY ("invited_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "team_members" ADD CONSTRAINT "team_members_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "team_members" ADD CONSTRAINT "team_members_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "video_projects" ADD CONSTRAINT "video_projects_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "video_projects" ADD CONSTRAINT "video_projects_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "project_text_overlays" ADD CONSTRAINT "project_text_overlays_project_id_video_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."video_projects"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "video_scripts" ADD CONSTRAINT "video_scripts_project_id_video_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."video_projects"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "video_segments" ADD CONSTRAINT "video_segments_script_id_video_scripts_id_fk" FOREIGN KEY ("script_id") REFERENCES "public"."video_scripts"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "audio_resources" ADD CONSTRAINT "audio_resources_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "project_audio_resources" ADD CONSTRAINT "project_audio_resources_project_id_video_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."video_projects"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "project_audio_resources" ADD CONSTRAINT "project_audio_resources_audio_resource_id_audio_resources_id_fk" FOREIGN KEY ("audio_resource_id") REFERENCES "public"."audio_resources"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "usage_tracking" ADD CONSTRAINT "usage_tracking_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "verification_tokens" ADD CONSTRAINT "verification_tokens_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "project_display_settings" ADD CONSTRAINT "project_display_settings_project_id_video_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."video_projects"("id") ON DELETE no action ON UPDATE no action;

-- Step 10: Set NOT NULL constraints where appropriate
ALTER TABLE "team_members" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "team_members" ALTER COLUMN "team_id" SET NOT NULL;
ALTER TABLE "invitations" ALTER COLUMN "team_id" SET NOT NULL;
ALTER TABLE "invitations" ALTER COLUMN "invited_by" SET NOT NULL;
ALTER TABLE "ad_campaigns" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "ad_history" ALTER COLUMN "campaign_id" SET NOT NULL;
ALTER TABLE "ad_history" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "video_projects" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "project_text_overlays" ALTER COLUMN "project_id" SET NOT NULL;
ALTER TABLE "video_scripts" ALTER COLUMN "project_id" SET NOT NULL;
ALTER TABLE "video_segments" ALTER COLUMN "script_id" SET NOT NULL;
ALTER TABLE "audio_resources" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "project_audio_resources" ALTER COLUMN "project_id" SET NOT NULL;
ALTER TABLE "project_audio_resources" ALTER COLUMN "audio_resource_id" SET NOT NULL;
ALTER TABLE "usage_tracking" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "verification_tokens" ALTER COLUMN "user_id" SET NOT NULL;
ALTER TABLE "project_display_settings" ALTER COLUMN "project_id" SET NOT NULL; 