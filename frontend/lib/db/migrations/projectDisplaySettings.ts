import { pgTable, serial, integer, jsonb, timestamp, foreignKey } from 'drizzle-orm/pg-core';
import { videoProjects } from '../schema';

/**
 * Migration file for project display settings table
 * This table stores intro and end screen settings for each project
 */
export const projectDisplaySettings = pgTable('project_display_settings', {
  id: serial('id').primaryKey().notNull(),
  projectId: integer('project_id').notNull(),
  introSettings: jsonb('intro_settings'), // Stores all intro screen settings as JSONB
  endSettings: jsonb('end_settings'),     // Stores all end screen settings as JSONB
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
}, (table) => {
  return {
    projectIdFk: foreignKey({
      columns: [table.projectId],
      foreignColumns: [videoProjects.id],
      name: 'project_display_settings_project_id_fk'
    })
  };
}); 