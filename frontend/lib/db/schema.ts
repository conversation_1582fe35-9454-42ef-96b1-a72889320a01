import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  integer,
  jsonb,
  boolean,
  real,
  unique,
  date,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { number } from 'zod';
//plans and credits
export const planCredits ={
  freeTrial:100,
  Basic:1200,
  Standard:5600,
  Pro:12490
} as const
export type PlanKey = keyof typeof planCredits;
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  googleId: text('google_id').unique(),
  avatar: text('avatar'),
  role: varchar('role', { length: 20 }).notNull().default('owner'),
  emailVerified: boolean('email_verified').default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
  // 新增用户级别订阅字段
  stripeCustomerId: text('stripe_customer_id').unique(),
  stripeSubscriptionId: text('stripe_subscription_id').unique(),
  stripeProductId: text('stripe_product_id'),
  planName: varchar('plan_name', { length: 50 }),
  subscriptionStatus: varchar('subscription_status', { length: 20 }),
  credits: integer('credits').default(0),//credits column
  brandName: varchar('brand_name', { length: 100 }), // 用户的品牌名称
});

export const teams = pgTable('teams', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  stripeCustomerId: text('stripe_customer_id').unique(),
  stripeSubscriptionId: text('stripe_subscription_id').unique(),
  stripeProductId: text('stripe_product_id'),
  planName: varchar('plan_name', { length: 50 }),
  subscriptionStatus: varchar('subscription_status', { length: 20 }),
  credits: integer('credits').default(0), //credits column
});

export const teamMembers = pgTable('team_members', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id),
  teamId: uuid('team_id')
    .notNull()
    .references(() => teams.id),
  role: varchar('role', { length: 50 }).notNull(),
  joinedAt: timestamp('joined_at').notNull().defaultNow(),
});

export const activityLogs = pgTable('activity_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  teamId: uuid('team_id')
    .references(() => teams.id),
  userId: uuid('user_id').references(() => users.id),
  action: text('action').notNull(),
  timestamp: timestamp('timestamp').notNull().defaultNow(),
  ipAddress: varchar('ip_address', { length: 45 }),
});

export const invitations = pgTable('invitations', {
  id: uuid('id').primaryKey().defaultRandom(),
  teamId: uuid('team_id')
    .notNull()
    .references(() => teams.id),
  email: varchar('email', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull(),
  invitedBy: uuid('invited_by')
    .notNull()
    .references(() => users.id),
  invitedAt: timestamp('invited_at').notNull().defaultNow(),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
});

// adCampaigns
export const adCampaigns = pgTable('ad_campaigns', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id),
  description: text('description').notNull(),
  status: varchar('status', { length: 20 }).notNull().default('draft'),
  responseData: jsonb('response_data'), // store Full Json from backend
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// adHistory
export const adHistory = pgTable('ad_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  campaignId: uuid('campaign_id').notNull().references(() => adCampaigns.id),
  userId: uuid('user_id').notNull().references(() => users.id),
  action: varchar('action', { length: 20 }).notNull(), // create, update, delete
  timestamp: timestamp('timestamp').notNull().defaultNow(),
  data: jsonb('data'),
});


// ai chats
export const aiChats = pgTable( 'ai_chats',{
  id: uuid('id').primaryKey().defaultRandom(),
  title: varchar('title'),
  userId: uuid('user_id').notNull().references(() =>users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// chat messages
export const chatMessages = pgTable( 'chat_messages',{
  id: uuid("id").primaryKey().defaultRandom(),
  chatId: uuid('chat_id').notNull().references(() => aiChats.id),
  role: varchar('role').notNull(),
  content: varchar('content').notNull(),
  timeStamp: timestamp('created_at').notNull().defaultNow(),
});

// Video Projects Table
export const videoProjects = pgTable('video_projects', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id),
  teamId: uuid('team_id').references(() => teams.id),
  title: varchar('title', { length: 255 }).notNull(),
  brandName: varchar('brand_name', { length: 100 }).notNull(),
  summary: text('summary'),
  description: text('description'),
  adLength: integer('ad_length').notNull(), // in seconds, multiples of 5
  status: varchar('status', { length: 20 }).notNull().default('pending'), // pending, processing, completed
  productUrl: text('product_url'), // URL to product image/video for video generation
  generatedProductImageUrl: text('generated_product_image_url'), // Cached URL of generated product image for all clips
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

// Project Text Overlays Table - stores text overlays at project level
export const projectTextOverlays = pgTable('project_text_overlays', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectId: uuid('project_id').notNull().references(() => videoProjects.id),
  overlayData: jsonb('overlay_data').notNull(), // stores all text overlay properties in a single jsonb field
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Video Scripts Table
export const videoScripts = pgTable('video_scripts', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectId: uuid('project_id').notNull().references(() => videoProjects.id),
  segmentNumber: integer('segment_number').notNull(), 
  version: integer('version').notNull().default(1), 
  isCurrentVersion: boolean('is_current_version').notNull().default(true), 
  scriptText: text('script_text').notNull(), 
  narratorText: text('narrator_text'), 
  phrase: text('phrase'),
  narratorAudio: text('narrator_audio'), // Keep for backward compatibility
  narratorAudioMale: text('narrator_audio_male'), // Store male voice audio specifically
  narratorAudioFemale: text('narrator_audio_female'), // Store female voice audio specifically
  audioId: varchar('audio_id', { length: 100 }),
  defaultVoice: varchar('default_voice', { length: 50 }),
  elevenlabsVoiceIds: jsonb('elevenlabs_voice_ids'), // Array of voice IDs
  status: varchar('status', { length: 20 }).notNull().default('pending'), // pending, processing, completed
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Video Segments Table -
export const videoSegments = pgTable('video_segments', {
  id: uuid('id').primaryKey().defaultRandom(),
  scriptId: uuid('script_id').notNull().references(() => videoScripts.id),
  version: integer('version').notNull().default(1), 
  isCurrentVersion: boolean('is_current_version').notNull().default(true), 
  generationId: varchar('generation_id', { length: 100 }),
  videoUrl: text('video_url'), // Generated video URL
  status: varchar('status', { length: 20 }).notNull().default('pending'), // pending, processing, completed
  createdAt: timestamp('created_at').notNull().defaultNow(),
  completedAt: timestamp('completed_at'),
});

// Audio Resources Table
export const audioResources = pgTable('audio_resources', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id),
  name: varchar('name', { length: 255 }).notNull(),
  type: varchar('type', { length: 20 }).notNull(), // background, voice, effect
  url: text('url').notNull(),
  duration: integer('duration'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// junction table for audio resources and video projects since their relation is many to many
export const projectAudioResources = pgTable('project_audio_resources', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectId: uuid('project_id').notNull().references(() => videoProjects.id),
  audioResourceId: uuid('audio_resource_id').notNull().references(() => audioResources.id),
});


// Usage tracking table
export const usageTracking = pgTable('usage_tracking', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id),
  feature: varchar('feature', { length: 50 }).notNull(), // e.g., 'script_generation'
  count: integer('count').notNull().default(0),
  resetDate: timestamp('reset_date').notNull(), // Date when the count will reset
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const usageTrackingRelations = relations(usageTracking, ({ one }) => ({
  user: one(users, {
    fields: [usageTracking.userId],
    references: [users.id],
  }),
}));

// Define all relations after all tables are defined
export const teamsRelations = relations(teams, ({ many }) => ({
  teamMembers: many(teamMembers),
  activityLogs: many(activityLogs),
  invitations: many(invitations),
}));

export const usersRelations = relations(users, ({ many }) => ({
  teamMembers: many(teamMembers),
  invitationsSent: many(invitations),
  adCampaigns: many(adCampaigns),
  videoProjects: many(videoProjects), // Add video projects relation
}));

export const invitationsRelations = relations(invitations, ({ one }) => ({
  team: one(teams, {
    fields: [invitations.teamId],
    references: [teams.id],
  }),
  invitedBy: one(users, {
    fields: [invitations.invitedBy],
    references: [users.id],
  }),
}));

export const teamMembersRelations = relations(teamMembers, ({ one }) => ({
  user: one(users, {
    fields: [teamMembers.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [teamMembers.teamId],
    references: [teams.id],
  }),
}));

export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
  team: one(teams, {
    fields: [activityLogs.teamId],
    references: [teams.id],
  }),
  user: one(users, {
    fields: [activityLogs.userId],
    references: [users.id],
  }),
}));

export const adCampaignsRelations = relations(adCampaigns, ({ one, many }) => ({
  user: one(users, {
    fields: [adCampaigns.userId],
    references: [users.id],
  }),
  history: many(adHistory),
}));

export const adHistoryRelations = relations(adHistory, ({ one }) => ({
  campaign: one(adCampaigns, {
    fields: [adHistory.campaignId],
    references: [adCampaigns.id],
  }),
  user: one(users, {
    fields: [adHistory.userId],
    references: [users.id],
  }),
}));

export const videoProjectsRelations = relations(videoProjects, ({ many, one }) => ({
  scripts: many(videoScripts),
  projectAudioResources: many(projectAudioResources),
  textOverlays: many(projectTextOverlays),
  displaySettings: many(projectDisplaySettings),
  user: one(users, {
    fields: [videoProjects.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [videoProjects.teamId],
    references: [teams.id],
  }),
}));

export const projectTextOverlaysRelations = relations(projectTextOverlays, ({ one }) => ({
  project: one(videoProjects, {
    fields: [projectTextOverlays.projectId],
    references: [videoProjects.id],
  }),
}));

export const videoScriptsRelations = relations(videoScripts, ({ one, many }) => ({
  project: one(videoProjects, {
    fields: [videoScripts.projectId],
    references: [videoProjects.id],
  }),
  segments: many(videoSegments),
}));

export const videoSegmentsRelations = relations(videoSegments, ({ one }) => ({
  script: one(videoScripts, {
    fields: [videoSegments.scriptId],
    references: [videoScripts.id],
  }),
}));

export const audioResourcesRelations = relations(audioResources, ({ one, many }) => ({
  project: many(videoProjects),//many projects
  user:one(users,{
   fields: [audioResources.userId],// added user relation
   references: [users.id],
  }),
}));

// Define the relation between videoProjects and audioResources
export const projectAudioResourcesRelations = relations(projectAudioResources, ({ one }) => ({
  project: one(videoProjects, {
    fields: [projectAudioResources.projectId],
    references: [videoProjects.id],
  }),
  audioResource: one(audioResources, {
    fields: [projectAudioResources.audioResourceId],
    references: [audioResources.id],
  }),
}));

// Project Display Settings Table - stores intro and end screen settings at project level
export const projectDisplaySettings = pgTable('project_display_settings', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectId: uuid('project_id').notNull().references(() => videoProjects.id),
  introSettings: jsonb('intro_settings'), // stores intro screen settings as JSON
  endSettings: jsonb('end_settings'),     // stores end screen settings as JSON
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Project Display Settings Relations
export const projectDisplaySettingsRelations = relations(projectDisplaySettings, ({ one }) => ({
  project: one(videoProjects, {
    fields: [projectDisplaySettings.projectId],
    references: [videoProjects.id],
  }),
}));

export type User = typeof users.$inferSelect & {
  googleId: string | null;
  avatar: string | null;
};

export type NewUser = typeof users.$inferInsert & {
  googleId: string | null;
  avatar: string | null;
};

export type Team = typeof teams.$inferSelect;
export type NewTeam = typeof teams.$inferInsert;
export type TeamMember = typeof teamMembers.$inferSelect;
export type NewTeamMember = typeof teamMembers.$inferInsert;
export type ActivityLog = typeof activityLogs.$inferSelect;
export type NewActivityLog = typeof activityLogs.$inferInsert;
export type Invitation = typeof invitations.$inferSelect;
export type NewInvitation = typeof invitations.$inferInsert;
export type AdCampaign = typeof adCampaigns.$inferSelect;
export type NewAdCampaign = typeof adCampaigns.$inferInsert;
export type AdHistory = typeof adHistory.$inferSelect;
export type NewAdHistory = typeof adHistory.$inferInsert;

// Video editing related types
export type VideoProject = typeof videoProjects.$inferSelect;
export type NewVideoProject = typeof videoProjects.$inferInsert;
export type ProjectTextOverlay = typeof projectTextOverlays.$inferSelect;
export type VideoScript = typeof videoScripts.$inferSelect;
export type NewVideoScript = typeof videoScripts.$inferInsert;
export type VideoSegment = typeof videoSegments.$inferSelect;
export type NewVideoSegment = typeof videoSegments.$inferInsert;
export type AudioResource = typeof audioResources.$inferSelect;
export type NewAudioResource = typeof audioResources.$inferInsert;

export type TeamDataWithMembers = Team & {
  teamMembers: (TeamMember & {
    user: Pick<User, 'id' | 'name' | 'email'>;
  })[];
};

export enum ActivityType {
  SIGN_UP = 'SIGN_UP',
  SIGN_IN = 'SIGN_IN',
  SIGN_OUT = 'SIGN_OUT',
  UPDATE_PASSWORD = 'UPDATE_PASSWORD',
  DELETE_ACCOUNT = 'DELETE_ACCOUNT',
  UPDATE_ACCOUNT = 'UPDATE_ACCOUNT',
  CREATE_TEAM = 'CREATE_TEAM',
  REMOVE_TEAM_MEMBER = 'REMOVE_TEAM_MEMBER',
  INVITE_TEAM_MEMBER = 'INVITE_TEAM_MEMBER',
  ACCEPT_INVITATION = 'ACCEPT_INVITATION',
}

export type ProjectAudioResource = typeof projectAudioResources.$inferSelect;
export type NewProjectAudioResource = typeof projectAudioResources.$inferInsert;

export type UsageTracking = typeof usageTracking.$inferSelect;
export type NewUsageTracking = typeof usageTracking.$inferInsert;

export const verificationTokens = pgTable('verification_tokens', {
  id: uuid('id').primaryKey().defaultRandom(),
  token: text('token').notNull(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}, (table) => [
  unique('verification_tokens_token_unique').on(table.token),
]);

// Project Display Settings Type Definitions
export type ProjectDisplaySetting = typeof projectDisplaySettings.$inferSelect;
export type NewProjectDisplaySetting = typeof projectDisplaySettings.$inferInsert;
 