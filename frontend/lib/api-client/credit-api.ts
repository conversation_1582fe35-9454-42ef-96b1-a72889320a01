/**
 * API client functions for interacting with the credits system
 */

// Get user's credit balance and history
export async function getUserCredits() {
  const response = await fetch('/api/user/credits', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch user credits');
  }

  return response.json();
}

// Check if user has enough credits for an operation
export async function checkCredits(feature: string, parameters: any = {}) {
  const response = await fetch('/api/credits/check', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ feature, parameters }),
  });

  if (!response.ok) {
    throw new Error('Failed to check credits');
  }

  return response.json();
}

// Generate audience insights
export async function generateAudienceInsights(data: {
  brandName: string;
  industry: string;
  targetAudience?: string;
}) {
  const response = await fetch('/api/audience-insights/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to generate audience insights');
  }

  return response.json();
}

// Create a video campaign
export async function createVideoCampaign(data: {
  title: string;
  brandName: string;
  adLength: number;
  summary?: string;
  description?: string;
  productUrl?: string;
  introLogoUrl?: string;
  endLogoUrl?: string;
}) {
  const response = await fetch('/api/video-campaigns/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to create video campaign');
  }

  return response.json();
}

// Generate script
export async function generateScript(data: {
  projectData: {
    brandName: string;
    summary: string;
    description: string;
  };
  segmentNumber: number;
}) {
  const response = await fetch('/api/scripts/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to generate script');
  }

  return response.json();
} 