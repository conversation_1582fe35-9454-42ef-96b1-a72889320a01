import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// VideoProject interface definition
export interface VideoProject {
  id: string;
  userId: string;
  title: string;
  brandName: string;
  summary: string | null;
  description: string;
  adLength: string;
  status: 'pending' | 'processing' | 'completed';
  createdAt: string;
  updatedAt: string;
}

// VideoProject store interface definition
interface VideoProjectState {
  // state
  projects: VideoProject[];
  currentProject: VideoProject | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  
  // actions
  fetchProjects: () => Promise<void>;
  fetchProject: (id: string) => Promise<void>;
  createProject: (project: Omit<VideoProject, 'id' | 'userId' | 'createdAt' | 'updatedAt' | 'status'>) => Promise<VideoProject | null>;
  updateProject: (id: string, data: Partial<VideoProject>) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  setCurrentProject: (project: VideoProject | null) => void;
  clearError: () => void;
}

// create VideoProjectStore
export const useVideoProjectStore = create<VideoProjectState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        projects: [],
        currentProject: null,
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        error: null,
        
        // fetch all projects
        fetchProjects: async () => {
          set({ isLoading: true, error: null });
          try {
            const response = await fetch('/api/video-editing/video-projects');
            if (!response.ok) {
              throw new Error('获取项目列表失败');
            }
            const data = await response.json();
            set({ projects: data, isLoading: false });
          } catch (error) {
            console.error('Error fetching projects:', error);
            set({ 
              error: error instanceof Error ? error.message : '获取项目失败', 
              isLoading: false 
            });
          }
        },
        
        // get a single project
        fetchProject: async (id) => {
          set({ isLoading: true, error: null });
          try {
            const response = await fetch(`/api/video-editing/video-projects/${id}`);
            if (!response.ok) {
              throw new Error('获取项目详情失败');
            }
            const data = await response.json();
            set({ currentProject: data, isLoading: false });
          } catch (error) {
            console.error('Error fetching project:', error);
            set({ 
              error: error instanceof Error ? error.message : '获取项目详情失败', 
              isLoading: false 
            });
          }
        },
        
        // create a new project
        createProject: async (project) => {
          set({ isCreating: true, error: null });
          try {
            const response = await fetch('/api/video-editing/video-projects', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(project),
            });
            
            if (!response.ok) {
              throw new Error('创建项目失败');
            }
            
            const newProject = await response.json();
            set(state => ({ 
              projects: [...state.projects, newProject],
              currentProject: newProject,
              isCreating: false 
            }));
            
            return newProject;
          } catch (error) {
            console.error('Error creating project:', error);
            set({ 
              error: error instanceof Error ? error.message : '创建项目失败', 
              isCreating: false 
            });
            return null;
          }
        },
        
        // update a project
        updateProject: async (id, data) => {
          set({ isUpdating: true, error: null });
          try {
            const response = await fetch(`/api/video-editing/video-projects/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data),
            });
            
            if (!response.ok) {
              throw new Error('更新项目失败');
            }
            
            const updatedProject = await response.json();
            set(state => ({ 
              projects: state.projects.map(p => p.id === id ? updatedProject : p),
              currentProject: state.currentProject?.id === id ? updatedProject : state.currentProject,
              isUpdating: false 
            }));
          } catch (error) {
            console.error('Error updating project:', error);
            set({ 
              error: error instanceof Error ? error.message : '更新项目失败', 
              isUpdating: false 
            });
          }
        },
        
        // delete a project
        deleteProject: async (id) => {
          set({ isDeleting: true, error: null });
          try {
            const response = await fetch(`/api/video-editing/video-projects/${id}`, {
              method: 'DELETE',
            });
            
            if (!response.ok) {
              throw new Error('删除项目失败');
            }
            
            set(state => ({ 
              projects: state.projects.filter(p => p.id !== id),
              currentProject: state.currentProject?.id === id ? null : state.currentProject,
              isDeleting: false 
            }));
          } catch (error) {
            console.error('Error deleting project:', error);
            set({ 
              error: error instanceof Error ? error.message : '删除项目失败', 
              isDeleting: false 
            });
          }
        },
        
        // set current project
        setCurrentProject: (project) => {
          set({ currentProject: project });
        },
        
        // clear error
        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: 'video-project-storage',
        partialize: (state) => ({ 
          projects: state.projects,
          currentProject: state.currentProject 
        }),
      }
    )
  )
);