import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { VideoSegment } from '@/lib/store/videoSegmentStore';

interface VideoPlayerState {
  // Basic state
  segments: VideoSegment[];
  selectedVoice: 'blue' | 'pink';
  playerReady: boolean;
  currentFrame: number;
  backgroundMusicVolume: number;
  isBackgroundMusicEnabled: boolean;
  
  // Settings
  fps: number;
  framesPerSegment: number;
  transitionDurationFrames: number;
  introBlackScreenFrames: number;
  endScreenFrames: number;
  
  // Dynamic settings from other stores (will be set by components)
  introEnabled: boolean;
  endEnabled: boolean;
  backgroundMusicUrl: string | null;
  scripts: Record<string, any>;
  
  // Computed values
  totalFrames: number;
  totalTransitionsCount: number;
  
  // Actions
  setSegments: (segments: VideoSegment[]) => void;
  setSelectedVoice: (voice: 'blue' | 'pink') => void;
  setPlayerReady: (ready: boolean) => void;
  setCurrentFrame: (frame: number) => void;
  setIntroEnabled: (enabled: boolean) => void;
  setEndEnabled: (enabled: boolean) => void;
  setBackgroundMusicUrl: (url: string | null) => void;
  setBackgroundMusicVolume: (volume: number) => void;
  setBackgroundMusicEnabled: (enabled: boolean) => void;
  setScripts: (scripts: Record<string, any>) => void;
  updateDynamicSettings: (settings: {
    introEnabled: boolean;
    endEnabled: boolean;
  }) => void;
  
  // Getter methods (no direct dependency on hooks)
  getVoiceAudioFile: (segment: VideoSegment, index: number) => string;
}

export const useVideoPlayerStore = create<VideoPlayerState>()(
  persist(
    (set, get) => ({
      // Initial state
      segments: [],
      selectedVoice: 'blue',
      playerReady: false,
      currentFrame: 0,
      backgroundMusicVolume: 1,
      isBackgroundMusicEnabled: true,
      
      // Fixed settings
      fps: 30,
      framesPerSegment: 140, // 5 seconds at 30 fps
      transitionDurationFrames: 5, // 1 second transition
      introBlackScreenFrames: 60, // 2 seconds
      endScreenFrames: 99, // 3 seconds
      
      // Dynamic settings (will be set by components)
      introEnabled: true,
      endEnabled: true,
      backgroundMusicUrl: null,
      scripts: {},
      
      // Computed values (initially 0, will be calculated in setSegments)
      totalFrames: 0,
      totalTransitionsCount: 0,
      
      // Setter methods
      setSegments: (segments) => {
        if (!Array.isArray(segments)) {
          console.error('setSegments called with invalid segments:', segments);
          return;
        }
        
        const introEnabled = get().introEnabled;
        const endEnabled = get().endEnabled;
        const framesPerSegment = get().framesPerSegment;
        const introBlackScreenFrames = get().introBlackScreenFrames;
        const endScreenFrames = get().endScreenFrames;
        
        // Calculate total frames based on segments and settings
        const segmentsFrames = segments.length * framesPerSegment;
        const introFrames = introEnabled ? introBlackScreenFrames : 0;
        const endFrames = endEnabled ? endScreenFrames : 0;
        const totalFrames = segmentsFrames + introFrames + endFrames;
        
        const totalTransitionsCount = segments.length > 0 ? segments.length - 1 : 0;
        
        set({ 
          segments, 
          totalFrames, 
          totalTransitionsCount,
          playerReady: segments.length > 0
        });
      },
      
      // Set the selected voice type (blue = male, pink = female)
      setSelectedVoice: (voice) => {
        // Only update if the value is actually changing
        if (get().selectedVoice !== voice) {
          console.log(`[videoPlayerStore] Changing voice to: ${voice}`);
          set({ selectedVoice: voice });
          
          // If we had any other side effects to perform when voice changes,
          // they would go here. For example, updating preferences or
          // triggering other store updates.
        }
      },
      
      setPlayerReady: (ready) => set({ playerReady: ready }),
      setCurrentFrame: (frame) => set({ currentFrame: frame }),
      setIntroEnabled: (enabled) => {
        set({ introEnabled: enabled });
        // Recalculate frames when changing settings
        get().setSegments(get().segments);
      },
      setEndEnabled: (enabled) => {
        set({ endEnabled: enabled });
        // Recalculate frames when changing settings
        get().setSegments(get().segments);
      },
      setBackgroundMusicUrl: (url) => set({ backgroundMusicUrl: url }),
      setBackgroundMusicVolume: (volume) => set({ backgroundMusicVolume: volume }),
      setBackgroundMusicEnabled: (enabled) => set({ isBackgroundMusicEnabled: enabled }),
      setScripts: (scripts) => set({ scripts }),
      updateDynamicSettings: (settings) => {
        set({ 
          introEnabled: settings.introEnabled,
          endEnabled: settings.endEnabled
        });
        // Recalculate frames when changing settings
        get().setSegments(get().segments);
      },
      
      // Business logic methods
      getVoiceAudioFile: (segment, index) => {
        const selectedVoice = get().selectedVoice;
        
        // Get the voice file based on the selected voice type
        let voiceFile;
        
        // Lookup the script for this segment to get voice-specific audio
        const script = get().scripts[segment.scriptId];
        if (script) {
          if (selectedVoice === 'pink' && script.narratorAudioFemale) {
            return script.narratorAudioFemale;
          } else if (selectedVoice === 'blue' && script.narratorAudioMale) {
            return script.narratorAudioMale;
          }
        }
        
        // Fallback to the segment's narratorAudio
        voiceFile = segment.narratorAudio;
        
        if (!voiceFile) {
          console.warn(`No voice file found for segment ${index} (${segment.id})`);
          return '';
        }
        
        return voiceFile;
      }
    }),
    {
      name: 'video-player-store', // Update version to avoid conflicts with old data
      partialize: (state) => ({
        // Don't persist computed values or derived state that can be recomputed
        selectedVoice: state.selectedVoice,
        introEnabled: state.introEnabled,
        endEnabled: state.endEnabled,
        backgroundMusicVolume: state.backgroundMusicVolume,
        isBackgroundMusicEnabled: state.isBackgroundMusicEnabled,
        // These settings might be worth persisting for user preference
        fps: state.fps,
        framesPerSegment: state.framesPerSegment,
        introBlackScreenFrames: state.introBlackScreenFrames,
        endScreenFrames: state.endScreenFrames,
      })
    }
  )
); 