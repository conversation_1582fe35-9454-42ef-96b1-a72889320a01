import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Text overlay data structure
export interface TextOverlay {
  id: string;
  text: string;
  fontSize: number;
  color: string;
  position: { x: number, y: number };
  alignment: 'left' | 'center' | 'right';
  startTime: number; // Start time in seconds
  duration: number; // Duration in seconds
}

// Enhanced Text overlay store with API integration
interface TextOverlayStore {
  textOverlays: TextOverlay[];
  projectId: string | null;
  isLoading: boolean;
  error: string | null;
  removedOverlayIds: string[];
  
  // Basic CRUD operations
  addTextOverlay: (overlay: Omit<TextOverlay, 'id'>) => TextOverlay;
  updateTextOverlay: (id: string, updates: Partial<Omit<TextOverlay, 'id'>>) => void;
  removeTextOverlay: (id: string) => void;
  
  // Project and API related operations
  setProjectId: (id: string | null) => void;
  loadFromServer: () => Promise<void>;
  saveToServer: () => Promise<void>;
}

// Constants for temporary ID management
const TEMP_ID_PREFIX = 'temp_';

export const useTextOverlayStore = create<TextOverlayStore>()(
  persist(
    (set, get) => ({
      textOverlays: [],
      projectId: null,
      isLoading: false,
      error: null,
      removedOverlayIds: [], // Initialize deleted IDs list
      
      // Add a new text overlay
      addTextOverlay: (overlayData) => {
        // Generate a temporary ID that's clearly distinguishable
        const tempId = TEMP_ID_PREFIX + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        const newOverlay = { 
          id: tempId,
          ...overlayData 
        };
        
        set(state => ({
          textOverlays: [...state.textOverlays, newOverlay]
        }));
        
        return newOverlay;
      },
      
      // Update an existing text overlay
      updateTextOverlay: (id, updates) => {
        set(state => ({
          textOverlays: state.textOverlays.map(overlay => 
            overlay.id === id ? { ...overlay, ...updates } : overlay
          )
        }));
      },
      
      // Remove a text overlay
      removeTextOverlay: (id) => {
        // Add ID to removed list if it's a real ID (from server)
        if (!id.startsWith(TEMP_ID_PREFIX)) {
          set(state => ({
            removedOverlayIds: [...state.removedOverlayIds, id]
          }));
        }
        
        // Remove from current list
        set(state => ({
          textOverlays: state.textOverlays.filter(overlay => overlay.id !== id)
        }));
      },
      
      // Set the current project ID
      setProjectId: (id) => {
        set({ projectId: id });
      },
      
      // Load text overlays from the server
      loadFromServer: async () => {
        const { projectId } = get();
        if (!projectId) return;
        
        set({ isLoading: true, error: null });
        
        try {
          const response = await fetch(`/api/video-editing/video-projects/${projectId}/text-overlays`);
          
          if (!response.ok) {
            throw new Error('Failed to load text overlays');
          }
          
          const data = await response.json();
          
          // Transform database format to frontend format
          const overlays = data.map((item: any) => {
            // Get overlay data - might be in overlayData or at top level
            const overlayData = item.overlayData || item;
            
            return {
              id: String(item.id),
              text: overlayData.text || '',
              fontSize: overlayData.fontSize || 16,
              color: overlayData.color || '#ffffff',
              position: { 
                x: overlayData.positionX || overlayData.position?.x || 0, 
                y: overlayData.positionY || overlayData.position?.y || 0
              },
              alignment: overlayData.alignment || 'center',
              startTime: overlayData.startTime || 0,
              duration: overlayData.duration || 5,
            };
          });
          
          set({ 
            textOverlays: overlays, 
            isLoading: false,
            removedOverlayIds: [] // Reset deleted IDs list
          });
        } catch (error) {
          console.error('Error loading text overlays:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          });
        }
      },
      
      // Save text overlays to the server
      saveToServer: async () => {
        const { projectId, textOverlays, removedOverlayIds } = get();
        if (!projectId) {
          console.error('Cannot save text overlays: projectId is not set');
          return;
        }
        
        console.log(`Saving text overlays for project ${projectId}: ${textOverlays.length} overlays, ${removedOverlayIds.length} to remove`);
        
        set({ isLoading: true, error: null });
        
        try {
          // 1. Process deletion operations
          for (const id of removedOverlayIds) {
            console.log(`Deleting text overlay with ID ${id}`);
            const response = await fetch(`/api/video-editing/video-projects/${projectId}/text-overlays?overlayId=${id}`, {
              method: 'DELETE'
            });
            
            if (!response.ok) {
              try {
                const errorData = await response.json();
                throw new Error(errorData.error || `Failed to delete text overlay (ID: ${id})`);
              } catch (parseError) {
                console.error('Failed to parse error response:', parseError);
                throw new Error(`Failed to delete text overlay (ID: ${id}): ${response.status} ${response.statusText}`);
              }
            }
          }
          
          // 2. Process add and update operations
          const updatedOverlays = [...textOverlays]; // Create a copy to update IDs
          
          for (let i = 0; i < updatedOverlays.length; i++) {
            const overlay = updatedOverlays[i];
            
            // If it's a newly created overlay (has temporary ID)
            if (overlay.id.startsWith(TEMP_ID_PREFIX)) {
              console.log(`Creating new text overlay: "${overlay.text}" at ${overlay.startTime}s`);
              // POST - create
              const response = await fetch(`/api/video-editing/video-projects/${projectId}/text-overlays`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  text: overlay.text,
                  fontSize: overlay.fontSize,
                  color: overlay.color,
                  positionX: overlay.position.x,
                  positionY: overlay.position.y,
                  alignment: overlay.alignment,
                  startTime: overlay.startTime,
                  duration: overlay.duration,
                })
              });
              
              if (!response.ok) {
                try {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to create text overlay');
                } catch (parseError) {
                  console.error('Failed to parse error response:', parseError);
                  throw new Error(`Failed to create text overlay: ${response.status} ${response.statusText}`);
                }
              }
              
              // Get the new overlay with server-generated ID
              const newOverlay = await response.json();
              console.log(`Created text overlay with server ID ${newOverlay.id}`);
              // Update the ID in our local state
              updatedOverlays[i] = {
                ...overlay,
                id: String(newOverlay.id)
              };
            } else {
              console.log(`Updating existing text overlay with ID ${overlay.id}`);
              // PUT - update existing overlay
              const response = await fetch(`/api/video-editing/video-projects/${projectId}/text-overlays?overlayId=${overlay.id}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  overlayId: overlay.id,
                  text: overlay.text,
                  fontSize: overlay.fontSize,
                  color: overlay.color,
                  positionX: overlay.position.x,
                  positionY: overlay.position.y,
                  alignment: overlay.alignment,
                  startTime: overlay.startTime,
                  duration: overlay.duration,
                })
              });
              
              if (!response.ok) {
                try {
                  const errorData = await response.json();
                  throw new Error(errorData.error || `Failed to update text overlay (ID: ${overlay.id})`);
                } catch (parseError) {
                  console.error('Failed to parse error response:', parseError);
                  throw new Error(`Failed to update text overlay (ID: ${overlay.id}): ${response.status} ${response.statusText}`);
                }
              }
            }
          }
          
          console.log('Successfully saved all text overlays');
          
          // Update the store with the new IDs
          set({ 
            textOverlays: updatedOverlays,
            removedOverlayIds: [],
            isLoading: false 
          });
        } catch (error) {
          console.error('Error saving text overlays:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          });
        }
      }
    }),
    {
      name: 'text-overlays-storage',
    }
  )
);
