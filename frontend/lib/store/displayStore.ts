import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * Combined screen settings interface for both intro and end screens
 */
export interface ScreenSettings {
  intro: {
    backgroundColor: string;
    logoUrl: string;
    logoWidth: number;  // Width as percentage
    logoHeight: number; // Height as percentage
    logoX: number;      // Horizontal position as percentage (0-100)
    logoY: number;      // Vertical position as percentage (0-100)
    isEnabled: boolean;
  };
  end: {
    backgroundColor: string;
    logoUrl: string;
    logoWidth: number;  // Width as percentage
    logoHeight: number; // Height as percentage
    logoX: number;      // Horizontal position as percentage (0-100)
    logoY: number;      // Vertical position as percentage (0-100)
    centerText: string;
    centerTextColor: string;
    centerTextSize: number;
    centerTextX: number; // Horizontal position as percentage (0-100)
    centerTextY: number; // Vertical position as percentage (0-100)
    bottomText: string;
    bottomTextColor: string;
    bottomTextSize: number;
    bottomTextX: number; // Horizontal position as percentage (0-100)
    bottomTextY: number; // Vertical position as percentage (0-100)
    isEnabled: boolean;
    audioUrl?: string | null; // Optional URL for end screen audio
    audioVolume?: number;     // Volume for end screen audio (0-1)
    isAudioEnabled?: boolean; // Toggle for end screen audio
  };
}

/**
 * Display store state and actions interface
 */
interface DisplayState {
  screens: ScreenSettings;
  currentProjectId: string | null; // Track current project ID for database sync
  isLoading: boolean;              // Loading state for async operations
  error: string | null;            // Error state for async operations
  
  // Methods
  setCurrentProject: (projectId: string | null) => void;
  updateIntroScreen: (updates: Partial<ScreenSettings['intro']>) => void;
  updateEndScreen: (updates: Partial<ScreenSettings['end']>) => void;
  resetScreens: () => void;
  
  // Database sync methods
  fetchProjectSettings: (projectId: string) => Promise<void>;
  saveProjectSettings: () => Promise<void>;
}

/**
 * Default settings for both screens
 */
const defaultSettings: ScreenSettings = {
  intro: {
    backgroundColor: '#ffffff',
    logoUrl: '/Fylow.png',
    logoWidth: 30,
    logoHeight: 70,
    logoX: 50,
    logoY: 50,
    isEnabled: true,
  },
  end: {
    backgroundColor: '#ffffff',
    logoUrl: '/Fylow.png',
    logoWidth: 30,
    logoHeight: 80,
    logoX: 50,
    logoY: 25,
    centerText: 'Thank You For Watching',
    centerTextColor: '#000000',
    centerTextSize: 32,
    centerTextX: 50,
    centerTextY: 60,
    bottomText: 'Visit our website for more information',
    bottomTextColor: '#000000',
    bottomTextSize: 18,
    bottomTextX: 50,
    bottomTextY: 85,
    isEnabled: false,
    audioUrl: null,
    audioVolume: 1,
    isAudioEnabled: true,
  }
};

/**
 * Combined display store for managing intro and end screens
 * Supports both local storage persistence and database sync
 */
export const useDisplayStore = create<DisplayState>()(
  persist(
    (set, get) => ({
      // Initial state
      screens: defaultSettings,
      currentProjectId: null,
      isLoading: false,
      error: null,
      
      // Set current project ID
      setCurrentProject: (projectId) => set({ currentProjectId: projectId }),
      
      // Update intro screen with auto-save to database if project ID exists
      updateIntroScreen: (updates) => {
        set(state => ({
          screens: {
            ...state.screens,
            intro: {
              ...state.screens.intro,
              ...updates
            }
          }
        }));
        
        // Auto-save to database if project ID is set
        const { currentProjectId, saveProjectSettings } = get();
        if (currentProjectId) {
          saveProjectSettings();
        }
      },
      
      // Update end screen with auto-save to database if project ID exists
      updateEndScreen: (updates) => {
        set(state => ({
          screens: {
            ...state.screens,
            end: {
              ...state.screens.end,
              ...updates
            }
          }
        }));
        
        // Auto-save to database if project ID is set
        const { currentProjectId, saveProjectSettings } = get();
        if (currentProjectId) {
          saveProjectSettings();
        }
      },
      
      // Reset both screens to default settings
      resetScreens: () => {
        set({ screens: defaultSettings });
        
        // Auto-save reset to database if project ID is set
        const { currentProjectId, saveProjectSettings } = get();
        if (currentProjectId) {
          saveProjectSettings();
        }
      },
      
      // Fetch settings from database for a specific project
      fetchProjectSettings: async (projectId) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await fetch(`/api/video-editing/projects/${projectId}/display-settings`);
          
          if (!response.ok) {
            throw new Error('Failed to fetch display settings');
          }
          
          const data = await response.json();
          
          // Update store with settings from database
          set(state => ({
            screens: {
              intro: data.introSettings || state.screens.intro,
              end: data.endSettings || state.screens.end
            },
            currentProjectId: projectId,
            isLoading: false
          }));
        } catch (error) {
          console.error('Error fetching display settings:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false 
          });
        }
      },
      
      // Save current settings to database
      saveProjectSettings: async () => {
        const { currentProjectId, screens } = get();
        if (!currentProjectId) return;
        
        try {
          set({ isLoading: true, error: null });
          
          const response = await fetch(
            `/api/video-editing/projects/${currentProjectId}/display-settings`, 
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                introSettings: screens.intro,
                endSettings: screens.end
              }),
            }
          );
          
          if (!response.ok) {
            throw new Error('Failed to save display settings');
          }
          
          set({ isLoading: false });
        } catch (error) {
          console.error('Error saving display settings:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false 
          });
        }
      }
    }),
    {
      name: 'display-storage',
      // Only persist screens to localStorage, not the project ID or loading states
      partialize: (state) => ({ screens: state.screens }),
    }
  )
);

/**
 * Backward compatibility layer for intro screen store
 * This maintains the same API as the original useIntroScreenStore
 */
export const useIntroScreenStore = () => {
  const { screens, updateIntroScreen, resetScreens } = useDisplayStore();
  
  return {
    settings: screens.intro,
    setBackgroundColor: (color: string) => updateIntroScreen({ backgroundColor: color }),
    setLogoUrl: (url: string) => updateIntroScreen({ logoUrl: url }),
    setLogoWidth: (width: number) => updateIntroScreen({ logoWidth: width }),
    setLogoHeight: (height: number) => updateIntroScreen({ logoHeight: height }),
    setLogoPosition: (x: number, y: number) => updateIntroScreen({ logoX: x, logoY: y }),
    setEnabled: (enabled: boolean) => updateIntroScreen({ isEnabled: enabled }),
    resetSettings: () => resetScreens(),
  };
};

/**
 * Backward compatibility layer for end screen store
 * This maintains the same API as the original useEndScreenStore
 */
export const useEndScreenStore = () => {
  const { screens, updateEndScreen, resetScreens } = useDisplayStore();
  
  return {
    settings: screens.end,
    setBackgroundColor: (color: string) => updateEndScreen({ backgroundColor: color }),
    setLogoUrl: (url: string) => updateEndScreen({ logoUrl: url }),
    setLogoWidth: (width: number) => updateEndScreen({ logoWidth: width }),
    setLogoHeight: (height: number) => updateEndScreen({ logoHeight: height }),
    setLogoPosition: (x: number, y: number) => updateEndScreen({ logoX: x, logoY: y }),
    setCenterText: (text: string) => updateEndScreen({ centerText: text }),
    setCenterTextColor: (color: string) => updateEndScreen({ centerTextColor: color }),
    setCenterTextSize: (size: number) => updateEndScreen({ centerTextSize: size }),
    setCenterTextPosition: (x: number, y: number) => updateEndScreen({ 
      centerTextX: x, 
      centerTextY: y 
    }),
    setBottomText: (text: string) => updateEndScreen({ bottomText: text }),
    setBottomTextColor: (color: string) => updateEndScreen({ bottomTextColor: color }),
    setBottomTextSize: (size: number) => updateEndScreen({ bottomTextSize: size }),
    setBottomTextPosition: (x: number, y: number) => updateEndScreen({ 
      bottomTextX: x, 
      bottomTextY: y 
    }),
    setEnabled: (enabled: boolean) => updateEndScreen({ isEnabled: enabled }),
    resetSettings: () => resetScreens(),
  };
}; 