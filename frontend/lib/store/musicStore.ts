import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Interface matching the database schema for audio resources
export interface AudioResource {
  id: number;
  userId: number;
  name: string;
  type: 'background' | 'voice' | 'effect'; // Matching the types in the database
  url: string;
  duration?: number; // Optional as it is in the database
  createdAt: Date;
}

interface MusicStore {
  audioResources: AudioResource[];
  currentAudioId: number | null;
  isLoading: boolean;
  isUploading: boolean;
  error: string | null;
  
  // CRUD operations
  addAudioResource: (resource: Omit<AudioResource, 'id' | 'createdAt'>) => Promise<AudioResource>;
  addAudioResourceFromFile: (file: File, type: AudioResource['type']) => Promise<AudioResource>;
  addAudioResourceFromElevenLabs: (text: string, gender: 'male' | 'female', name?: string) => Promise<AudioResource>;
  removeAudioResource: (id: number) => Promise<void>;
  updateAudioResource: (id: number, updates: Partial<Omit<AudioResource, 'id' | 'createdAt'>>) => Promise<AudioResource>;
  
  // Selection and filtering
  setCurrentAudio: (id: number | null) => void;
  getAudioByType: (type: AudioResource['type']) => AudioResource[];
  getCurrentAudio: () => AudioResource | null;
  
  // Data fetching
  fetchAudioResources: () => Promise<void>;
  fetchAudioResourcesByType: (type: AudioResource['type']) => Promise<void>;
}

// 将文件转换为Base64字符串
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

// 获取音频时长
const getAudioDuration = (url: string): Promise<number> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    audio.src = url;
    
    const handleLoadedMetadata = () => {
      resolve(audio.duration);
      cleanup();
    };
    
    const handleError = () => {
      reject(new Error('Failed to load audio file'));
      cleanup();
    };
    
    const cleanup = () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('error', handleError);
      audio.src = '';
    };
    
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('error', handleError);
  });
};

// Function to load default background music with proper filenames and durations
const loadDefaultMusic = async (): Promise<AudioResource[]> => {
  // Define default music files with their actual filenames
  const defaultFiles = [
    { id: -1, fileName: "Elegant Echo.mp3", name: "Elegant Echo" },
    { id: -2, fileName: "Soul Spring.mp3", name: "Soul Spring" },
    { id: -3, fileName: "Vibrant Flow.mp3", name: "Vibrant Flow" }
  ];
  
  console.log("Loading default background music files:", defaultFiles.map(f => f.fileName));
  
  // Use Promise.all to load durations in parallel
  return Promise.all(
    defaultFiles.map(async (file) => {
      const url = `/audio/background/default/${file.fileName}`;
      let duration = 180; // Default duration if we can't determine
      
      // Try to get actual duration
      try {
        if (typeof window !== 'undefined') {
          duration = await getAudioDuration(url);
          console.log(`Loaded duration for ${file.fileName}: ${duration}s`);
        }
      } catch (error) {
        console.warn(`Could not determine duration for ${file.fileName}`, error);
      }
      
      return {
        id: file.id,
        userId: -1, // Use negative userId to identify default resources
        name: file.name,
        type: 'background' as const,
        url,
        duration,
        createdAt: new Date()
      };
    })
  );
};

export const useMusicStore = create<MusicStore>()(
  persist(
    (set, get) => ({
      audioResources: [],
      currentAudioId: -1,
      isLoading: false,
      isUploading: false,
      error: null,
      
      // Fetch all audio resources from API
      fetchAudioResources: async () => {
        try {
          set({ isLoading: true, error: null });
          const response = await fetch('/api/video-editing/audio-resources');
          if (!response.ok) {
            throw new Error('Failed to fetch audio resources');
          }
          const resources = await response.json();
          
          // Get default resources with durations
          const defaultMusic = await loadDefaultMusic();
          
          // Merge API resources with default background music
          set({ audioResources: [...resources, ...defaultMusic] });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
        } finally {
          set({ isLoading: false });
        }
      },
      
      // Fetch audio resources by type from API
      fetchAudioResourcesByType: async (type) => {
        try {
          set({ isLoading: true, error: null });
          const response = await fetch(`/api/video-editing/audio-resources?type=${type}`);
          if (!response.ok) {
            throw new Error('Failed to fetch audio resources');
          }
          const resources = await response.json();
          
          // Only include default resources if fetching background music
          let mergedResources = resources;
          if (type === 'background') {
            const defaultMusic = await loadDefaultMusic();
            mergedResources = [...resources, ...defaultMusic];
          }
          
          set({ audioResources: mergedResources });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
        } finally {
          set({ isLoading: false });
        }
      },
      
      // Add a new audio resource through API
      addAudioResource: async (resourceData) => {
        try {
          set({ isLoading: true, error: null });
          const response = await fetch('/api/video-editing/audio-resources', {
            method: 'POST',
            body: JSON.stringify(resourceData),
            headers: {
              'Content-Type': 'application/json',
            },
          });
          
          if (!response.ok) {
            throw new Error('Failed to add audio resource');
          }
          
          const newResource = await response.json();
          set(state => ({
            audioResources: [...state.audioResources, newResource],
            currentAudioId: state.currentAudioId === null ? newResource.id : state.currentAudioId
          }));
          
          return newResource;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },
      
      // Add audio resource from file upload
      addAudioResourceFromFile: async (file, type) => {
        try {
          set({ isUploading: true, error: null });
          
          // Calculate duration before uploading
          const objectUrl = URL.createObjectURL(file);
          const duration = await getAudioDuration(objectUrl);
          URL.revokeObjectURL(objectUrl);
          
          const formData = new FormData();
          formData.append('file', file);
          formData.append('name', file.name);
          formData.append('type', type);
          formData.append('duration', duration.toString());
          
          const response = await fetch('/api/video-editing/audio-resources', {
            method: 'POST',
            body: formData,
          });
          
          if (!response.ok) {
            throw new Error('Failed to upload audio file');
          }
          
          const newResource = await response.json();
          set(state => ({
            audioResources: [...state.audioResources, newResource],
            currentAudioId: state.currentAudioId === null ? newResource.id : state.currentAudioId
          }));
          
          return newResource;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          throw error;
        } finally {
          set({ isUploading: false });
        }
      },
      
      // Add audio resource from ElevenLabs
      addAudioResourceFromElevenLabs: async (text, gender, name) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await fetch('/api/video-editing/elevenlabs', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              text,
              gender,
              name,
            }),
          });
          
          if (!response.ok) {
            throw new Error('Failed to generate speech');
          }
          
          const newResource = await response.json();
          set(state => ({
            audioResources: [...state.audioResources, newResource],
            currentAudioId: state.currentAudioId === null ? newResource.id : state.currentAudioId
          }));
          
          return newResource;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },
      
      // Remove audio resource through API
      removeAudioResource: async (id) => {
        try {
          // Prevent removing default resources (those with negative IDs)
          if (id < 0) {
            console.warn("Attempted to remove a default audio resource");
            return;
          }
          
          set({ isLoading: true, error: null });
          const response = await fetch(`/api/video-editing/audio-resources/${id}`, {
            method: 'DELETE',
          });
          
          if (!response.ok) {
            throw new Error('Failed to remove audio resource');
          }
          
          set(state => ({
            audioResources: state.audioResources.filter(resource => resource.id !== id),
            currentAudioId: state.currentAudioId === id ? null : state.currentAudioId
          }));
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },
      
      // Update audio resource through API
      updateAudioResource: async (id, updates) => {
        try {
          set({ isLoading: true, error: null });
          const response = await fetch(`/api/video-editing/audio-resources/${id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(updates),
          });
          
          if (!response.ok) {
            throw new Error('Failed to update audio resource');
          }
          
          const updatedResource = await response.json();
          set(state => ({
            audioResources: state.audioResources.map(resource => 
              resource.id === id ? updatedResource : resource
            )
          }));
          
          return updatedResource;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },
      
      // Set the currently selected audio
      setCurrentAudio: (id) => {
        set({ currentAudioId: id });
      },
      
      // Get audio resources by type
      getAudioByType: (type) => {
        return get().audioResources.filter(resource => resource.type === type);
      },
      
      // Get the currently selected audio resource
      getCurrentAudio: () => {
        const { currentAudioId, audioResources } = get();
        if (!currentAudioId) return null;
        return audioResources.find(resource => resource.id === currentAudioId) || null;
      }
    }),
    {
      name: 'audio-resources-storage',
      partialize: (state) => ({
        currentAudioId: state.currentAudioId,
        audioResources: state.audioResources,
      }),
    }
  )
);
