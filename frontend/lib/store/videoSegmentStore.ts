import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// Used to track segment requests that are already in progress
const pendingRequests = new Set<string>();

// Track pending batch fetch requests by projectId to prevent duplicate calls
const pendingBatchRequests = new Map<string, Promise<void>>();

// VideoSegment interface definition
export interface VideoSegment {
  id: string;
  scriptId: string;
  version: number;
  isCurrentVersion: boolean;
  generationId?: string;
  videoUrl: string | null;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  completedAt?: string;
  
  // Optional fields that may not be present in all API responses
  projectId?: string;
  scriptText?: string;
  narratorText?: string | null;
  narratorAudio?: string | null;
  audioId?: string | null;
  thumbnailUrl?: string | null;
  error?: string | null;
  updatedAt?: string;
}

// Response type for API calls that wrap segment in a message
interface SegmentResponse {
  message?: string;
  segment?: VideoSegment;
}

// VideoSegment store interface definition
interface VideoSegmentState {
  // state
  segments: VideoSegment[];
  currentSegment: VideoSegment | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isGenerating: boolean;
  isPolling: boolean;
  pollingSegmentId: string | null;
  pollingInterval: NodeJS.Timeout | null;
  error: string | null;
  
  // Cache for segments by scriptId to avoid unnecessary API calls
  scriptSegmentsMap: Record<string, { data: VideoSegment[], timestamp: number }>;
  
  // actions
  fetchSegments: (scriptId: string, projectId?: string, includeAllVersions?: boolean) => Promise<void>;
  fetchSegment: (id: string) => Promise<void>;
  createSegment: (segment: {
    scriptId: string;
    projectId: string;
    scriptText: string;
    narratorText?: string;
  }) => Promise<VideoSegment | null>;
  updateSegment: (id: string, data: Partial<VideoSegment>, createNewVersion?: boolean) => Promise<void>;
  deleteSegment: (id: string) => Promise<void>;
  generateSegment: (scriptId: string) => Promise<VideoSegment | null>;
  setCurrentSegment: (segment: VideoSegment | null) => void;
  startPolling: (segmentId: string) => void;
  stopPolling: () => void;
  clearError: () => void;
  
  // Cache management methods
  clearScriptCache: (scriptId: string) => void;
  clearAllCache: () => void;
}

// Helper function to extract segment from API response
const extractSegment = (data: any): VideoSegment => {
  // Check if the response is wrapped in a message object
  if (data && data.segment) {
    console.log('API returned segment in wrapper:', data);
    return data.segment;
  }
  
  // Otherwise, assume the data is the segment itself
  console.log('API returned direct segment:', data);
  return data;
};

// create VideoSegmentStore
export const useVideoSegmentStore = create<VideoSegmentState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        segments: [] as VideoSegment[],
        currentSegment: null,
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        isGenerating: false,
        isPolling: false,
        pollingSegmentId: null,
        pollingInterval: null,
        error: null,
        
        // Initialize cache map
        scriptSegmentsMap: {},
        
        // fetch segments
        fetchSegments: async (scriptId, projectId, includeAllVersions = false) => {
          // Create a request key for deduplication
          const requestKey = scriptId || projectId || 'unknown';
          
          // If fetching by scriptId, check cache first
          if (scriptId) {
            const cachedData = get().scriptSegmentsMap[scriptId];
            const now = Date.now();
            const cacheValidityPeriod = 15 * 60 * 1000; // 15 minutes cache validity - extended to reduce API calls
            
            if (cachedData && (now - cachedData.timestamp < cacheValidityPeriod)) {
              console.log(`[videoSegmentStore] fetchSegments: Using cached segments for scriptId: ${scriptId}`);
              set({ 
                segments: cachedData.data,
                isLoading: false 
              });
              return;
            }
            
            // Check if there's already a pending request for this script
            if (pendingRequests.has(requestKey)) {
              console.log(`[videoSegmentStore] fetchSegments: Request already in progress for ${requestKey}, skipping`);
              return;
            }
          }
          
          // For project-based requests, check pending batch requests
          if (projectId && pendingBatchRequests.has(projectId)) {
            console.log(`[videoSegmentStore] fetchSegments: Batch request already in progress for project ${projectId}, waiting`);
            await pendingBatchRequests.get(projectId);
            return;
          }
          
          // Add to pending requests
          pendingRequests.add(requestKey);
          
          // Only set isLoading to true if we need to fetch from API
          set({ isLoading: true, error: null });
          
          try {
            let url = '/api/video-editing/segments?';
            
            if (scriptId) {
              url += `scriptId=${scriptId}`;
            } else if (projectId) {
              url += `projectId=${projectId}`;
            } else {
              throw new Error('Either scriptId or projectId is required');
            }
            
            if (includeAllVersions) {
              url += '&includeAllVersions=true';
            }
            
            const response = await fetch(url);
            if (!response.ok) {
              throw new Error('Failed to fetch segments');
            }
            const responseData = await response.json();
            
            // Process the response data
            let segments: VideoSegment[];
            
            if (Array.isArray(responseData)) {
              segments = responseData;
            } else if (responseData.segments) {
              segments = responseData.segments;
            } else {
              console.error('Unexpected API response format for segments:', responseData);
              segments = [];
            }
            
            // Update cache with timestamp
            if (scriptId) {
              set(state => ({ 
                segments: segments, 
                scriptSegmentsMap: {
                  ...state.scriptSegmentsMap,
                  [scriptId]: { data: segments, timestamp: Date.now() }
                },
                isLoading: false 
              }));
            } else {
              set({ segments: segments, isLoading: false });
            }
          } catch (error) {
            console.error('Error fetching segments:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to fetch segments', 
              isLoading: false 
            });
          } finally {
            // Always remove from pending requests when done
            pendingRequests.delete(requestKey);
            if (projectId) {
              pendingBatchRequests.delete(projectId);
            }
          }
        },
        
        // get a single segment
        fetchSegment: async (id) => {
          // if request is already in progress, skip
          if (pendingRequests.has(id)) {
            console.log(`fetchSegment: ${id} - Request already in progress, skipping`);
            return;
          }
          
          // add to pending requests
          pendingRequests.add(id);
          
          set({ isLoading: true, error: null });
          try {
            console.log(`fetchSegment: ${id}`);
            const response = await fetch(`/api/video-editing/segments/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch segment details');
            }
            const responseData = await response.json();
            
            // Extract segment from response
            const segmentData = extractSegment(responseData);
            
            // Update current segment
            set({ currentSegment: segmentData, isLoading: false });
            
            // Update in segments array if exists
            set(state => ({
              segments: state.segments.map(s => s.id === id ? segmentData : s)
            }));
            
            // Update in cache if this segment exists in any cached script
            set(state => {
              const updatedCache = { ...state.scriptSegmentsMap };
              const scriptId = segmentData.scriptId;
              
              if (updatedCache[scriptId]) {
                const cachedData = updatedCache[scriptId];
                updatedCache[scriptId] = {
                  data: cachedData.data.map((s: VideoSegment) => 
                    s.id === id ? segmentData : s
                  ),
                  timestamp: cachedData.timestamp
                };
              }
              
              return { scriptSegmentsMap: updatedCache };
            });
          } catch (error) {
            console.error('Error fetching segment:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to fetch segment details', 
              isLoading: false 
            });
          } finally {
            // remove from pending requests
            pendingRequests.delete(id);
          }
        },
        
        // create a new segment
        createSegment: async (segment) => {
          set({ isCreating: true, error: null });
          try {
            const response = await fetch('/api/video-editing/segments', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(segment),
            });
            
            if (!response.ok) {
              throw new Error('Failed to create segment');
            }
            
            const responseData = await response.json();
            const newSegment = extractSegment(responseData);
            
            set(state => ({ 
              segments: [...state.segments, newSegment],
              currentSegment: newSegment,
              isCreating: false 
            }));
            
            // Clear cache for this script to ensure fresh data on next fetch
            get().clearScriptCache(segment.scriptId);
            
            return newSegment;
          } catch (error) {
            console.error('Error creating segment:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to create segment', 
              isCreating: false 
            });
            return null;
          }
        },
        
        // update a segment
        updateSegment: async (id, data, createNewVersion = false) => {
          set({ isUpdating: true, error: null });
          try {
            const response = await fetch(`/api/video-editing/segments/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...data,
                createNewVersion
              }),
            });
            
            if (!response.ok) {
              throw new Error('Failed to update segment');
            }
            
            const responseData = await response.json();
            const updatedSegment = extractSegment(responseData);
            
            set(state => {
              // If we created a new version, we need to add it to the segments array
              // and potentially remove the old version from the current view
              if (createNewVersion) {
                // Clear cache for this script to ensure fresh data on next fetch
                get().clearScriptCache(updatedSegment.scriptId);
                
                return { 
                  segments: [...state.segments.filter(s => s.id !== id), updatedSegment],
                  currentSegment: state.currentSegment?.id === id ? updatedSegment : state.currentSegment,
                  isUpdating: false 
                };
              } else {
                // Update in cache if this segment exists in any cached script
                const updatedCache = { ...state.scriptSegmentsMap };
                const scriptId = updatedSegment.scriptId;
                
                if (updatedCache[scriptId]) {
                  const cachedData = updatedCache[scriptId];
                  updatedCache[scriptId] = {
                    data: cachedData.data.map((s: VideoSegment) => 
                      s.id === id ? updatedSegment : s
                    ),
                    timestamp: cachedData.timestamp
                  };
                }
                
                return { 
                  segments: state.segments.map((s: VideoSegment) => s.id === id ? updatedSegment : s),
                  currentSegment: state.currentSegment?.id === id ? updatedSegment : state.currentSegment,
                  scriptSegmentsMap: updatedCache,
                  isUpdating: false 
                };
              }
            });
          } catch (error) {
            console.error('Error updating segment:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to update segment', 
              isUpdating: false 
            });
          }
        },
        
        // delete a segment
        deleteSegment: async (id) => {
          set({ isDeleting: true, error: null });
          try {
            const response = await fetch(`/api/video-editing/segments/${id}`, {
              method: 'DELETE',
            });
            
            if (!response.ok) {
              throw new Error('Failed to delete segment');
            }
            
            // Get the scriptId before removing the segment
            const segmentToDelete = get().segments.find(s => s.id === id);
            const scriptId = segmentToDelete?.scriptId;
            
            set(state => ({ 
              segments: state.segments.filter(s => s.id !== id),
              currentSegment: state.currentSegment?.id === id ? null : state.currentSegment,
              isDeleting: false 
            }));
            
            // Clear cache for this script if we found the scriptId
            if (scriptId) {
              get().clearScriptCache(scriptId);
            }
          } catch (error) {
            console.error('Error deleting segment:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to delete segment', 
              isDeleting: false 
            });
          }
        },
        
        // generate a segment from a script
        generateSegment: async (scriptId) => {
          set({ isGenerating: true, error: null });
          try {
            const response = await fetch('/api/video-editing/segments/generate', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                scriptId
              }),
            });
            
            if (!response.ok) {
              throw new Error('Failed to generate segment');
            }
            
            const responseData = await response.json();
            
            // Extract segment from response which might be wrapped in a message object
            const segmentData = extractSegment(responseData);
            
            // Validate data before using it
            if (!segmentData || !segmentData.id) {
              console.error('Invalid segment data received:', segmentData);
              throw new Error('Invalid segment data received');
            }
            
            set(state => ({ 
              segments: [...state.segments, segmentData],
              currentSegment: segmentData,
              isGenerating: false 
            }));
            
            // Clear cache for this script to ensure fresh data on next fetch
            get().clearScriptCache(scriptId);
            
            // Only start polling if we have a valid segment ID
            console.log(`Starting polling for new segment: ${segmentData.id}`);
            get().startPolling(segmentData.id);
            
            return segmentData;
          } catch (error) {
            console.error('Error generating segment:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to generate segment', 
              isGenerating: false 
            });
            return null;
          }
        },
        
        // set current segment
        setCurrentSegment: (segment) => {
          set({ currentSegment: segment });
        },
        
        // start polling for segment status updates
        startPolling: (segmentId) => {
          // Validate segmentId
          if (!segmentId) {
            console.error('startPolling called with invalid segmentId:', segmentId);
            return;
          }
          
          // Stop any existing polling
          get().stopPolling();
          
          console.log(`Starting polling for segment: ${segmentId}`);
          
          // Set polling state
          set({ 
            isPolling: true, 
            pollingSegmentId: segmentId 
          });
          
          // Create polling interval
          const interval = setInterval(async () => {
            // Get the current polling ID at the beginning of each interval
            const currentPollingId = get().pollingSegmentId;
            
            // If no valid polling ID, stop polling
            if (!currentPollingId) {
              console.log('Polling stopped because segmentId is no longer valid');
              get().stopPolling();
              return;
            }
            
            try {
              // Skip if another request for this segment is already in progress
              if (pendingRequests.has(currentPollingId)) {
                console.log(`Polling: ${currentPollingId} - Request already in progress, skipping this poll`);
                return;
              }
              
              console.log(`Polling segment: ${currentPollingId}`);
              
              // Add to pending requests
              pendingRequests.add(currentPollingId);
              
              const response = await fetch(`/api/video-editing/segments/${currentPollingId}`);
              if (!response.ok) {
                throw new Error(`Failed to poll segment status for ID: ${currentPollingId}`);
              }
              
              const responseData = await response.json();
              
              // Extract segment from response
              const segmentData = extractSegment(responseData);
              
              // Update segment in state
              set(state => ({
                segments: state.segments.map(s => s.id === currentPollingId ? segmentData : s),
                currentSegment: state.currentSegment?.id === currentPollingId ? segmentData : state.currentSegment
              }));
              
              // Update in cache if this segment exists in any cached script
              set(state => {
                const updatedCache = { ...state.scriptSegmentsMap };
                const scriptId = segmentData.scriptId;
                
                if (updatedCache[scriptId]) {
                  const cachedData = updatedCache[scriptId];
                  updatedCache[scriptId] = {
                    data: cachedData.data.map((s: VideoSegment) => 
                      s.id === currentPollingId ? segmentData : s
                    ),
                    timestamp: cachedData.timestamp
                  };
                }
                
                return { scriptSegmentsMap: updatedCache };
              });
              
              // If segment is completed or failed, stop polling
              if (segmentData.status === 'completed' || segmentData.status === 'failed') {
                console.log(`Segment ${currentPollingId} status is ${segmentData.status}, stopping polling`);
                get().stopPolling();
              }
            } catch (error) {
              console.error('Error polling segment status:', error);
              // Don't stop polling on error, just log it
            } finally {
              // Remove from pending requests
              pendingRequests.delete(currentPollingId);
            }
          }, 3000); // Poll every 3 seconds
          
          // Save interval reference
          set({ pollingInterval: interval });
        },
        
        // stop polling
        stopPolling: () => {
          const { pollingInterval } = get();
          if (pollingInterval) {
            console.log('Stopping polling');
            clearInterval(pollingInterval);
            set({ 
              isPolling: false, 
              pollingSegmentId: null, 
              pollingInterval: null 
            });
          }
        },
        
        // clear error
        clearError: () => {
          set({ error: null });
        },
        
        // Cache management methods
        clearScriptCache: (scriptId) => {
          console.log(`[videoSegmentStore] Clearing cache for scriptId: ${scriptId}`);
          set(state => {
            const newMap = { ...state.scriptSegmentsMap };
            delete newMap[scriptId];
            return { scriptSegmentsMap: newMap };
          });
        },
        
        clearAllCache: () => {
          console.log('[videoSegmentStore] Clearing all segment cache');
          set({ scriptSegmentsMap: {} });
        }
      }),
      {
        name: 'video-segment-storage',
        partialize: (state) => ({ 
          currentSegment: state.currentSegment,
          scriptSegmentsMap: state.scriptSegmentsMap
        }),
      }
    )
  )
);
