# Store Architecture

This document describes the store architecture used in this project. This architecture has been redesigned to improve maintainability, reduce complexity, and make the codebase easier to extend.

## Core Principles

1. **Modular Stores**: Each functional area has its own store (video, display, audio)
2. **Coordinator Pattern**: Complex operations involving multiple stores are handled by a coordinator
3. **Unified API Access**: All API requests go through a central service
4. **Hook-based Interface**: Components interact with stores through custom hooks

## Store Organization

### Display Store (`displayStore.ts`)

Combined store for managing both intro and end screen settings. Includes backward compatibility layers for smooth transition from the old architecture.

```typescript
// Example usage
import { useDisplayStore } from '@/lib/store/displayStore';

// Access the store directly
const { screens, updateIntroScreen } = useDisplayStore();

// Use the backward compatibility layer
import { useIntroScreenStore } from '@/lib/store/displayStore';
const { settings, setBackgroundColor } = useIntroScreenStore();
```

### Video Stores (`videoScriptStore.ts` and `videoSegmentStore.ts`)

Stores for managing video scripts and segments. These remain separate but are coordinated through the StoreCoordinator.

### Audio Store (`musicStore.ts`)

Store for managing audio resources like background music, voice over, and sound effects.

## Coordinator Pattern

The StoreCoordinator handles complex operations that involve multiple stores.

```typescript
// Example usage
import { storeCoordinator } from '@/lib/services/storeCoordinator';

// Fetch all current version segments
const segments = await storeCoordinator.fetchAllCurrentVersionSegments(projectId);

// Initialize the editor
const { scripts, segments } = await storeCoordinator.initializeEditor(projectId);
```

## API Service

Centralized service for all API requests.

```typescript
// Example usage
import { apiService } from '@/lib/services/api';

// Fetch scripts
const scripts = await apiService.scripts.fetch(projectId);

// Generate a segment
const segment = await apiService.segments.generate(scriptId);
```

## Custom Hooks

Custom hooks provide a simplified interface for components to interact with stores.

### `useVideoEditor`

Provides a simplified interface for the video editing functionality.

```typescript
// Example usage
import { useVideoEditor } from '@/lib/hooks/useVideoEditor';

const { 
  currentScriptId,
  currentSegmentId,
  currentVersionSegments,
  handleScriptSelect,
  handleSegmentSelect,
  fetchAllCurrentVersionSegments,
  createTestSegment
} = useVideoEditor(projectId);
```

### `useDisplaySettings`

Provides a simplified interface for working with display settings.

```typescript
// Example usage
import { useDisplaySettings } from '@/lib/hooks/useDisplaySettings';

const {
  introSettings,
  endSettings,
  updateIntroBackgroundColor,
  updateEndCenterText,
  toggleIntroScreen
} = useDisplaySettings();
```

## Best Practices

1. **Prefer hooks over direct store access** in components
2. **Use the coordinator** for operations that involve multiple stores
3. **Use the API service** for all API requests
4. **Add new functionality** to the appropriate store or coordinator

## Migration Guide

When updating existing components:

1. Replace direct store imports with the appropriate hook
2. Replace store operations with hook methods
3. Test thoroughly to ensure behavior is preserved

## Future Improvements

1. Migrate remaining stores to this architecture
2. Add more utility hooks for common operations
3. Further optimize performance by selectively subscribing to store updates 