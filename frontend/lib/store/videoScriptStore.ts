import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// used to track requests that are already in progress
const pendingRequests = new Map<string, Promise<any>>();

// Track script generation requests to prevent duplicates
const scriptGenerationLocks = new Set<string>();

// VideoScript interface definition
export interface VideoScript {
  id: string;
  projectId: string;
  segmentNumber: number;
  version: number;
  isCurrentVersion: boolean;
  scriptText: string;
  narratorText: string | null;
  phrase: string | null;
  narratorAudio: string | null;
  narratorAudioMale: string | null;
  narratorAudioFemale: string | null;
  audioId: string | null;
  defaultVoice: string | null;
  elevenlabsVoiceIds: any | null;
  status: 'pending' | 'processing' | 'completed';
  textOverlays: any | null;
  createdAt: string;
  updatedAt: string;
}

// VideoScript store interface definition
interface VideoScriptState {
  // state
  scripts: VideoScript[];
  currentScript: VideoScript | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isGenerating: boolean;
  error: string | null;
  
  // Cache for scripts by projectId to avoid unnecessary API calls
  projectScriptsMap: Record<string, { data: VideoScript[], timestamp: number }>;
  
  // actions
  fetchScripts: (projectId: string, includeAllVersions: boolean) => Promise<void>;
  fetchScript: (id: string) => Promise<void>;
  createScript: (script: {
    projectId: string;
    segmentNumber: number;
    scriptText: string;
    narratorText?: string;
    phrase?: string;
    defaultVoice?: string;
  }) => Promise<VideoScript | null>;
  updateScript: (id: string, data: Partial<VideoScript>, createNewVersion?: boolean) => Promise<void>;
  deleteScript: (id: string) => Promise<void>;
  generateScripts: (projectId: string, segmentCount?: number) => Promise<VideoScript[]>;
  setCurrentScript: (script: VideoScript | null) => void;
  clearError: () => void;
  
  // Cache management methods
  clearProjectCache: (projectId: string) => void;
  clearAllCache: () => void;
}

// create VideoScriptStore
export const useVideoScriptStore = create<VideoScriptState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        scripts: [],
        currentScript: null,
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        isGenerating: false,
        error: null,
        
        // Initialize cache map
        projectScriptsMap: {},
        
        // fetch all scripts for a project
        // if includeAllVersions is true, all versions of the script will be returned
        fetchScripts: async (projectId, includeAllVersions = false) => {
          // Check if we have cached data
          const cachedData = get().projectScriptsMap[projectId];
          const now = Date.now();
          const cacheValidityPeriod = 5 * 60 * 1000; // 5 minutes cache validity
          
          // If cache exists, is not expired, and we don't need all versions
          if (
            !includeAllVersions && 
            cachedData && 
            (now - cachedData.timestamp < cacheValidityPeriod)
          ) {
            console.log(`[videoScriptStore] fetchScripts: Using cached scripts for projectId: ${projectId}`);
            set({ 
              scripts: cachedData.data,
              isLoading: false 
            });
            return;
          }
          
          // check if request is already in progress
          const requestKey = `${projectId}_${includeAllVersions ? 'all' : 'current'}`;
          if (pendingRequests.has(requestKey)) {
            console.log(`[videoScriptStore] fetchScripts: Request already in progress, reusing existing request - projectId: ${projectId}`);
            await pendingRequests.get(requestKey);
            return;
          }
          
          set({ isLoading: true, error: null });
          
          // create request Promise
          const requestPromise = (async () => {
            try {
              console.log(`[videoScriptStore] fetchScripts: Initiating new request - projectId: ${projectId}`);
              const response = await fetch(`/api/video-editing/scripts?projectId=${projectId}${includeAllVersions ? '&includeAllVersions=true' : ''}`);
              if (!response.ok) {
                throw new Error('Failed to fetch scripts');
              }
              const data = await response.json();
              
              // Update cache with timestamp
              if (!includeAllVersions) {
                set(state => ({ 
                  scripts: data, 
                  projectScriptsMap: {
                    ...state.projectScriptsMap,
                    [projectId]: { data, timestamp: Date.now() }
                  },
                  isLoading: false 
                }));
              } else {
                set({ scripts: data, isLoading: false });
              }
            } catch (error) {
              console.error('Error fetching scripts:', error);
              set({ 
                error: error instanceof Error ? error.message : 'Failed to fetch scripts', 
                isLoading: false 
              });
            } finally {
              // when request is complete, remove from Map
              pendingRequests.delete(requestKey);
            }
          })();
          
          // store request Promise in Map
          pendingRequests.set(requestKey, requestPromise);
          
          await requestPromise;
        },
        
        // get a single script
        fetchScript: async (id) => {
          set({ isLoading: true, error: null });
          try {
            const response = await fetch(`/api/video-editing/scripts/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch script details');
            }
            const data = await response.json();
            set({ currentScript: data, isLoading: false });
          } catch (error) {
            console.error('Error fetching script:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to fetch script details', 
              isLoading: false 
            });
          }
        },
        
        // create a new script
        createScript: async (script) => {
          set({ isCreating: true, error: null });
          try {
            const response = await fetch('/api/video-editing/scripts', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(script),
            });
            
            if (!response.ok) {
              throw new Error('Failed to create script');
            }
            
            const newScript = await response.json();
            set(state => ({ 
              scripts: [...state.scripts, newScript],
              currentScript: newScript,
              isCreating: false 
            }));
            
            // Clear cache for this project to ensure fresh data on next fetch
            get().clearProjectCache(script.projectId);
            
            return newScript;
          } catch (error) {
            console.error('Error creating script:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to create script', 
              isCreating: false 
            });
            return null;
          }
        },
        
        // update a script
        updateScript: async (id, data, createNewVersion = false) => {
          set({ isUpdating: true, error: null });
          
          try {
            const response = await fetch(`/api/video-editing/scripts/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...data,
                createNewVersion
              }),
            });
            
            if (!response.ok) {
              throw new Error('Failed to update script');
            }
            
            const updatedScript = await response.json();
            
            // Get the projectId before updating state
            const projectId = updatedScript.projectId;
            
            set(state => {
              // If we created a new version, we need to add it to the scripts array
              // and potentially remove the old version from the current view
              if (createNewVersion) {
                return { 
                  scripts: [...state.scripts.filter(s => s.id !== id), updatedScript],
                  currentScript: state.currentScript?.id === id ? updatedScript : state.currentScript,
                  isUpdating: false 
                };
              } else {
                return { 
                  scripts: state.scripts.map(s => s.id === id ? updatedScript : s),
                  currentScript: state.currentScript?.id === id ? updatedScript : state.currentScript,
                  isUpdating: false 
                };
              }
            });
            
            // Clear cache for this project to ensure fresh data on next fetch
            if (projectId) {
              get().clearProjectCache(projectId);
            }
          } catch (error) {
            console.error('Error updating script:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to update script', 
              isUpdating: false 
            });
          }
        },
        
        // delete a script
        deleteScript: async (id) => {
          set({ isDeleting: true, error: null });
          try {
            // Get the script to be deleted to get its projectId
            const scriptToDelete = get().scripts.find(s => s.id === id);
            const projectId = scriptToDelete?.projectId;
            
            const response = await fetch(`/api/video-editing/scripts/${id}`, {
              method: 'DELETE',
            });
            
            if (!response.ok) {
              throw new Error('Failed to delete script');
            }
            
            set(state => ({ 
              scripts: state.scripts.filter(s => s.id !== id),
              currentScript: state.currentScript?.id === id ? null : state.currentScript,
              isDeleting: false 
            }));
            
            // Clear cache for this project if we found the projectId
            if (projectId) {
              get().clearProjectCache(projectId);
            }
          } catch (error) {
            console.error('Error deleting script:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to delete script', 
              isDeleting: false 
            });
          }
        },
        
        // generate scripts for a project
        // if segmentCount is not provided, the API will calculate it based on project.adLength
        generateScripts: async (projectId, segmentCount) => {
          // Check if this project is already being processed
          if (scriptGenerationLocks.has(projectId)) {
            console.log(`[videoScriptStore] generateScripts: Request already in progress for project: ${projectId}. Skipping duplicate call.`);
            
            // Return existing scripts to avoid empty state
            return get().scripts;
          }
          
          try {
            // Add project to lock set
            scriptGenerationLocks.add(projectId);
            console.log(`[videoScriptStore] generateScripts: Acquired lock for project: ${projectId}`);
            
            set({ isGenerating: true, error: null });
            
            const response = await fetch('/api/video-editing/scripts/generate', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                projectId,
                segmentCount
              }),
            });
            
            if (!response.ok) {
              throw new Error('Failed to generate scripts');
            }
            
            const data = await response.json();
            set(state => ({ 
              scripts: data,
              isGenerating: false 
            }));
            
            // Clear cache for this project to ensure fresh data on next fetch
            get().clearProjectCache(projectId);
            
            return data as VideoScript[];
          } catch (error) {
            console.error('Error generating scripts:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to generate scripts', 
              isGenerating: false 
            });
            return [] as VideoScript[];
          } finally {
            // Release the lock regardless of success or failure
            scriptGenerationLocks.delete(projectId);
            console.log(`[videoScriptStore] generateScripts: Released lock for project: ${projectId}`);
          }
        },
        
        // set current script
        setCurrentScript: (script) => {
          set({ currentScript: script });
        },
        
        // clear error
        clearError: () => {
          set({ error: null });
        },
        
        // Cache management methods
        clearProjectCache: (projectId) => {
          console.log(`[videoScriptStore] Clearing cache for projectId: ${projectId}`);
          set(state => {
            const newMap = { ...state.projectScriptsMap };
            delete newMap[projectId];
            return { projectScriptsMap: newMap };
          });
        },
        
        clearAllCache: () => {
          console.log('[videoScriptStore] Clearing all script cache');
          set({ projectScriptsMap: {} });
        },
      }),
      {
        name: 'video-script-storage',
        partialize: (state) => ({ 
          currentScript: state.currentScript,
          projectScriptsMap: state.projectScriptsMap
        }),
      }
    )
  )
);