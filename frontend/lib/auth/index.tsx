'use client';

import { createContext, useContext, ReactNode } from 'react';
import { User } from '@/lib/db/schema';

// Update UserContextType to handle potentially undefined userPromise
type UserContextType = {
  userPromise: Promise<User | null> | null;
};

// Create a default context value with null promise
const defaultContextValue: UserContextType = {
  userPromise: null
};

// Initialize context with default value instead of null
const UserContext = createContext<UserContextType>(defaultContextValue);

export function useUser(): UserContextType {
  const context = useContext(UserContext);
  // No need to throw error as we have a default value
  return context;
}

export function UserProvider({
  children,
  userPromise
}: {
  children: ReactNode;
  userPromise: Promise<User | null>;
}) {
  return (
    <UserContext.Provider value={{ userPromise }}>
      {children}
    </UserContext.Provider>
  );
}
