import { compare, hash } from 'bcryptjs';
import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import { NewUser } from '@/lib/db/schema';

const key = new TextEncoder().encode(process.env.AUTH_SECRET);
const SALT_ROUNDS = 10;

export async function hashPassword(password: string) {
  return hash(password, SALT_ROUNDS);
}

export async function comparePasswords(
  plainTextPassword: string,
  hashedPassword: string
) {
  return compare(plainTextPassword, hashedPassword);
}

type SessionData = {
  user: { id: string };
  expires: string;
};

export async function signToken(payload: SessionData) {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('14 days from now')
    .sign(key);
}

export async function verifyToken(input: string) {
  const { payload } = await jwtVerify(input, key, {
    algorithms: ['HS256'],
  });
  return payload as SessionData;
}

export async function getSession() {
  const session = (await cookies()).get('session')?.value;
  if (!session) return null;
  return await verifyToken(session);
}

export async function getSessionToken() {
  const sessionCookie = (await cookies()).get('session')?.value;
  return sessionCookie || null;
}

export async function setSession(user: NewUser) {
  const expiresInFourteenDays = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000);

  const session: SessionData = {
    user: { id: user.id! },
    expires: expiresInFourteenDays.toISOString(),
  };
  const encryptedSession = await signToken(session);
  
  // Determine if we're in a secure environment (HTTPS) or not
  // For EC2 without HTTPS, we need to set secure: false to allow cookies to be set
  const isSecureEnvironment = process.env.NODE_ENV === 'production' && 
                             !process.env.ALLOW_INSECURE_COOKIES;
  
  (await cookies()).set('session', encryptedSession, {
    expires: expiresInFourteenDays,
    httpOnly: true,
    secure: isSecureEnvironment, // Only set secure flag in production with HTTPS
    sameSite: 'lax',
  });
}
