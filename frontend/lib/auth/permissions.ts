import { usageTracking, users, User } from '../db/schema';
import { eq, and, gte, sql } from 'drizzle-orm';
import { db } from '../db/drizzle';

// Define credit costs for different operations
export const CREDIT_COSTS = {
  // Audience insight generation
  GENERATE_AUDIENCE_INSIGHT: 25,
  
  // Video campaign - per second
  VIDEO_CAMPAIGN_PER_SECOND: 10,
  
  // Script generation costs by model
  SCRIPT_GENERATION: {
    'gpt-4.1': 10,
  },
  
  // Audio generation - per 10 words
  AUDIO_GENERATION: 1,
  
  // Minimum credits threshold
  MINIMUM_REQUIRED_CREDITS: 10
};

// Define user capabilities regardless of plan/subscription type
export interface UserCapabilities {
  // Whether the user has permission to generate scripts
  canGenerateScripts: boolean;
  
  // List of AI models the user is allowed to use
  allowedModels: string[];
}

// Define the capabilities for each user type/plan
// In a pure credit system, this maps user types to their allowed features
// but doesn't enforce usage limits (which are handled by credits)
export const USER_CAPABILITIES: Record<string, UserCapabilities> = {
  'Basic': {
    canGenerateScripts: true,
    allowedModels: ['gpt-4.1'],
  },
  'Standard': {
    canGenerateScripts: true,
    allowedModels: ['gpt-4.1'],
  },
  'Pro': {
    canGenerateScripts: true,
    allowedModels: ['gpt-4.1', 'gpt-4.1'],
  },
};

/**
 * Get the capabilities for a specific user based on their type/plan
 * This only affects permission access, not usage limits (which are controlled by credits)
 */
export function getUserCapabilities(user: User | null): UserCapabilities {
  if (!user) {
    return {
      canGenerateScripts: false,
      allowedModels: [ ],
    };
  }
  
  return {
    canGenerateScripts: true,
    allowedModels: ['gpt-4.1', 'gpt-4.1'],
  };
}

/**
 * Check if a user has permission to generate scripts
 * This is a capability check, not a credit check
 */
export function canGenerateScripts(user: User | null): boolean {
  return getUserCapabilities(user).canGenerateScripts;
}

/**
 * Get the allowed AI models for a user
 * Different models consume different amounts of credits
 */
export function getAllowedModels(user: User | null): string[] {
  return getUserCapabilities(user).allowedModels;
}

/**
 * Check if user has enough credits for an operation
 * This is the primary limit in the credit-based system
 */
export async function hasEnoughCredits(userId: string, requiredCredits: number): Promise<boolean> {
  const userResult = await db
    .select({ credits: users.credits })
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);
  
  if (userResult.length === 0) {
    return false; // User not found
  }
  
  const userCredits = userResult[0].credits || 0;
  return userCredits >= requiredCredits;
}

/**
 * Deduct credits from user account
 * Returns true if successful, false if insufficient credits
 */
export async function deductCredits(userId: string, creditsToDeduct: number, feature: string = 'credits_consumption'): Promise<boolean> {
  // First check if user has enough credits
  if (!(await hasEnoughCredits(userId, creditsToDeduct))) {
    return false;
  }
  
  // Deduct credits
  await db
    .update(users)
    .set({ 
      credits: sql`credits - ${creditsToDeduct}`,
      updatedAt: new Date()
    })
    .where(eq(users.id, userId));
  
  // Record credit consumption
  await db.insert(usageTracking).values({
    userId,
    feature,
    count: creditsToDeduct,
    resetDate: new Date(9999, 11, 31), // Credits consumption doesn't need reset
  });
  
  return true;
}

/**
 * Calculate credits required for video campaign based on duration
 */
export function calculateVideoCampaignCredits(durationInSeconds: number): number {
  return durationInSeconds * CREDIT_COSTS.VIDEO_CAMPAIGN_PER_SECOND;
}

/**
 * Calculate credits required for audio generation based on word count
 * Cost is per 10 words, rounded up to ensure integer credit value
 */
export function calculateAudioGenerationCredits(wordCount: number): number {
  // Calculate credits based on 1 credit per 10 words, rounded up
  return Math.ceil(wordCount / 10) * CREDIT_COSTS.AUDIO_GENERATION;
}

/**
 * Get user's current credit balance
 */
export async function getUserCreditsBalance(userId: string): Promise<number> {
  const userResult = await db
    .select({ credits: users.credits })
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);
  
  if (userResult.length === 0) {
    return 0;
  }
  
  return userResult[0].credits || 0;
}

/**
 * Increment the usage count for a specific feature
 * This is kept for analytics purposes only, not for enforcing limits
 */
export async function incrementUsageCount(userId: string, feature: string): Promise<void> {
  // Get the current month's first day (for reset date)
  const today = new Date();
  const firstDayOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
  
  // Check if there's an existing record for this month
  const usageResult = await db
    .select()
    .from(usageTracking)
    .where(
      and(
        eq(usageTracking.userId, userId),
        eq(usageTracking.feature, feature),
        gte(usageTracking.resetDate, new Date(today.getFullYear(), today.getMonth(), 1))
      )
    )
    .limit(1);
  
  if (usageResult.length === 0) {
    // Create a new usage record
    await db.insert(usageTracking).values({
      userId,
      feature,
      count: 1,
      resetDate: firstDayOfNextMonth,
    });
  } else {
    // Update the existing record
    await db
      .update(usageTracking)
      .set({ 
        count: usageResult[0].count + 1,
        updatedAt: new Date()
      })
      .where(eq(usageTracking.id, usageResult[0].id));
  }
}

/**
 * Get the OpenAI API key if the user has permission to use it
 * @deprecated This function is no longer used and should be removed.
 * API keys should be accessed directly via process.env in the service layer
 * to prevent potential security issues.
 */
export function getOpenAIApiKey(user: User | null): string | null {
  console.warn('getOpenAIApiKey is deprecated and should not be used');
  
  // 更严格的检查，防止意外使用
  return null;
}