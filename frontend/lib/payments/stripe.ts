import Stripe from 'stripe';
import { redirect } from 'next/navigation';
import { Team, User,planCredits, PlanKey, users } from '@/lib/db/schema';
import {
  getTeamByStripeCustomerId,
  getUser,
  updateTeamSubscription,
  getUserByStripeCustomerId,
  updateUserSubscription
} from '@/lib/db/queries';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia'
});

// Flag to configure whether to use user-level subscriptions
// Can be set to true after complete migration
export const USE_USER_LEVEL_SUBSCRIPTIONS = true;

export async function createCheckoutSession({
  team,
  priceId,
  userId
}: {
  team: Team | null;
  priceId: string;
  userId: string;
}) {
  if (!team) {
    redirect(`/sign-up?redirect=checkout&priceId=${priceId}`);
  }

  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1
      }
    ],
    mode: 'subscription',
    success_url: `${process.env.BASE_URL}/api/stripe/checkout?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.BASE_URL}/pricing`,
    customer: team.stripeCustomerId || undefined,
    client_reference_id: userId,
    allow_promotion_codes: true,
    subscription_data: {
      trial_period_days: 14
    }
  });

  redirect(session.url!);
}

// 新增：用户级别的结账会话创建
export async function createUserCheckoutSession({
  user,
  priceId,
  userId
}: {
  user: User | null;
  priceId: string;
  userId: string;
}) {
  if (!user) {
    redirect(`/sign-up?redirect=checkout&priceId=${priceId}`);
  }

  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1
      }
    ],
    mode: 'subscription',
    success_url: `${process.env.BASE_URL}/api/stripe/user-checkout?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.BASE_URL}/pricing`,
    customer: user.stripeCustomerId || undefined,
    client_reference_id: userId,
    allow_promotion_codes: true,
    subscription_data: {
      trial_period_days: 14
    }
  });

  redirect(session.url!);
}

export async function createCustomerPortalSession(team: Team) {
  if (!team.stripeCustomerId || !team.stripeProductId) {
    redirect('/pricing');
  }

  let configuration: Stripe.BillingPortal.Configuration;
  const configurations = await stripe.billingPortal.configurations.list();

  if (configurations.data.length > 0) {
    configuration = configurations.data[0];
  } else {
    const product = await stripe.products.retrieve(team.stripeProductId);
    if (!product.active) {
      throw new Error("Team's product is not active in Stripe");
    }

    const prices = await stripe.prices.list({
      product: product.id,
      active: true
    });
    if (prices.data.length === 0) {
      throw new Error("No active prices found for the team's product");
    }

    configuration = await stripe.billingPortal.configurations.create({
      business_profile: {
        headline: 'Manage your subscription'
      },
      features: {
        subscription_update: {
          enabled: true,
          default_allowed_updates: ['price', 'quantity', 'promotion_code'],
          proration_behavior: 'create_prorations',
          products: [
            {
              product: product.id,
              prices: prices.data.map((price) => price.id)
            }
          ]
        },
        payment_method_update: {
          enabled: true
        },
        subscription_cancel: {
          enabled: true,
          mode: 'at_period_end',
          cancellation_reason: {
            enabled: true,
            options: [
              'too_expensive',
              'missing_features',
              'switched_service',
              'unused',
              'other'
            ]
          }
        }
      }
    });
  }

  return stripe.billingPortal.sessions.create({
    customer: team.stripeCustomerId,
    return_url: `${process.env.BASE_URL}/dashboard`,
    configuration: configuration.id
  });
}

// 新增：用户级别的客户门户会话创建
export async function createUserCustomerPortalSession(user: User) {
  if (!user.stripeCustomerId || !user.stripeProductId) {
    redirect('/pricing');
  }

  let configuration: Stripe.BillingPortal.Configuration;
  const configurations = await stripe.billingPortal.configurations.list();

  if (configurations.data.length > 0) {
    configuration = configurations.data[0];
  } else {
    const product = await stripe.products.retrieve(user.stripeProductId);
    if (!product.active) {
      throw new Error("User's product is not active in Stripe");
    }

    const prices = await stripe.prices.list({
      product: product.id,
      active: true
    });
    if (prices.data.length === 0) {
      throw new Error("No active prices found for the user's product");
    }

    configuration = await stripe.billingPortal.configurations.create({
      business_profile: {
        headline: 'Manage your subscription'
      },
      features: {
        subscription_update: {
          enabled: true,
          default_allowed_updates: ['price', 'promotion_code'],
          proration_behavior: 'create_prorations',
          products: [
            {
              product: product.id,
              prices: prices.data.map((price) => price.id)
            }
          ]
        },
        subscription_cancel: {
          enabled: true,
          mode: 'at_period_end',
          cancellation_reason: {
            enabled: true,
            options: [
              'too_expensive',
              'missing_features',
              'switched_service',
              'unused',
              'other'
            ]
          }
        }
      }
    });
  }

  return stripe.billingPortal.sessions.create({
    customer: user.stripeCustomerId,
    return_url: `${process.env.BASE_URL}/dashboard`,
    configuration: configuration.id
  });
}

export async function handleSubscriptionChange(
  subscription: Stripe.Subscription
) {
  const customerId = subscription.customer as string;
  const subscriptionId = subscription.id;
  const status = subscription.status;

  // 首先尝试查找团队
  const team = await getTeamByStripeCustomerId(customerId);
  if (team) {
    // 如果找到团队，更新团队订阅
    if (status === 'active'  ) {
      const plan = subscription.items.data[0]?.plan;
      const rawPlanName = (plan.product as Stripe.Product).name; 
      const normalizedPlanName = rawPlanName.toLowerCase() as PlanKey;
      await updateTeamSubscription(team.id, {
        stripeSubscriptionId: subscriptionId,
        stripeProductId: plan?.product as string,
        planName: (plan?.product as Stripe.Product).name,
        subscriptionStatus: status,
        credits: planCredits[normalizedPlanName] || 0
      });
    }else if (status === 'trialing') {
      const plan = subscription.items.data[0]?.plan;
      await updateTeamSubscription(team.id, {
        stripeSubscriptionId: subscriptionId,
        stripeProductId: plan?.product as string,
        planName: (plan?.product as Stripe.Product).name,
        subscriptionStatus: status,
        credits: planCredits['freeTrial'] || 0
      });
    }else if (status === 'canceled' || status === 'unpaid') {
      await updateTeamSubscription(team.id, {
        stripeSubscriptionId: null,
        stripeProductId: null,
        planName: null,
        subscriptionStatus: status,
        credits: null
      });
    }
  } else {
    // 如果没有找到团队，尝试查找用户
    const user = await getUserByStripeCustomerId(customerId);
    
    if (user) {
      // 如果找到用户，更新用户订阅
      if (status === 'active'  ) {
        const plan = subscription.items.data[0]?.plan;
        const rawPlanName = (plan.product as Stripe.Product).name; 
        const normalizedPlanName = rawPlanName.toLowerCase() as PlanKey;
        await updateUserSubscription(user.id, {
          stripeSubscriptionId: subscriptionId,
          stripeProductId: plan?.product as string,
          planName: (plan?.product as Stripe.Product).name,
          subscriptionStatus: status,
          credits: planCredits[normalizedPlanName] || 0
        });
      }else if (status === 'trialing') {
        const plan = subscription.items.data[0]?.plan;
        await updateUserSubscription(user.id, {
          stripeSubscriptionId: subscriptionId,
          stripeProductId: plan?.product as string,
          planName: (plan?.product as Stripe.Product).name,
          subscriptionStatus: status,
          credits: planCredits['freeTrial'] || 0
        });
      }else if (status === 'canceled' || status === 'unpaid') {
        await updateUserSubscription(user.id, {
          stripeSubscriptionId: null,
          stripeProductId: null,
          planName: null,
          subscriptionStatus: status,
          credits: null
        });
      }
    } else {
      console.error('Neither team nor user found for Stripe customer:', customerId);
    }
  }
}

// 新增：专门处理用户订阅变更的函数
export async function handleUserSubscriptionChange(
  subscription: Stripe.Subscription
) {
  const customerId = subscription.customer as string;
  const subscriptionId = subscription.id;
  const status = subscription.status;

  const user = await getUserByStripeCustomerId(customerId);

  if (!user) {
    console.error('User not found for Stripe customer:', customerId);
    return;
  }

  if (status === 'active' || status === 'trialing') {
    const plan = subscription.items.data[0]?.plan;
    const rawPlanName = (plan.product as Stripe.Product).name; 
    const normalizedPlanName = rawPlanName.toLowerCase() as PlanKey;
    const product = await stripe.products.retrieve(plan?.product as string);
    // console.log("--------------------------------------")
    // console.log({
    //   user: user.id,
    //   status: subscription.status,
    //   planName: product.name,
    //   customerId: customerId,
    //   subscriptionId: subscriptionId,
    //   product: product

    // })
    await updateUserSubscription(user.id, {
      stripeSubscriptionId: subscriptionId,
      stripeProductId: plan?.product as string,
      planName: product.name,
      subscriptionStatus: status,
      credits: planCredits[normalizedPlanName] || 0
    });
  } else if (status === 'canceled' || status === 'unpaid') {
    await updateUserSubscription(user.id, {
      stripeSubscriptionId: null,
      stripeProductId: null,
      planName: null,
      subscriptionStatus: status,
      credits: null
    });
  }
}

export async function getStripePrices() {
  const prices = await stripe.prices.list({
    expand: ['data.product'],
    active: true,
    type: 'recurring'
  });

  return prices.data.map((price) => ({
    id: price.id,
    productId:
      typeof price.product === 'string' ? price.product : price.product.id,
    unitAmount: price.unit_amount,
    currency: price.currency,
    interval: price.recurring?.interval,
    trialPeriodDays: price.recurring?.trial_period_days,
  }));
}

export async function getStripeProducts() {
  const products = await stripe.products.list({
    active: true,
    expand: ['data.default_price']
  });

  return products.data.map((product) => ({
    id: product.id,
    name: product.name,
    description: product.description,
    defaultPriceId:
      typeof product.default_price === 'string'
        ? product.default_price
        : product.default_price?.id
  }));
}

// 智能处理结账会话创建的函数
export async function createSmartCheckoutSession({
  entity,
  priceId,
  userId
}: {
  entity: Team | User | null;
  priceId: string;
  userId: string;
}) {
  if (USE_USER_LEVEL_SUBSCRIPTIONS) {
    // 使用用户级别订阅
    return createUserCheckoutSession({
      user: entity as User,
      priceId,
      userId
    });
  } else {
    // 使用团队级别订阅
    return createCheckoutSession({
      team: entity as Team,
      priceId,
      userId
    });
  }
}

// 智能处理客户门户会话创建的函数
export async function createSmartCustomerPortalSession(entity: Team | User) {
  if (USE_USER_LEVEL_SUBSCRIPTIONS) {
    // 使用用户级别订阅
    return createUserCustomerPortalSession(entity as User);
  } else {
    // 使用团队级别订阅
    return createCustomerPortalSession(entity as Team);
  }
}
