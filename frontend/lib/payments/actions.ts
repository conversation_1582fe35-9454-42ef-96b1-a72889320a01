'use server';

import { redirect } from 'next/navigation';
import { 
  createSmartCheckoutSession, 
  createSmartCustomerPortalSession 
} from './stripe';
import { getUser, getSmartEntity } from '@/lib/db/queries';

// Handle checkout process using Server Action
export async function checkoutAction(formData: FormData) {
  const priceId = formData.get('priceId') as string;
  const user = await getUser();
  
  if (!user) {
    redirect('/sign-in');
  }
  
  const entity = await getSmartEntity(user);
  
  if (!entity) {
    throw new Error('Unable to find user or team entity');
  }
  
  await createSmartCheckoutSession({ 
    entity: entity, 
    priceId: priceId,
    userId: user.id.toString()
  });
}

// Handle customer portal process using Server Action
export async function customerPortalAction() {
  const user = await getUser();
  
  if (!user) {
    redirect('/login');
  }
  
  const entity = await getSmartEntity(user);
  
  if (!entity) {
    throw new Error('Unable to find user or team entity');
  }
  
  const portalSession = await createSmartCustomerPortalSession(entity);
  redirect(portalSession.url);
}
