/**
 * ConcurrencyManager - A utility to manage concurrent operations
 * 
 * This class helps control the number of concurrent operations that can be performed,
 * which is especially useful for external API calls that have rate limiting or
 * concurrent request limitations.
 */
export class ConcurrencyManager {
  private maxConcurrentOperations: number;
  private runningOperations: number = 0;
  private queue: Array<{
    task: () => Promise<any>;
    resolve: (value: any) => void;
    reject: (reason: any) => void;
  }> = [];
  private name: string;

  /**
   * Create a new ConcurrencyManager
   * 
   * @param maxConcurrentOperations Maximum number of operations that can run simultaneously
   * @param name Optional name for debugging purposes
   */
  constructor(maxConcurrentOperations: number, name: string = 'default') {
    this.maxConcurrentOperations = maxConcurrentOperations;
    this.name = name;
  }

  /**
   * Enqueue an operation to be executed when resources are available
   * 
   * @param task Function that returns a promise
   * @returns Promise that resolves with the result of the task
   */
  enqueue<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      // Add task to queue
      this.queue.push({
        task,
        resolve,
        reject
      });
      
      // Process queue
      this.processQueue();
    });
  }

  /**
   * Process the queue of operations
   */
  private processQueue(): void {
    // If we're at or over capacity, or queue is empty, don't process
    if (this.runningOperations >= this.maxConcurrentOperations || this.queue.length === 0) {
      return;
    }

    // Get next task
    const nextTask = this.queue.shift();
    if (!nextTask) return;

    // Increment running operations counter
    this.runningOperations++;
    
    console.log(`[ConcurrencyManager:${this.name}] Starting operation (${this.runningOperations}/${this.maxConcurrentOperations} running, ${this.queue.length} in queue)`);

    // Execute task
    nextTask.task()
      .then(result => {
        nextTask.resolve(result);
        return result;
      })
      .catch(err => {
        nextTask.reject(err);
        return err;
      })
      .finally(() => {
        // Decrement running operations counter
        this.runningOperations--;
        
        console.log(`[ConcurrencyManager:${this.name}] Completed operation (${this.runningOperations}/${this.maxConcurrentOperations} running, ${this.queue.length} in queue)`);
        
        // Process next task in queue
        this.processQueue();
      });
  }

  /**
   * Get the current number of running operations
   */
  getRunningOperationsCount(): number {
    return this.runningOperations;
  }

  /**
   * Get the current queue length
   */
  getQueueLength(): number {
    return this.queue.length;
  }

  /**
   * Get the manager's status as a string
   */
  getStatus(): string {
    return `${this.runningOperations}/${this.maxConcurrentOperations} running, ${this.queue.length} in queue`;
  }

  /**
   * Set a new maximum concurrent operations limit
   * 
   * @param maxConcurrentOperations New maximum number of operations
   */
  setMaxConcurrentOperations(maxConcurrentOperations: number): void {
    this.maxConcurrentOperations = maxConcurrentOperations;
    
    // Process queue in case new capacity is available
    this.processQueue();
  }
} 