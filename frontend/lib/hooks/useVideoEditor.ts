import { useState, useCallback, useEffect } from 'react';
import { useVideoScriptStore } from '@/lib/store/videoScriptStore';
import { useVideoSegmentStore, VideoSegment } from '@/lib/store/videoSegmentStore';
import { storeCoordinator } from '@/lib/services/storeCoordinator';

/**
 * useVideoEditor Hook
 * 
 * Custom hook that provides a simplified interface for the video editing functionality
 * Handles coordination between video script and segment stores
 * 
 * @param projectIdParam - The project ID as a string or null
 * @returns Video editor state and methods
 */
export function useVideoEditor(projectIdParam: string | null) {
  // Convert project ID to number if present
  const projectId = projectIdParam ? projectIdParam : null;
  
  // Local state
  const [currentScriptId, setCurrentScriptId] = useState<string | null>(null);
  const [currentSegmentId, setCurrentSegmentId] = useState<string | null>(null);
  const [currentVersionSegments, setCurrentVersionSegments] = useState<VideoSegment[]>([]);
  const [isEditorInitialized, setIsEditorInitialized] = useState(false);
  const [isGeneratingSegments, setIsGeneratingSegments] = useState(false);
  // Track which segment IDs are currently being generated
  const [generatingSegmentIds, setGeneratingSegmentIds] = useState<string[]>([]);
  
  // Get store methods directly to avoid dependency issues
  const fetchScripts = useVideoScriptStore(state => state.fetchScripts);
  const generateScripts = useVideoScriptStore(state => state.generateScripts);
  const scripts = useVideoScriptStore(state => state.scripts);
  const isLoading = useVideoScriptStore(state => state.isLoading);
  const isGenerating = useVideoScriptStore(state => state.isGenerating);
  
  // Segment store methods
  const fetchSegments = useVideoSegmentStore(state => state.fetchSegments);
  const generateSegment = useVideoSegmentStore(state => state.generateSegment);
  const isSegmentGenerating = useVideoSegmentStore(state => state.isGenerating);
  // Get isPolling from useVideoSegmentStore to track polling status
  const isSegmentPolling = useVideoSegmentStore(state => state.isPolling);
  
  /**
   * Cleans up duplicate scripts for a project
   * This helps ensure the project has consistent data
   */
  const cleanupDuplicateScripts = useCallback(async () => {
    if (!projectId) return;
    
    try {
      console.log(`Cleaning up duplicate scripts for project: ${projectId}`);
      const response = await fetch('/api/video-editing/scripts/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to clean up scripts');
      }
      
      const result = await response.json();
      
      if (result.fixedCount > 0) {
        console.log(`Fixed ${result.fixedCount} duplicate scripts for project ${projectId}`);
        // Refresh scripts if we fixed any duplicates
        await fetchScripts(projectId, false);
      } else {
        console.log(`No duplicate scripts found for project ${projectId}`);
      }
      
      return result;
    } catch (error) {
      console.error('Error cleaning up duplicate scripts:', error);
    }
  }, [projectId, fetchScripts]);
  
  /**
   * Fetches all current version segments from all scripts
   */
  const fetchAllCurrentVersionSegments = useCallback(async () => {
    if (!projectId) return [];
    
    try {
      const segments = await storeCoordinator.fetchAllCurrentVersionSegments(projectId);
      setCurrentVersionSegments(segments);
      return segments;
    } catch (error) {
      console.error('Error fetching current version segments:', error);
      return [];
    }
  }, [projectId]);
  
  /**
   * Handles script selection
   * Fetches segments for the selected script and selects the current version
   */
  const handleScriptSelect = useCallback(async (scriptId: string) => {
    if (!projectId) return;
    
    console.log('Selecting script:', scriptId);
    setCurrentScriptId(scriptId);
    
    try {
      // 只获取segments数据但不自动设置currentSegmentId，避免重新加载视频
      await storeCoordinator.fetchAllCurrentVersionSegments(projectId);
      
      // 更新currentVersionSegments，但不更改当前播放状态
      const segments = await fetchAllCurrentVersionSegments();
      setCurrentVersionSegments(segments);
    } catch (error) {
      console.error('Error handling script selection:', error);
    }
  }, [projectId, fetchAllCurrentVersionSegments]);
  
  /**
   * Handles segment selection
   * If the segment belongs to a different script, updates the current script ID
   */
  const handleSegmentSelect = useCallback(async (segmentId: string, scriptId: string) => {
    console.log('Selecting segment:', segmentId, 'from script:', scriptId);
    setCurrentSegmentId(segmentId);
    
    // Update currentScriptId if it's different from the current one
    if (scriptId !== currentScriptId) {
      console.log('Updating current script ID:', scriptId);
      setCurrentScriptId(scriptId);
      
      // Ensure segments for this script are loaded
      try {
        if (projectId) {
          await fetchSegments(scriptId);
        }
      } catch (error) {
        console.error('Error fetching segments for script:', error);
      }
    }
  }, [currentScriptId, fetchSegments, projectId]);
  
  /**
   * Creates a test segment for the current script
   */
  const createTestSegment = useCallback(async () => {
    if (!currentScriptId) return null;
    
    try {
      console.log('Generating new segment for script:', currentScriptId);
      const segment = await generateSegment(currentScriptId);
      
      if (segment) {
        console.log('Generated segment:', segment.id);
        setCurrentSegmentId(segment.id);
        await fetchAllCurrentVersionSegments();
        return segment;
      }
      
      return null;
    } catch (error) {
      console.error('Error creating test segment:', error);
      return null;
    }
  }, [currentScriptId, generateSegment, fetchAllCurrentVersionSegments]);
  
  /**
   * Generates video segments for all scripts in the project
   * This will iterate through all scripts and create video segments for each one
   * The generation status will be tracked until all segments are fully processed
   */
  const generateAllSegments = useCallback(async () => {
    if (!projectId || !scripts || scripts.length === 0) return [];
    
    // Set generating state to show loading UI
    setIsGeneratingSegments(true);
    // Array to track new segment IDs that are being generated
    const newGeneratingIds: string[] = [];
    
    try {
      console.log(`Generating video segments for all ${scripts.length} scripts in project ${projectId}`);
      
      const generatedSegments = [];
      
      // Process each script in sequence
      for (const script of scripts) {
        console.log(`Generating segment for script ${script.id} (segment ${script.segmentNumber})`);
        
        try {
          // Generate segment for this script
          const segment = await generateSegment(script.id);
          
          if (segment) {
            generatedSegments.push(segment);
            // Track this segment for status monitoring
            newGeneratingIds.push(segment.id);
            console.log(`Generated segment ${segment.id} for script ${script.id}`);
          }
        } catch (error) {
          console.error(`Error generating segment for script ${script.id}:`, error);
          // Continue with next script even if one fails
        }
      }
      
      // Update the list of segments being generated
      setGeneratingSegmentIds(newGeneratingIds);
      
      // After all segments are generated, update the UI with the latest data
      await fetchAllCurrentVersionSegments();
      
      // If we have at least one successful segment, select it
      if (generatedSegments.length > 0) {
        const firstSegment = generatedSegments[0];
        setCurrentSegmentId(firstSegment.id);
        setCurrentScriptId(firstSegment.scriptId);
      }
      
      return generatedSegments;
    } catch (error) {
      console.error('Error generating segments for all scripts:', error);
      // Only reset in case of a catastrophic error
      setIsGeneratingSegments(false);
      return [];
    }
    // Removed the finally block - we'll let the polling mechanism
    // determine when to set isGeneratingSegments to false
  }, [projectId, scripts, generateSegment, fetchAllCurrentVersionSegments]);
  
  /**
   * Initializes the video editor with data for the project
   */
  const initializeEditor = useCallback(async () => {
    if (!projectId) return;
    
    try {
      console.log('Initializing editor for project:', projectId);
      
      // First, clean up any duplicate scripts
      await cleanupDuplicateScripts();
      
      const { scripts, segments, isGeneratingSegments: isGenSegments } = await storeCoordinator.initializeEditor(projectId);
      
      // Set generating segments state from response
      setIsGeneratingSegments(isGenSegments || false);
      
      // Always set currentVersionSegments, even if empty
      setCurrentVersionSegments(segments || []);
      
      // If we have segments, select the first one
      if (segments && segments.length > 0) {
        const firstSegment = segments[0];
        setCurrentSegmentId(firstSegment.id);
        setCurrentScriptId(firstSegment.scriptId);
      }
      // Otherwise, if we have scripts, select the first one
      else if (scripts && scripts.length > 0) {
        setCurrentScriptId(scripts[0].id);
      }
      
      setIsEditorInitialized(true);
      console.log('Editor initialized successfully');
    } catch (error) {
      console.error('Error initializing editor:', error);
    }
  }, [projectId, cleanupDuplicateScripts]);
  
  // Initialize the editor when the component mounts or projectId changes
  useEffect(() => {
    if (projectId && !isEditorInitialized) {
      initializeEditor();
    }
  }, [projectId, initializeEditor, isEditorInitialized]);
  
  // Watch for scripts changes and fetch segments when scripts become available
  useEffect(() => {
    if (projectId && scripts && scripts.length > 0 && currentVersionSegments.length === 0) {
      console.log('Scripts available, fetching segments...');
      fetchAllCurrentVersionSegments();
    }
  }, [projectId, scripts, currentVersionSegments.length, fetchAllCurrentVersionSegments]);
  
  // Monitor segment generation and polling state from the store
  useEffect(() => {
    // Start generation state when either segment generation begins or store is polling
    if (isSegmentGenerating || isSegmentPolling) {
      console.log(`[useVideoEditor] Setting generation active - isSegmentGenerating: ${isSegmentGenerating}, isSegmentPolling: ${isSegmentPolling}`);
      setIsGeneratingSegments(true);
    } else if (isGeneratingSegments && !isSegmentGenerating && !isSegmentPolling) {
      // Only stop generation state when both generation and polling are done
      // When generation and polling finish, wait a moment then fetch the segments
      console.log(`[useVideoEditor] Generation and polling complete, fetching segments before finalizing`);
      const timer = setTimeout(() => {
        fetchAllCurrentVersionSegments();
        // Note: We'll only set isGeneratingSegments to false after we've verified
        // all segments are complete in the polling mechanism below
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isSegmentGenerating, isSegmentPolling, isGeneratingSegments, fetchAllCurrentVersionSegments]);
  
  // Monitor segment status updates - simplified to rely on store polling
  useEffect(() => {
    // Only monitor status changes, don't create additional polling
    // The videoSegmentStore handles all polling internally
    if (!isGeneratingSegments && !isSegmentPolling && generatingSegmentIds.length > 0) {
      // Check if all tracked segments have completed
      const allCompleted = generatingSegmentIds.every(id => {
        const segment = currentVersionSegments.find(s => s.id === id);
        return segment && (segment.status === 'completed' || segment.status === 'failed');
      });
      
      if (allCompleted) {
        console.log('All segments have completed processing');
        setIsGeneratingSegments(false);
        setGeneratingSegmentIds([]);
        
        // Auto-select first completed segment if none is currently selected
        const completedSegment = currentVersionSegments.find(s => 
          generatingSegmentIds.includes(s.id) && s.status === 'completed' && s.videoUrl
        );
        
        if (completedSegment && !currentSegmentId) {
          console.log(`Auto-selecting completed segment: ${completedSegment.id}`);
          setCurrentSegmentId(completedSegment.id);
          setCurrentScriptId(completedSegment.scriptId);
        }
      }
    }
  }, [isGeneratingSegments, isSegmentPolling, generatingSegmentIds, currentVersionSegments, currentSegmentId]);
  
  return {
    // State
    currentScriptId,
    currentSegmentId,
    scripts,
    currentVersionSegments,
    isLoading,
    isGenerating,
    isInitialized: isEditorInitialized,
    isGeneratingSegments,
    // Also expose the isSegmentPolling state so components can use it
    isSegmentPolling,
    
    // Methods
    initializeEditor,
    handleScriptSelect,
    handleSegmentSelect,
    fetchAllCurrentVersionSegments,
    createTestSegment,
    generateAllSegments,
    cleanupDuplicateScripts,
    
    // Store methods (direct access if needed)
    fetchScripts,
    fetchSegments,
    generateSegment,
  };
} 