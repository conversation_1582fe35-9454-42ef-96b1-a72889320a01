import { useCallback, useEffect, useRef } from 'react';
import { useDisplayStore, ScreenSettings } from '../store/displayStore';

/**
 * useDisplaySettings Hook
 * 
 * Custom hook that provides a simplified interface for working with
 * display settings (intro and end screens)
 * 
 * @param projectId Optional project ID to link settings to database
 * @returns Methods and state for working with display settings
 */
export function useDisplaySettings(projectId?: string) {
  // Access the display store
  const { 
    screens, 
    currentProjectId,
    isLoading,
    error,
    updateIntroScreen, 
    updateEndScreen, 
    resetScreens,
    setCurrentProject,
    fetchProjectSettings,
    saveProjectSettings
  } = useDisplayStore();
  
  // Keep track of initialization
  const initialized = useRef(false);
  
  // Effect to load project settings when projectId changes
  useEffect(() => {
    // Skip if no projectId is provided (local mode only)
    if (!projectId) {
      // Only clear current project if it was previously set and we're switching to local mode
      if (currentProjectId) {
        setCurrentProject(null);
      }
      return;
    }
    
    // Skip if already initialized with this projectId
    if (initialized.current && projectId === currentProjectId) {
      return;
    }
    
    // Set project ID and fetch settings
    setCurrentProject(projectId);
    fetchProjectSettings(projectId);
    
    // Mark as initialized
    initialized.current = true;
  }, [projectId, currentProjectId]); // Only depend on the actual values, not the store methods
  
  // Get individual screen settings
  const introSettings = screens.intro;
  const endSettings = screens.end;
  
  /**
   * Update intro screen background color
   */
  const updateIntroBackgroundColor = useCallback((color: string) => {
    updateIntroScreen({ backgroundColor: color });
  }, [updateIntroScreen]);
  
  /**
   * Update intro screen logo
   */
  const updateIntroLogo = useCallback((updates: {
    url?: string;
    width?: number;
    height?: number;
    x?: number;
    y?: number;
  }) => {
    const { url, width, height, x, y } = updates;
    const updateData: Partial<ScreenSettings['intro']> = {};
    
    if (url !== undefined) updateData.logoUrl = url;
    if (width !== undefined) updateData.logoWidth = width;
    if (height !== undefined) updateData.logoHeight = height;
    if (x !== undefined) updateData.logoX = x;
    if (y !== undefined) updateData.logoY = y;
    
    updateIntroScreen(updateData);
  }, [updateIntroScreen]);
  
  /**
   * Toggle intro screen on/off
   */
  const toggleIntroScreen = useCallback((isEnabled?: boolean) => {
    updateIntroScreen({ 
      isEnabled: isEnabled !== undefined ? isEnabled : !introSettings.isEnabled 
    });
  }, [updateIntroScreen, introSettings.isEnabled]);
  
  /**
   * Update end screen background color
   */
  const updateEndBackgroundColor = useCallback((color: string) => {
    updateEndScreen({ backgroundColor: color });
  }, [updateEndScreen]);
  
  /**
   * Update end screen logo
   */
  const updateEndLogo = useCallback((updates: {
    url?: string;
    width?: number;
    height?: number;
    x?: number;
    y?: number;
  }) => {
    const { url, width, height, x, y } = updates;
    const updateData: Partial<ScreenSettings['end']> = {};
    
    if (url !== undefined) updateData.logoUrl = url;
    if (width !== undefined) updateData.logoWidth = width;
    if (height !== undefined) updateData.logoHeight = height;
    if (x !== undefined) updateData.logoX = x;
    if (y !== undefined) updateData.logoY = y;
    
    updateEndScreen(updateData);
  }, [updateEndScreen]);
  
  /**
   * Update end screen center text
   */
  const updateEndCenterText = useCallback((updates: {
    text?: string;
    color?: string;
    size?: number;
    x?: number;
    y?: number;
  }) => {
    const { text, color, size, x, y } = updates;
    const updateData: Partial<ScreenSettings['end']> = {};
    
    if (text !== undefined) updateData.centerText = text;
    if (color !== undefined) updateData.centerTextColor = color;
    if (size !== undefined) updateData.centerTextSize = size;
    if (x !== undefined) updateData.centerTextX = x;
    if (y !== undefined) updateData.centerTextY = y;
    
    updateEndScreen(updateData);
  }, [updateEndScreen]);
  
  /**
   * Update end screen bottom text
   */
  const updateEndBottomText = useCallback((updates: {
    text?: string;
    color?: string;
    size?: number;
    x?: number;
    y?: number;
  }) => {
    const { text, color, size, x, y } = updates;
    const updateData: Partial<ScreenSettings['end']> = {};
    
    if (text !== undefined) updateData.bottomText = text;
    if (color !== undefined) updateData.bottomTextColor = color;
    if (size !== undefined) updateData.bottomTextSize = size;
    if (x !== undefined) updateData.bottomTextX = x;
    if (y !== undefined) updateData.bottomTextY = y;
    
    updateEndScreen(updateData);
  }, [updateEndScreen]);
  
  /**
   * Toggle end screen on/off
   */
  const toggleEndScreen = useCallback((isEnabled?: boolean) => {
    updateEndScreen({ 
      isEnabled: isEnabled !== undefined ? isEnabled : !endSettings.isEnabled 
    });
  }, [updateEndScreen, endSettings.isEnabled]);
  
  /**
   * Reset both screens to default settings
   */
  const resetAllScreens = useCallback(() => {
    resetScreens();
  }, [resetScreens]);
  
  return {
    // State
    introSettings,
    endSettings,
    isLoading,
    error,
    currentProjectId,
    
    // Intro screen methods
    updateIntroBackgroundColor,
    updateIntroLogo,
    toggleIntroScreen,
    
    // End screen methods
    updateEndBackgroundColor,
    updateEndLogo,
    updateEndCenterText,
    updateEndBottomText,
    toggleEndScreen,
    
    // Common methods
    resetAllScreens,
    saveProjectSettings,
    
    // Direct access to store methods (for complex updates)
    updateIntroScreen,
    updateEndScreen,
  };
} 