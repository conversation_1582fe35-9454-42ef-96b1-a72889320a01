
  export interface Description {
    subTitle: string;
    description: string;
  }
  
  export interface Review {
    description: string;
    reviewer: string | null;
    link: string | null;
  }
  
  export interface ReviewGroup {
    subTitle: string;
    reviews: Review[];
  }
  
  export interface SubSection {
    id: string;
    subTitle: string;
    descriptions?: Description[];
    reviewGroups?: ReviewGroup[];
  }
  
  export interface Section {
    id: string;
    title: string;
    type: "tags" | "list" | "structured";
    data: string[] | SubSection[];
  }
  
  export interface TargetingResults {
    title: string;
    sections: Section[];
  }