/**
 * API Service
 * 
 * Centralized API client for all backend interactions.
 * Provides type-safe methods for each API endpoint.
 */
class ApiService {
  /**
   * Script-related API methods
   */
  scripts = {
    /**
     * Fetch scripts for a project
     * 
     * @param projectId The project ID to fetch scripts for
     * @param includeVersions Whether to include all versions
     * @returns Array of scripts
     */
    fetch: async (projectId: string, includeVersions = false) => {
      const url = `/api/video-editing/scripts?projectId=${projectId}${includeVersions ? '&includeVersions=true' : ''}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to fetch scripts');
      }
      
      return await response.json();
    },
    
    /**
     * Create a new script
     * 
     * @param data Script data
     * @returns Created script
     */
    create: async (data: any) => {
      const response = await fetch('/api/video-editing/scripts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to create script');
      }
      
      return await response.json();
    },
    
    /**
     * Update an existing script
     * 
     * @param id Script ID
     * @param data Script update data
     * @returns Updated script
     */
    update: async (id: number, data: any) => {
      const response = await fetch(`/api/video-editing/scripts/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to update script');
      }
      
      return await response.json();
    },
    
    /**
     * Generate scripts for a project
     * 
     * @param projectId The project ID to generate scripts for
     * @returns Array of generated scripts
     */
    generate: async (projectId: string) => {
      const response = await fetch('/api/video-editing/scripts/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate scripts');
      }
      
      return response.json();
    },
  };
  
  /**
   * Segment-related API methods
   */
  segments = {
    /**
     * Fetch segments for a script
     * 
     * @param scriptId The script ID to fetch segments for
     * @param includeAllVersions Whether to include all versions
     * @returns Array of segments
     */
    fetch: async (scriptId: number, includeAllVersions = false) => {
      const url = `/api/video-editing/segments?scriptId=${scriptId}${includeAllVersions ? '&includeAllVersions=true' : ''}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to fetch segments');
      }
      
      return await response.json();
    },
    
    /**
     * Fetch segments for a project
     * 
     * @param projectId The project ID to fetch segments for
     * @returns Array of segments
     */
    fetchByProject: async (projectId: string) => {
      const url = `/api/video-editing/segments?projectId=${projectId}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to fetch segments by project');
      }
      
      return await response.json();
    },
    
    /**
     * Fetch a single segment by ID
     * 
     * @param id Segment ID
     * @returns Segment data
     */
    fetchById: async (id: number) => {
      const response = await fetch(`/api/video-editing/segments/${id}`);
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to fetch segment');
      }
      
      return await response.json();
    },
    
    /**
     * Generate a segment for a script
     * 
     * @param scriptId The script ID to generate a segment for
     * @returns Generated segment
     */
    generate: async (scriptId: number) => {
      const response = await fetch(`/api/video-editing/segments/generate/${scriptId}`, { 
        method: 'POST' 
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to generate segment');
      }
      
      return await response.json();
    },
    
    /**
     * Create a new segment
     * 
     * @param data Segment data
     * @returns Created segment
     */
    create: async (data: any) => {
      const response = await fetch('/api/video-editing/segments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to create segment');
      }
      
      return await response.json();
    },
  };
  
  /**
   * Audio-related API methods
   */
  audio = {
    /**
     * Generate voice audio from text using ElevenLabs
     * 
     * @param text Text to convert to speech
     * @param voiceId Voice ID to use
     * @returns Audio data
     */
    generateVoice: async (text: string, voiceId: string) => {
      const response = await fetch('/api/video-editing/elevenlabs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text, voiceId }),
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(error.message || 'Failed to generate voice audio');
      }
      
      return await response.json();
    },
  };
}

// Export singleton instance
export const apiService = new ApiService(); 