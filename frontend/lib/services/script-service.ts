import { OpenAI } from 'openai';
import { 
  canGenerateScripts, 
  getAllowedModels,
  CREDIT_COSTS
} from '@/lib/auth/permissions';

/**
 * Script generation service
 * This service handles the core logic for generating scripts using OpenAI
 * It's extracted to be used both by the API route and internal server functions
 */
export async function generateScript({
  user,
  projectData,
  segmentNumber,
  requestedModel,
}: {
  user: any;
  projectData: {
    brandName: string;
    summary?: string | null;
    description?: string | null;
  };
  segmentNumber: number;
  requestedModel?: 'gpt-4.1' | 'gpt-4.1';
}) {
  // Validation for required data
  if (!projectData || !segmentNumber) {
    throw new Error('Missing required parameters');
  }

  // Check if user can generate scripts
  if (!canGenerateScripts(user)) {
    throw new Error('You do not have permission to generate scripts');
  }
  
  // Get allowed models for the user
  const allowedModels = getAllowedModels(user);
  
  // If a specific model was requested, verify that the user has access to it
  let model: 'gpt-4.1' | 'gpt-4.1' = 'gpt-4.1'; // Default model
  if (requestedModel) {
    if (allowedModels.includes(requestedModel) && (requestedModel === 'gpt-4.1' || requestedModel === 'gpt-4.1')) {
      model = requestedModel;
    } else {
      // If the requested model is not allowed, fall back to the best available
      model = allowedModels.includes('gpt-4.1') ? 'gpt-4.1' : 'gpt-4.1';
    }
  } else {
    // If no model specified, use the best available
    model = allowedModels.includes('gpt-4.1') ? 'gpt-4.1' : 'gpt-4.1';
  }

  // Calculate the cost for this script generation
  const creditCost = CREDIT_COSTS.SCRIPT_GENERATION[model];
  
  // 直接使用环境变量中的 API 密钥
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    throw new Error('API key not available');
  }

  const client = new OpenAI({
    apiKey: apiKey,
  });

  // System prompt for generating the script content
  const systemPrompt = `
  Generate a detailed script segment for a video advertisement. This segment should be EXACTLY 5 seconds long.

  For this segment, provide:
  1. A detailed visual description that includes:
  - Camera angle and movement (e.g., close-up, pan, zoom, tracking shot)
  - Lighting and atmosphere
  - Color scheme and visual tone
  - Specific object placement and movement
  - Actor expressions and movements (if present)

  2. A VERY concise narrator's voiceover text that:
  - Must be SHORT enough to be spoken clearly within 5 seconds
  - Should be around 8-12 words maximum
  - Uses clear, impactful language
  - Maintains a professional tone

  3. A short phrase that:
  - Must be EXACTLY 3 words or fewer
  - Is catchy and memorable
  - Embodies the essence of the brand or product
  - Uses strong, impactful English words

  Key requirements for the script:
  - Focus on a SINGLE scene or view for this 5-second segment
  - Include SPECIFIC camera directions
  - Describe the exact visual elements and their placement
  - Keep scene transitions minimal
  - Ensure the scene can be feasibly rendered in 5 seconds
  - Keep narrator text extremely concise and speakable within 5 seconds

  Advertisement Details:
  - Brand: ${projectData.brandName}
  - Summary: ${projectData.summary || 'N/A'}
  - Description: ${projectData.description || 'N/A'}

  Return format:
  {
    "script": "Dynamic close-up shot of a sleek smartwatch on a minimalist white surface. Sharp product lighting emphasizes the curved glass and premium metal finish. Camera smoothly rotates to reveal the vibrant display coming to life.",
    "narrator": "Elevate your fitness journey with SmartFit Pro.",
    "phrase": "Fit. Smart. Revolutionary."
  }
  `;

  try {
    // Call OpenAI API to generate script content
    const completion = await client.chat.completions.create({
      model: model,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: `Create an engaging 5-second video segment #${segmentNumber}` }
      ],
      temperature: 0.7,
    });

    const content = completion.choices[0].message.content;
    
    if (!content) {
      throw new Error('Failed to generate script: OpenAI returned empty content');
    }

    // Parse the returned JSON string
    let scriptData;
    try {
      // Try to parse the entire content
      scriptData = JSON.parse(content);
    } catch (e) {
      // If direct parsing fails, try to extract the JSON part
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        scriptData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('Unable to parse OpenAI response content');
      }
    }

    // Return the parsed data along with model and credit cost
    return {
      script: scriptData.script,
      narrator: scriptData.narrator,
      phrase: scriptData.phrase,
      model: model,
      creditCost: creditCost
    };
  } catch (error) {
    // Throw error to be handled by the caller
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error('Failed to generate script: ' + String(error));
    }
  }
} 