export interface LumaLabsGenerationRequest {
  prompt: string;
  aspect_ratio?: string;
  loop?: boolean;
  keyframes?: any;
  callback_url?: string;
  model?: string;
  resolution?: string;
  duration?: string;
}

// New interface for Image Generation requests
export interface LumaImageGenerationRequest {
  prompt: string;
  aspect_ratio?: string;
  model?: string;
  image_ref?: Array<{
    url: string;
    weight: number;
  }>;
  style_ref?: Array<{
    url: string;
    weight: number;
  }>;
  character_ref?: {
    identity0: {
      images: string[];
    };
  };
  callback_url?: string;
}

export interface LumaLabsGenerationResponse {
  id: string;
  generation_type?: string;
  status?: 'dreaming' | 'completed' | 'failed';
  state?: 'dreaming' | 'completed' | 'failed';
  url?: string;
  error?: string;
  failure_reason?: string;
  created_at?: string;
  assets?: {
    video?: string;
    image?: string;
    progress_video: string | null;
  };
  model?: string;
  request?: any;
}

export class LumaLabsService {
  private apiKey: string;
  private baseUrl: string = 'https://api.lumalabs.ai/dream-machine/v1';
  private mockMode: boolean = false; // Enable mock mode for testing and development
  private mockVideoUrl: string = 'https://storage.cdn-luma.com/dream-machine/af5d4efe-44cc-4252-b6e4-a15c479b4357/aaad5f3a-2381-41d5-aa10-6429c6a6cdce/2025-03-12T10-18-52_in_an_upscale_indoor_watermarked.mp4';
  private callbackUrlBase: string;

  constructor() {
    const apiKey = process.env.LUMA_API_KEY;
    if (!apiKey) {
      throw new Error('LUMA_API_KEY is not defined in environment variables');
    }
    this.apiKey = apiKey;
    
    // Use NEXT_PUBLIC_CALLBACK_URL_BASE if defined, otherwise fallback to default
    this.callbackUrlBase = process.env.CALLBACK_URL_BASE || '';
    
    // Log callback base URL for debugging
    console.log(`LumaLabsService initialized with callback URL base: ${this.callbackUrlBase || 'Not set (using relative URLs)'}`);
  }
  
  // Getter method for mockMode
  getMockMode(): boolean {
    return this.mockMode;
  }
  
  // Helper method to generate a full callback URL
  generateCallbackUrl(path: string): string {
    // If no base is set, return the relative path
    if (!this.callbackUrlBase) {
      return path;
    }
    
    // Ensure the base doesn't end with a slash and the path starts with a slash
    const base = this.callbackUrlBase.endsWith('/') 
      ? this.callbackUrlBase.slice(0, -1) 
      : this.callbackUrlBase;
      
    const formattedPath = path.startsWith('/') ? path : `/${path}`;
    
    return `${base}${formattedPath}`;
  }

  async createGeneration(params: LumaLabsGenerationRequest): Promise<LumaLabsGenerationResponse> {
    // If a callback URL is provided, modify it to use the configured base URL
    if (params.callback_url && this.callbackUrlBase) {
      // Extract just the path portion of the callback URL
      try {
        const url = new URL(params.callback_url, 'http://dummy-base');
        const path = url.pathname + url.search;
        params.callback_url = this.generateCallbackUrl(path);
        console.log(`Using modified callback URL: ${params.callback_url}`);
      } catch (error) {
        console.error(`Error parsing callback URL ${params.callback_url}:`, error);
      }
    }
    
    //  Mock mode implementation
    if (this.mockMode) {
      console.log('MOCK MODE: Simulating Luma Labs API createGeneration call');
      console.log('Request params:', JSON.stringify(params));
      
      // Generate a random ID
      const mockId = `mock-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      
      // If callback URL is provided, simulate callback after 5 seconds
      if (params.callback_url) {
        console.log(`MOCK MODE: Will call callback URL after 5 seconds: ${params.callback_url}`);
        
        // Use setTimeout to simulate async callback
        setTimeout(async () => {
          try {
            const callbackUrl = params.callback_url;
            // Ensure callback_url is not undefined
            if (!callbackUrl) return;
            
            console.log(`MOCK MODE: Calling callback URL: ${callbackUrl}`);
            
            // Mock callback request
            const response = await fetch(callbackUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                id: mockId,
                status: 'completed',
                url: this.mockVideoUrl
              })
            });
            
            if (response.ok) {
              console.log(`MOCK MODE: Callback successful for generation ${mockId}`);
            } else {
              console.error(`MOCK MODE: Callback failed for generation ${mockId}: ${response.statusText}`);
            }
          } catch (error) {
            console.error(`MOCK MODE: Error calling callback URL: ${error}`);
          }
        }, 5000);
      }
      
      // Return mock response
      return {
        id: mockId,
        status: 'dreaming', // Initial status is processing
      };
    }
    
    // Actual API call implementation
    try {
      const response = await fetch(`${this.baseUrl}/generations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        const errorData = await response.json();
        const error = new Error(`Luma Labs API error: ${errorData.message || response.statusText}`);
        (error as any).statusCode = response.status;
        (error as any).originalError = errorData;
        (error as any).requestParams = { ...params, prompt: params.prompt.substring(0, 100) + (params.prompt.length > 100 ? '...' : '') };
        throw error;
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating Luma Labs generation:', error);
      throw error;
    }
  }

  async getGeneration(generationId: string): Promise<LumaLabsGenerationResponse> {
    // Mock mode implementation
    if (this.mockMode) {
      console.log(`MOCK MODE: Simulating Luma Labs API getGeneration call for ID: ${generationId}`);
      
      // Check if ID starts with mock
      if (!generationId.startsWith('mock-')) {
        return {
          id: generationId,
          status: 'completed',
          url: this.mockVideoUrl
        };
      }
      
      // Extract timestamp from ID to simulate progress
      const timestampStr = generationId.split('-')[1];
      const timestamp = parseInt(timestampStr || '0');
      const elapsedTime = Date.now() - timestamp;
      
      // If 5 seconds have passed, return completed status
      if (elapsedTime > 5000) {
        return {
          id: generationId,
          status: 'completed',
          url: this.mockVideoUrl
        };
      }
      
      // Otherwise return processing status
      return {
        id: generationId,
        status: 'dreaming'
      };
    }
    
    // Actual API call implementation
    try {
      const response = await fetch(`${this.baseUrl}/generations/${generationId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        const error = new Error(`Luma Labs API error: ${errorData.message || response.statusText}`);
        (error as any).statusCode = response.status;
        (error as any).originalError = errorData;
        (error as any).generationId = generationId;
        throw error;
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching Luma Labs generation:', error);
      throw error;
    }
  }

  /**
   * Create an image generation using Luma Labs API
   * This method supports image references for product-aware generation
   * 
   * @param params - Image generation parameters including image references
   * @returns Promise<LumaLabsGenerationResponse>
   */
  async createImageGeneration(params: LumaImageGenerationRequest): Promise<LumaLabsGenerationResponse> {
    console.log('LumaLabsService: Creating image generation with params:', JSON.stringify(params, null, 2));
    
    // Mock mode implementation
    if (this.mockMode) {
      console.log('MOCK MODE: Simulating Luma Labs Image Generation API call');
      console.log('Image generation request params:', JSON.stringify(params));
      
      const mockId = `mock-img-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      
      return {
        id: mockId,
        generation_type: 'image',
        status: 'dreaming'
      };
    }

    // Real API implementation
    try {
      const response = await fetch(`${this.baseUrl}/generations/image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...params,
          generation_type: 'image'
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Image generation request failed: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Luma Labs Image Generation API request failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Image generation request successful:', result);
      return result;
    } catch (error) {
      console.error('Error creating image generation:', error);
      throw error;
    }
  }

  // New method: Wait for image generation to complete with polling
  async waitForImageGeneration(generationId: string, maxWaitTimeMs: number = 60000): Promise<string | null> {
    console.log(`[Image Generation] Waiting for completion of ${generationId}, max wait: ${maxWaitTimeMs}ms`);
    
    const startTime = Date.now();
    const pollInterval = 2000; // Poll every 2 seconds
    
    while (Date.now() - startTime < maxWaitTimeMs) {
      try {
        const generation = await this.getGeneration(generationId);
        
        console.log(`[Image Generation] Status check for ${generationId}: ${generation.status || generation.state}`);
        
        // Check for completion
        if (generation.status === 'completed' || generation.state === 'completed') {
          // For image generation, the URL is in assets.image field
          if (generation.assets?.image) {
            console.log(`[Image Generation] Completed successfully: ${generation.assets.image}`);
            return generation.assets.image;
          } else if (generation.url) {
            // Fallback to direct url field for backward compatibility
            console.log(`[Image Generation] Completed successfully (fallback): ${generation.url}`);
            return generation.url;
          } else {
            console.warn(`[Image Generation] Completed but no image URL returned`);
            return null;
          }
        }
        
        // Check for failure
        if (generation.status === 'failed' || generation.state === 'failed') {
          console.error(`[Image Generation] Failed: ${generation.error || 'Unknown error'}`);
          return null;
        }
        
        // Still processing, wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        
      } catch (error) {
        console.error(`[Image Generation] Error polling status:`, error);
        return null;
      }
    }
    
    console.warn(`[Image Generation] Timeout after ${maxWaitTimeMs}ms waiting for ${generationId}`);
    return null;
  }
}

// Create a singleton instance
export const lumaLabsService = new LumaLabsService();
