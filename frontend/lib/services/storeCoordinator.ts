import { useVideoScriptStore } from '@/lib/store/videoScriptStore';
import { useVideoSegmentStore, VideoSegment } from '@/lib/store/videoSegmentStore';

// Keep track of initialization in progress to prevent duplicates
const initializationInProgress = new Set<String>();

// Track pending segment fetch requests to prevent duplicate API calls
const pendingSegmentRequests = new Map<string, Promise<VideoSegment[]>>();

// Debug control - set to false in production to reduce console output
const DEBUG_ENABLED = process.env.NODE_ENV === 'development';

/**
 * StoreCoordinator
 * 
 * Handles cross-store communication and complex operations
 * that involve multiple stores. This centralizes logic that 
 * would otherwise be duplicated across components.
 */
class StoreCoordinator {
  /**
   * Fetches all current version segments from all scripts in a project
   * Used for both the preview and timeline display
   * 
   * @param projectId The project ID to fetch segments for
   * @returns Array of current version segments ordered by script sequence
   */
  async fetchAllCurrentVersionSegments(projectId: string): Promise<VideoSegment[]> {
    if (!projectId) return [];
    
    // Check if there's already a pending request for this project
    if (pendingSegmentRequests.has(projectId)) {
      if (DEBUG_ENABLED) {
        console.log(`[StoreCoordinator] Request deduplication: Using existing request for project ${projectId}`);
      }
      return pendingSegmentRequests.get(projectId)!;
    }
    
    // Create new request and store it in pending requests
    const requestPromise = this._fetchAllCurrentVersionSegmentsInternal(projectId);
    pendingSegmentRequests.set(projectId, requestPromise);
    
    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Remove from pending requests when done
      pendingSegmentRequests.delete(projectId);
    }
  }
  
  /**
   * Internal implementation of fetchAllCurrentVersionSegments
   * This is separated to allow for request deduplication
   */
  private async _fetchAllCurrentVersionSegmentsInternal(projectId: string): Promise<VideoSegment[]> {
    try {
      // Get script store state and fetch scripts
      const scriptStore = useVideoScriptStore.getState();
      if (!scriptStore) {
        console.error('Script store not available');
        return [];
      }
      
      await scriptStore.fetchScripts(projectId, false);
      const scripts = Array.isArray(scriptStore.scripts) ? scriptStore.scripts : [];
      
      if (scripts.length === 0) {
        console.log('No scripts found for project:', projectId);
        return [];
      }
      
      // Get segment store for accessing segments
      const segmentStore = useVideoSegmentStore.getState();
      if (!segmentStore) {
        console.error('Segment store not available');
        return [];
      }
      
      const allCurrentVersionSegments: VideoSegment[] = [];
      
      // Loop through each script to get its current version segment
      for (const script of scripts) {
        // Check if we have cached segments for this script
        const cachedSegments = segmentStore.scriptSegmentsMap[script.id]?.data;
        
        if (!cachedSegments) {
          // If no cached segments, fetch them
          await segmentStore.fetchSegments(script.id);
        }
        
        // Get segments from the updated cache after fetching
        const scriptSegments = segmentStore.scriptSegmentsMap[script.id]?.data || [];
        
        // Find the current version segment for this script
        const currentVersionSegment = scriptSegments.find(segment => segment.isCurrentVersion);
        
        if (currentVersionSegment) {
          allCurrentVersionSegments.push(currentVersionSegment);
        }
      }
      
      // Sort segments by script order
      allCurrentVersionSegments.sort((a, b) => {
        const scriptA = scripts.find(s => s.id === a.scriptId);
        const scriptB = scripts.find(s => s.id === b.scriptId);
        return (scriptA?.segmentNumber || 0) - (scriptB?.segmentNumber || 0);
      });
      
      if (DEBUG_ENABLED) {
        console.log('Fetched current version segments:', allCurrentVersionSegments.length);
      }
      
      return allCurrentVersionSegments;
    } catch (error) {
      console.error('Error fetching current version segments:', error);
      return [];
    }
  }
  
  /**
   * Handles script selection and fetches/generates segments as needed
   * 
   * @param scriptId The script ID to select
   * @param projectId The project ID for context
   * @returns The segment ID of the current version segment, or null if none
   */
  async handleScriptSelect(scriptId: string, projectId: string): Promise<string | null> {
    if (!scriptId) return null;
    
    try {
      // Get segment store and fetch segments
      const segmentStore = useVideoSegmentStore.getState();
      if (!segmentStore) {
        console.error('Segment store not available');
        return null;
      }
      
      await segmentStore.fetchSegments(scriptId);
      
      // Check if we have valid segments for this script
      const scriptSegments = segmentStore.scriptSegmentsMap[scriptId]?.data || [];
      const validSegments = scriptSegments.filter(
        segment => segment.status === 'completed' && segment.videoUrl
      );
      
      // If we have valid segments, select the current version
      if (validSegments.length > 0) {
        const currentVersionSegment = scriptSegments.find(segment => segment.isCurrentVersion);
        if (currentVersionSegment) {
          console.log('Using existing segment:', currentVersionSegment.id);
          return currentVersionSegment.id;
        }
      }
      
      // If no valid segments found, generate a new one
      console.log('No valid segments found for script, generating new segment');
      const segment = await segmentStore.generateSegment(scriptId);
      if (segment) {
        console.log('Generated new segment:', segment.id);
        return segment.id;
      }
      
      return null;
    } catch (error) {
      console.error('Error handling script selection:', error);
      return null;
    }
  }
  
  /**
   * Initializes the editor with data for a project
   * Prevents duplicate initializations for the same project
   * 
   * @param projectId The project ID to initialize
   * @returns Object containing scripts and segments
   */
  async initializeEditor(projectId: string) {
    if (!projectId) return { scripts: [], segments: [], isGeneratingSegments: false };
    
    // Check if initialization is already in progress for this project
    if (initializationInProgress.has(projectId)) {
      console.log(`[storeCoordinator] initializeEditor: Already initializing project ${projectId}. Returning current state.`);
      
      // Get current script and segment data
      const scriptStore = useVideoScriptStore.getState();
      const scripts = Array.isArray(scriptStore.scripts) ? scriptStore.scripts : [];
      
      // Return current state without triggering new generation
      return { 
        scripts, 
        segments: [], 
        isGeneratingSegments: true // Assume generation is in progress
      };
    }
    
    try {
      // Mark initialization as in progress
      initializationInProgress.add(projectId);
      console.log(`[storeCoordinator] initializeEditor: Starting initialization for project ${projectId}`);
      
      // Get script store and fetch scripts
      const scriptStore = useVideoScriptStore.getState();
      if (!scriptStore) {
        console.error('Script store not available');
        return { scripts: [], segments: [], isGeneratingSegments: false };
      }
      
      await scriptStore.fetchScripts(projectId, false);
      const scripts = Array.isArray(scriptStore.scripts) ? scriptStore.scripts : [];
      
      // If no scripts found and not already generating, generate them
      if (scripts.length === 0 && !scriptStore.isGenerating) {
        try {
          await scriptStore.generateScripts(projectId);
          // Fetch the generated scripts after generation
          const generatedScripts = Array.isArray(scriptStore.scripts) ? scriptStore.scripts : [];
          
          // Auto-generate a video segment for the first script if available
          // This ensures we have at least one segment to display when a project is created
          if (generatedScripts.length > 0) {
            const firstScript = generatedScripts[0];
            console.log('Auto-generating video segment for first script:', firstScript.id);
            
            // Get segment store
            const segmentStore = useVideoSegmentStore.getState();
            if (segmentStore) {
              // Start segment generation but don't wait for it to complete
              // We'll return isGeneratingSegments=true to show a loading state
              segmentStore.generateSegment(firstScript.id);
              
              // Return with isGeneratingSegments flag to indicate loading
              return { 
                scripts: generatedScripts, 
                segments: [], 
                isGeneratingSegments: true 
              };
            }
          }
          
          // Fetch all current version segments
          const segments = await this.fetchAllCurrentVersionSegments(projectId);
          
          return { scripts: generatedScripts, segments, isGeneratingSegments: false };
        } catch (error) {
          console.error('Failed to auto-generate scripts:', error);
          return { scripts: [], segments: [], isGeneratingSegments: false };
        }
      }
      
      // Fetch all current version segments
      const segments = await this.fetchAllCurrentVersionSegments(projectId);
      
      // If we have scripts but no segments, trigger generation for the first script
      if (scripts.length > 0 && segments.length === 0) {
        // Get segment store
        const segmentStore = useVideoSegmentStore.getState();
        if (segmentStore) {
          const firstScript = scripts[0];
          console.log('Auto-generating video segment for existing first script:', firstScript.id);
          
          // Start segment generation but don't wait for completion
          segmentStore.generateSegment(firstScript.id);
          
          return { scripts, segments: [], isGeneratingSegments: true };
        }
      }
      
      return { scripts, segments, isGeneratingSegments: false };
    } catch (error) {
      console.error('Error initializing editor:', error);
      return { scripts: [], segments: [], isGeneratingSegments: false };
    } finally {
      // Always remove from in-progress set when finished
      initializationInProgress.delete(projectId);
      console.log(`[storeCoordinator] initializeEditor: Completed initialization for project ${projectId}`);
    }
  }
}

// Export singleton instance
export const storeCoordinator = new StoreCoordinator(); 