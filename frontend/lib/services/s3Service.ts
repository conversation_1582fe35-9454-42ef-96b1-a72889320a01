import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Validate required environment variables
const requiredEnvVars = [
  'AWS_REGION',
  'LOCAL_DEV_AWS_ACCESS_KEY_ID',
  'LOCAL_DEV_AWS_SECRET_ACCESS_KEY',
  'S3_BUCKET_NAME',
  'S3_BUCKET_REGION'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.LOCAL_DEV_AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.LOCAL_DEV_AWS_SECRET_ACCESS_KEY!,
  },
});

export class S3Service {
  private static instance: S3Service;
  private bucketName: string;
  private bucketRegion: string;

  private constructor() {
    this.bucketName = process.env.S3_BUCKET_NAME!;
    this.bucketRegion = process.env.S3_BUCKET_REGION!;
  }

  public static getInstance(): S3Service {
    if (!S3Service.instance) {
      S3Service.instance = new S3Service();
    }
    return S3Service.instance;
  }

  /**
   * Generate permanent public URL without presigning
   * Note: This method can only be used for objects configured for public access
   */
  getPublicUrl(key: string): string {
    return `https://${this.bucketName}.s3.${this.bucketRegion}.amazonaws.com/${key}`;
  }

  async uploadFile(file: File, key: string): Promise<void> {
    try {
      // Remove audio-only validation to support all file types
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Create S3 upload command
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: buffer,
        ContentType: file.type,
      });

      // Upload to S3
      await s3Client.send(command);
      
      // No longer generating presigned URL, just return
    } catch (error) {
      console.error("Error uploading file to S3:", error);
      if (error instanceof Error) {
        throw new Error(`S3 upload failed: ${error.message}`);
      }
      throw new Error('Failed to upload file to S3');
    }
  }

  async getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const url = await getSignedUrl(s3Client, command, { expiresIn });
      return url;
    } catch (error) {
      console.error("Error generating presigned URL:", error);
      if (error instanceof Error) {
        throw new Error(`Failed to generate presigned URL: ${error.message}`);
      }
      throw new Error('Failed to generate presigned URL');
    }
  }

  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await s3Client.send(command);
    } catch (error) {
      console.error("Error deleting file from S3:", error);
      if (error instanceof Error) {
        throw new Error(`Failed to delete file from S3: ${error.message}`);
      }
      throw new Error('Failed to delete file from S3');
    }
  }
} 