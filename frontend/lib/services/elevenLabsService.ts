// ElevenLabs API configuration
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY || '';
const ELEVENLABS_API_URL = 'https://api.elevenlabs.io/v1';

import { DEFAULT_MALE_VOICE, DEFAULT_FEMALE_VOICE, getVoiceById } from '@/lib/constants/voices';
import { ConcurrencyManager } from '@/lib/utils/concurrencyManager';

// Create a concurrency manager for ElevenLabs with max 2 concurrent requests
// This is based on the error message: "Your current subscription is associated with a maximum of 2 concurrent requests"
const elevenlabsConcurrencyManager = new ConcurrencyManager(2, 'elevenlabs');

export interface VoiceSettings {
  stability: number;
  similarity_boost: number;
  style: number;
  use_speaker_boost: boolean;
}

export interface Voice {
  voice_id: string;
  name: string;
  category?: string;
  description?: string;
  preview_url?: string;
  labels?: Record<string, string>;
}

export interface TextToSpeechRequest {
  text: string;
  model_id?: string;
  voice_settings?: VoiceSettings;
  output_format?: string;
}

/**
 * ElevenLabs service for text-to-speech operations
 */
export class ElevenLabsService {
  /**
   * Get default male voice ID
   * @returns Default male voice ID
   */
  getDefaultMaleVoiceId(): string {
    return DEFAULT_MALE_VOICE.id;
  }

  /**
   * Get default female voice ID
   * @returns Default female voice ID
   */
  getDefaultFemaleVoiceId(): string {
    return DEFAULT_FEMALE_VOICE.id;
  }

  /**
   * Get default voice ID by gender
   * @param gender 'male' or 'female'
   * @returns Default voice ID for the specified gender
   */
  getDefaultVoiceIdByGender(gender: 'male' | 'female'): string {
    return gender === 'male' ? this.getDefaultMaleVoiceId() : this.getDefaultFemaleVoiceId();
  }

  /**
   * Check if a voice ID is one of our default voices
   * @param voiceId Voice ID to check
   * @returns True if the voice ID is a default voice
   */
  isDefaultVoice(voiceId: string): boolean {
    return !!getVoiceById(voiceId);
  }

  /**
   * Get all available voices
   * @returns List of available voices
   */
  async getVoices(): Promise<Voice[]> {
    try {
      const response = await fetch(`${ELEVENLABS_API_URL}/voices`, {
        method: 'GET',
        headers: {
          'xi-api-key': ELEVENLABS_API_KEY,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch voices: ${response.statusText}`);
      }

      const data = await response.json();
      return data.voices || [];
    } catch (error) {
      console.error('Error fetching voices:', error);
      throw new Error('Failed to fetch voices from ElevenLabs');
    }
  }

  /**
   * Generate speech from text
   * @param text Text to convert to speech
   * @param voiceId Voice ID to use
   * @param options Additional options
   * @returns Audio as ArrayBuffer
   */
  async generateSpeech(
    text: string,
    voiceId: string,
    options: {
      modelId?: string;
      voiceSettings?: VoiceSettings;
      outputFormat?: string;
    } = {}
  ): Promise<ArrayBuffer> {
    // Use the concurrency manager to limit concurrent requests
    return elevenlabsConcurrencyManager.enqueue(async () => {
      try {
        const { modelId = 'eleven_multilingual_v2', voiceSettings, outputFormat = 'mp3_44100_128' } = options;
        
        const url = `${ELEVENLABS_API_URL}/text-to-speech/${voiceId}?output_format=${outputFormat}`;
        
        const payload = {
          text,
          model_id: modelId,
          voice_settings: voiceSettings,
        };

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'xi-api-key': ELEVENLABS_API_KEY,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to generate speech: ${response.statusText} - ${errorText}`);
        }

        return await response.arrayBuffer();
      } catch (error) {
        console.error('Error generating speech:', error);
        throw new Error('Failed to generate speech from ElevenLabs');
      }
    });
  }

  /**
   * Generate speech and return as base64 string
   * @param text Text to convert to speech
   * @param voiceId Voice ID to use
   * @param options Additional options
   * @returns Base64 encoded audio string
   */
  async generateSpeechAsBase64(
    text: string,
    voiceId: string,
    options: {
      modelId?: string;
      voiceSettings?: VoiceSettings;
      outputFormat?: string;
    } = {}
  ): Promise<string> {
    const audioBuffer = await this.generateSpeech(text, voiceId, options);
    const base64Audio = Buffer.from(audioBuffer).toString('base64');
    return base64Audio;
  }

  /**
   * Get a specific voice by ID
   * @param voiceId Voice ID
   * @returns Voice details
   */
  async getVoice(voiceId: string): Promise<Voice | null> {
    try {
      const response = await fetch(`${ELEVENLABS_API_URL}/voices/${voiceId}`, {
        method: 'GET',
        headers: {
          'xi-api-key': ELEVENLABS_API_KEY,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch voice: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching voice:', error);
      throw new Error('Failed to fetch voice from ElevenLabs');
    }
  }

  /**
   * Get the ElevenLabs concurrency manager instance
   * This allows external components to check the status of the queue
   */
  getConcurrencyManager(): ConcurrencyManager {
    return elevenlabsConcurrencyManager;
  }
}

// Export a singleton instance
export const elevenLabsService = new ElevenLabsService();
