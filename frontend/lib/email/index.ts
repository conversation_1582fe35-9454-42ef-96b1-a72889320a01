import nodemailer from 'nodemailer';
import { randomBytes } from 'crypto';
import { db } from '../db/drizzle';
import { verificationTokens } from '../db/schema';
import { addDays } from 'date-fns';

// SMTP configuration from environment variables
const SMTP_SERVER = process.env.SMTP_SERVER || '';
const SMTP_PORT = parseInt(process.env.SMTP_PORT || '587');
const SMTP_USER = process.env.SMTP_SENDER_EMAIL || '';
const SMTP_PASSWORD = process.env.SMTP_PASSWORD || '';
const SMTP_FROM_EMAIL = process.env.SMTP_SENDER_EMAIL || '';
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

// Create a reusable transporter object
const transporter = nodemailer.createTransport({
  host: SMTP_SERVER,
  port: SMTP_PORT,
  secure: SMTP_PORT === 465, // true for 465, false for other ports
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASSWORD,
  },
});

// Check if email configuration is available
export function isEmailConfigured(): boolean {
  return Boolean(SMTP_SERVER && SMTP_USER && SMTP_PASSWORD && SMTP_FROM_EMAIL);
}

/**
 * Generate a verification token for a user
 * @param userId The user ID to generate a token for
 * @returns The generated token
 */
export async function generateVerificationToken(userId: string): Promise<string> {
  // Generate a random token
  const token = randomBytes(32).toString('hex');
  
  // Set expiration date (3 days from now)
  const expiresAt = addDays(new Date(), 3);
  
  // Store the token in the database
  await db.insert(verificationTokens).values({
    token,
    userId,
    expiresAt,
  });
  
  return token;
}

/**
 * Verify SMTP connection works properly
 * @returns Promise resolving to boolean indicating success
 */
export async function verifyConnection(): Promise<boolean> {
  if (!isEmailConfigured()) {
    console.warn('Email not configured. Cannot verify connection.');
    return false;
  }

  try {
    // Verify the connection configuration
    const verified = await transporter.verify();
    if (verified) {
      console.log('SMTP connection verified successfully');
    }
    return verified;
  } catch (error) {
    console.error('Error verifying SMTP connection:', error);
    return false;
  }
}

/**
 * Send verification email to a user
 * @param email Email address to send to
 * @param token Verification token
 * @returns Promise resolving to boolean indicating success
 */
export async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  if (!isEmailConfigured()) {
    console.warn('Email not configured. Skipping verification email.');
    return false;
  }
  
  // Verify connection before sending
  try {
    const connectionValid = await verifyConnection();
    if (!connectionValid) {
      console.error('Failed to verify SMTP connection before sending email');
      return false;
    }
  } catch (error) {
    console.error('Error during connection verification:', error);
  }
  
  const verificationUrl = `${APP_URL}/verify-email?token=${token}`;
  
  const mailOptions = {
    from: SMTP_FROM_EMAIL,
    to: email,
    subject: 'Verify your email address',
    text: `Please verify your email address by clicking the following link: ${verificationUrl}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Email Verification</h2>
        <p>Thank you for signing up! Please verify your email address by clicking the button below:</p>
        <a href="${verificationUrl}" style="display: inline-block; background-color: #0284c7; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 20px 0;">
          Verify Email
        </a>
        <p>Or copy and paste this link in your browser:</p>
        <p>${verificationUrl}</p>
        <p>This link will expire in 3 days.</p>
      </div>
    `,
  };
  
  try {
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Error sending verification email:', error);
    return false;
  }
}

/**
 * Send a generic email
 * @param to Recipient email address
 * @param subject Email subject
 * @param text Plain text content
 * @param html HTML content
 * @returns Promise resolving to boolean indicating success
 */
export async function sendEmail(
  to: string, 
  subject: string, 
  text: string, 
  html: string
): Promise<boolean> {
  if (!isEmailConfigured()) {
    console.warn('Email not configured. Skipping email.');
    return false;
  }
  
  // Verify connection before sending
  try {
    const connectionValid = await verifyConnection();
    if (!connectionValid) {
      console.error('Failed to verify SMTP connection before sending email');
      return false;
    }
  } catch (error) {
    console.error('Error during connection verification:', error);
  }
  
  const mailOptions = {
    from: SMTP_FROM_EMAIL,
    to,
    subject,
    text,
    html,
  };
  
  try {
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
} 