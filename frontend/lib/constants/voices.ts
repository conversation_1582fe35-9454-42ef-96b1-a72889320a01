/**
 * Default ElevenLabs voice configurations
 */

export interface DefaultVoice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description?: string;
}

export const DEFAULT_VOICES: DefaultVoice[] = [
  {
    id: 'JBFqnCBsd6RMkjVDRZzb',
    name: '<PERSON>',
    gender: 'male',
    description: 'Default male voice'
  },
  {
    id: '9BWtsMINqrJLrRacOk9x',
    name: 'Aria',
    gender: 'female',
    description: 'Default female voice'
  }
];

export const DEFAULT_MALE_VOICE: DefaultVoice = DEFAULT_VOICES[0];
export const DEFAULT_FEMALE_VOICE: DefaultVoice = DEFAULT_VOICES[1];

/**
 * Get default voice by gender
 * @param gender 'male' or 'female'
 * @returns Default voice for the specified gender
 */
export function getDefaultVoiceByGender(gender: 'male' | 'female'): DefaultVoice {
  return gender === 'male' ? DEFAULT_MALE_VOICE : DEFAULT_FEMALE_VOICE;
}

/**
 * Get voice by ID
 * @param id Voice ID
 * @returns Voice with the specified ID or undefined if not found
 */
export function getVoiceById(id: string): DefaultVoice | undefined {
  return DEFAULT_VOICES.find(voice => voice.id === id);
}
