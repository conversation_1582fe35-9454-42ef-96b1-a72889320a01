# Next.js SaaS Starter

This is a starter template for building a SaaS application using **Next.js** with support for authentication, Stripe integration for payments, and a dashboard for logged-in users.

**Demo: [https://next-saas-start.vercel.app/](https://next-saas-start.vercel.app/)**

## Features

- Marketing landing page (`/`) with animated Terminal element
- Pricing page (`/pricing`) which connects to Stripe Checkout
- Dashboard pages with CRUD operations on users/teams
- Basic RBAC with Owner and Member roles
- Subscription management with Stripe Customer Portal
- Email/password authentication with JWTs stored to cookies
- Global middleware to protect logged-in routes
- Local middleware to protect Server Actions or validate Zod schemas
- Activity logging system for any user events

## Tech Stack

- **Framework**: [Next.js](https://nextjs.org/)
- **Database**: [Postgres](https://www.postgresql.org/)
- **ORM**: [Drizzle](https://orm.drizzle.team/)
- **Payments**: [Stripe](https://stripe.com/)
- **UI Library**: [shadcn/ui](https://ui.shadcn.com/)

## Getting Started

```bash
git clone https://github.com/nextjs/saas-starter
cd saas-starter
pnpm install
```

## Running Locally

Use the included setup script to create your `.env` file:

```bash
pnpm db:setup
```

Then, run the database migrations and seed the database with a default user and team:

```bash
pnpm db:migrate
pnpm db:seed
```

This will create the following user and team:

- User: `<EMAIL>`
- Password: `admin123`

You can, of course, create new users as well through `/sign-up`.

Finally, run the Next.js development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the app in action.

Optionally, you can listen for Stripe webhooks locally through their CLI to handle subscription change events:

```bash
stripe listen --forward-to localhost:3000/api/stripe/webhook
```

## Testing Payments

To test Stripe payments, use the following test card details:

- Card Number: `4242 4242 4242 4242`
- Expiration: Any future date
- CVC: Any 3-digit number

## Going to Production

When you're ready to deploy your SaaS application to production, follow these steps:

### Set up a production Stripe webhook

1. Go to the Stripe Dashboard and create a new webhook for your production environment.
2. Set the endpoint URL to your production API route (e.g., `https://yourdomain.com/api/stripe/webhook`).
3. Select the events you want to listen for (e.g., `checkout.session.completed`, `customer.subscription.updated`).

### Deploy to Vercel

1. Push your code to a GitHub repository.
2. Connect your repository to [Vercel](https://vercel.com/) and deploy it.
3. Follow the Vercel deployment process, which will guide you through setting up your project.

### Add environment variables

In your Vercel project settings (or during deployment), add all the necessary environment variables. Make sure to update the values for the production environment, including:

1. `BASE_URL`: Set this to your production domain.
2. `STRIPE_SECRET_KEY`: Use your Stripe secret key for the production environment.
3. `STRIPE_WEBHOOK_SECRET`: Use the webhook secret from the production webhook you created in step 1.
4. `POSTGRES_URL`: Set this to your production database URL.
5. `AUTH_SECRET`: Set this to a random string. `openssl rand -base64 32` will generate one.

## Environment Variables

Set up the following environment variables in your `.env.local` file:

```env
# Email Service Configuration (for email verification and feedback)
SMTP_SERVER=smtp.example.com
SMTP_PORT=587  
SMTP_SENDER_EMAIL=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM_EMAIL=<EMAIL>
FEEDBACK_RECEIVER_EMAIL=<EMAIL>  # Optional: if different from sender email
```

### Feedback System

The application includes a feedback system that allows users to submit feedback directly from the video editing interface. This feedback is sent via email using the configured SMTP credentials.

To configure the feedback system:

1. Ensure the email configuration is set up correctly in your `.env.local` file
2. The feedback form will appear when users click the "Feedback" button in the preview area
3. Feedback submissions include subject and message
4. Emails are sent from `SMTP_SENDER_EMAIL` to the address specified in `FEEDBACK_RECEIVER_EMAIL` (or falls back to `SMTP_SENDER_EMAIL`)

## Other Templates

While this template is intentionally minimal and to be used as a learning resource, there are other paid versions in the community which are more full-featured:

- https://achromatic.dev
- https://shipfa.st
- https://makerkit.dev

## Google OAuth Configuration for Different Environments

### Production with HTTPS (Recommended)
For production environments with HTTPS properly configured:
1. Make sure your Google Cloud Console project has the correct authorized redirect URIs:
   - `https://yourdomain.com/api/auth/callback/google`
2. Set environment variables:
   ```
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret
   NEXTAUTH_URL=https://yourdomain.com
   AUTH_SECRET=your_auth_secret
   ```

### EC2 or HTTP Environments (Development/Testing Only)
For EC2 or other environments without HTTPS (not recommended for production):
1. Add your non-HTTPS domain to Google Cloud Console's authorized redirect URIs:
   - `http://your-ec2-domain.compute.amazonaws.com:3000/api/auth/callback/google`
2. Set the following environment variables:
   ```
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret
   NEXTAUTH_URL=http://your-ec2-domain.compute.amazonaws.com:3000
   AUTH_SECRET=your_auth_secret
   ALLOW_INSECURE_COOKIES=true  # This disables the 'secure' flag on cookies
   ```

> **Warning**: Setting `ALLOW_INSECURE_COOKIES=true` should only be used for development or testing environments. For production, always use HTTPS.

### Common Issues
If Google login works locally but not in your deployed environment:
1. Check that your Google Cloud Console project has the correct authorized redirect URIs
2. Verify all environment variables are set correctly
3. For HTTP environments, ensure `ALLOW_INSECURE_COOKIES=true` is set
4. Check server logs for any authentication errors
