# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.vscode

# Docker
postgres_data/
.env*.local

package-lock.json

tempCode/*

app/(videoEditing)/display/components/Preview/HelloWorld/*

helloworld.tsx

Root2.tsx

.cursor/rules/normal-rules.mdc

app/test/*
app/test-credits
app/test-scripts