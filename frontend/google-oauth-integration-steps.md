# Google OAuth Integration Steps

This document provides a step-by-step plan for integrating Google OAuth into an existing Next.js SaaS system.

## Integration Steps

### Step 1: Install NextAuth.js
NextAuth.js (now known as Auth.js) is the simplest integration method as it's specifically designed for Next.js applications.

```bash
npm install next-auth
# or
yarn add next-auth
# or
pnpm add next-auth
```

### Step 2: Create Auth.js Configuration
Create a configuration file in the `/app/api/auth/[...nextauth]` directory and set up the Google provider.

Required file:
- `/app/api/auth/[...nextauth]/route.ts`

### Step 3: Modify Database Schema
Add Google-related fields to the user table:
- `googleId`: Store Google user ID
- `avatarUrl`: Optional, store user avatar URL

File to modify:
- `/lib/db/schema.ts`

### Step 4: Create Google Login Button
Add a Google login button to the login page.

File to modify:
- `/app/(login)/login.tsx`

### Step 5: Handle OAuth Callback
Implement logic to handle the callback after Google login:
- Check if user exists
- If not exists, create new user
- If exists but not linked to Google, link the account
- Generate JWT and set cookie

Files to create/modify:
- `/app/api/auth/[...nextauth]/route.ts`
- `/lib/auth/oauth.ts` (optional, for handling OAuth-related logic)

### Step 6: Adjust Existing Authentication Logic
Modify authentication-related files to handle Google-authenticated users.

Files to modify:
- `/lib/auth/session.ts`
- `/app/(login)/actions.ts`

## Implementation Considerations

1. **Maintain Existing System Compatibility**: Ensure Google login doesn't break the existing email/password login system.

2. **User Account Merging**: When a user logs in with Google, decide whether to allow account merging or prompt for password login if the email already exists.

3. **Session Management**: Ensure session management for Google-authenticated users is consistent with the existing system.

4. **Frontend Interface**: Add "Sign in with Google" button below the login form while maintaining a clean and consistent interface.

5. **Error Handling**: Add appropriate error handling to ensure OAuth flow errors are properly caught and displayed.

## Technical Implementation Points

1. **NextAuth.js Integration**: NextAuth.js will handle most of the OAuth flow, but you need to integrate it with your existing JWT system.

2. **Database Adaptation**: Modify database queries to support user lookup by Google ID.

3. **Session Conversion**: Convert NextAuth.js sessions to your existing JWT format.

4. **Redirect Handling**: Ensure post-login redirect logic remains consistent with the existing system, especially for checkout flows.

## Environment Variables

Already configured:
```
GOOGLE_CLIENT_ID=741870917924-4ivddtrll9bqu6td8bbce93vk1kgbesa.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
```

Needs to be added:
```
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
```

## Reference Resources

- [NextAuth.js Official Documentation](https://next-auth.js.org/)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)



-- Disable foreign key constraints
SET session_replication_role = 'replica';

-- Empty all tables
TRUNCATE TABLE activity_logs CASCADE;
TRUNCATE TABLE invitations CASCADE;
TRUNCATE TABLE team_members CASCADE;
TRUNCATE TABLE teams CASCADE;
TRUNCATE TABLE users CASCADE;

-- Re-enable foreign key constraints
SET session_replication_role = 'origin';

-- Reset auto-increment IDs for all tables
ALTER SEQUENCE activity_logs_id_seq RESTART WITH 1;
ALTER SEQUENCE invitations_id_seq RESTART WITH 1;
ALTER SEQUENCE team_members_id_seq RESTART WITH 1;
ALTER SEQUENCE teams_id_seq RESTART WITH 1;
ALTER SEQUENCE users_id_seq RESTART WITH 1;