'use client';

import { useState, useEffect } from 'react';
import { 
  getUserCredits,
  checkCredits,
  generateAudienceInsights,
  createVideoCampaign,
  generateScript
} from '@/lib/api-client/credit-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { InfoIcon, CheckCircleIcon, AlertCircle, Loader2 } from 'lucide-react';

interface CreditBalance {
  credits: number;
  planName: string;
  history: any[];
  featureUsage: any[];
  subscriptionStatus: string;
}

export default function CreditTester() {
  // Credit balance state
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('audience');

  // Form states
  const [audienceForm, setAudienceForm] = useState({
    brandName: '',
    industry: '',
    targetAudience: ''
  });

  const [videoForm, setVideoForm] = useState({
    title: '',
    brandName: '',
    adLength: 10,
    summary: '',
    description: ''
  });

  const [scriptForm, setScriptForm] = useState({
    brandName: '',
    summary: '',
    description: '',
    segmentNumber: 1
  });

  // Load initial credit balance
  useEffect(() => {
    fetchCreditBalance();
  }, []);

  const fetchCreditBalance = async () => {
    try {
      setLoading(true);
      const data = await getUserCredits();
      setCreditBalance(data);
      setError(null);
    } catch (err) {
      setError('Failed to load credit balance');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Check credits for an operation
  const handleCheckCredits = async (feature: string, parameters: any = {}) => {
    try {
      setLoading(true);
      const data = await checkCredits(feature, parameters);
      setResult(data);
      setError(null);
      return data;
    } catch (err) {
      setError('Failed to check credits');
      console.error(err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Generate audience insights
  const handleGenerateAudienceInsights = async () => {
    try {
      // First check if we have enough credits
      setLoading(true);
      const check = await handleCheckCredits('audience_insight');
      
      if (!check || !check.hasEnoughCredits) {
        setError(`Insufficient credits. Required: ${check?.requiredCredits || 'unknown'}`);
        return;
      }
      
      // Generate insights
      const data = await generateAudienceInsights(audienceForm);
      setResult(data);
      
      // Refresh credit balance
      await fetchCreditBalance();
      
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to generate audience insights');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Create video campaign
  const handleCreateVideoCampaign = async () => {
    try {
      // First check if we have enough credits
      setLoading(true);
      const check = await handleCheckCredits('video_campaign', { durationInSeconds: videoForm.adLength });
      
      if (!check || !check.hasEnoughCredits) {
        setError(`Insufficient credits. Required: ${check?.requiredCredits || 'unknown'}`);
        return;
      }
      
      // Create campaign
      const data = await createVideoCampaign(videoForm);
      setResult(data);
      
      // Refresh credit balance
      await fetchCreditBalance();
      
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to create video campaign');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Generate script
  const handleGenerateScript = async () => {
    try {
      // First check if we have enough credits
      setLoading(true);
      const check = await handleCheckCredits('script_generation', { model: 'gpt-4.1' });
      
      if (!check || !check.hasEnoughCredits) {
        setError(`Insufficient credits. Required: ${check?.requiredCredits || 'unknown'}`);
        return;
      }
      
      // Generate script
      const data = await generateScript({
        projectData: {
          brandName: scriptForm.brandName,
          summary: scriptForm.summary,
          description: scriptForm.description
        },
        segmentNumber: scriptForm.segmentNumber
      });
      
      setResult(data);
      
      // Refresh credit balance
      await fetchCreditBalance();
      
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to generate script');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Credit System Test Page</h1>
      
      {/* Credit Balance Display */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Your Credit Balance</CardTitle>
          <CardDescription>Current plan and available credits</CardDescription>
        </CardHeader>
        <CardContent>
          {loading && <div className="flex items-center justify-center p-4"><Loader2 className="animate-spin mr-2" /> Loading...</div>}
          {creditBalance && (
            <div className="grid gap-4">
              <div className="flex justify-between">
                <span className="font-medium">Available Credits:</span>
                <span className="text-xl font-bold">{creditBalance.credits}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Current Plan:</span>
                <span>{creditBalance.planName}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Subscription Status:</span>
                <span>{creditBalance.subscriptionStatus}</span>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={fetchCreditBalance} variant="outline">Refresh Balance</Button>
        </CardFooter>
      </Card>
      
      {/* Test Tabs - Simple implementation without shadcn UI tabs */}
      <div className="mb-6">
        <div className="flex border-b">
          <button 
            className={`py-2 px-4 ${activeTab === 'audience' ? 'border-b-2 border-blue-500 font-medium' : ''}`}
            onClick={() => setActiveTab('audience')}
          >
            Test Audience Insights
          </button>
          <button 
            className={`py-2 px-4 ${activeTab === 'video' ? 'border-b-2 border-blue-500 font-medium' : ''}`}
            onClick={() => setActiveTab('video')}
          >
            Test Video Campaign
          </button>
          <button 
            className={`py-2 px-4 ${activeTab === 'script' ? 'border-b-2 border-blue-500 font-medium' : ''}`}
            onClick={() => setActiveTab('script')}
          >
            Test Script Generation
          </button>
        </div>
      </div>
      
      {/* Audience Insights Tab */}
      {activeTab === 'audience' && (
        <Card>
          <CardHeader>
            <CardTitle>Generate Audience Insights</CardTitle>
            <CardDescription>Costs 25 credits per generation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="brandName">Brand Name</Label>
                <Input 
                  id="brandName" 
                  value={audienceForm.brandName}
                  onChange={(e) => setAudienceForm({...audienceForm, brandName: e.target.value})}
                  placeholder="Enter brand name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="industry">Industry</Label>
                <Input 
                  id="industry" 
                  value={audienceForm.industry}
                  onChange={(e) => setAudienceForm({...audienceForm, industry: e.target.value})}
                  placeholder="Enter industry"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="targetAudience">Target Audience (optional)</Label>
                <Input 
                  id="targetAudience" 
                  value={audienceForm.targetAudience}
                  onChange={(e) => setAudienceForm({...audienceForm, targetAudience: e.target.value})}
                  placeholder="Enter target audience"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={() => handleCheckCredits('audience_insight')}
              disabled={loading}
            >
              Check Credit Cost
            </Button>
            <Button 
              onClick={handleGenerateAudienceInsights}
              disabled={loading || !audienceForm.brandName || !audienceForm.industry}
            >
              {loading ? <Loader2 className="animate-spin mr-2" /> : null}
              Generate Insights
            </Button>
          </CardFooter>
        </Card>
      )}
      
      {/* Video Campaign Tab */}
      {activeTab === 'video' && (
        <Card>
          <CardHeader>
            <CardTitle>Create Video Campaign</CardTitle>
            <CardDescription>Costs 10 credits per second of video</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Campaign Title</Label>
                <Input 
                  id="title" 
                  value={videoForm.title}
                  onChange={(e) => setVideoForm({...videoForm, title: e.target.value})}
                  placeholder="Enter campaign title"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="v-brandName">Brand Name</Label>
                <Input 
                  id="v-brandName" 
                  value={videoForm.brandName}
                  onChange={(e) => setVideoForm({...videoForm, brandName: e.target.value})}
                  placeholder="Enter brand name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="adLength">Video Length (seconds)</Label>
                <Input 
                  id="adLength" 
                  type="number"
                  value={videoForm.adLength}
                  onChange={(e) => setVideoForm({...videoForm, adLength: parseInt(e.target.value) || 10})}
                  placeholder="Enter video length in seconds"
                />
                <p className="text-sm text-muted-foreground">Cost: {videoForm.adLength * 10} credits</p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="summary">Summary</Label>
                <Input 
                  id="summary" 
                  value={videoForm.summary}
                  onChange={(e) => setVideoForm({...videoForm, summary: e.target.value})}
                  placeholder="Enter campaign summary"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  value={videoForm.description}
                  onChange={(e) => setVideoForm({...videoForm, description: e.target.value})}
                  placeholder="Enter campaign description"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={() => handleCheckCredits('video_campaign', { durationInSeconds: videoForm.adLength })}
              disabled={loading}
            >
              Check Credit Cost
            </Button>
            <Button 
              onClick={handleCreateVideoCampaign}
              disabled={loading || !videoForm.title || !videoForm.brandName}
            >
              {loading ? <Loader2 className="animate-spin mr-2" /> : null}
              Create Campaign
            </Button>
          </CardFooter>
        </Card>
      )}
      
      {/* Script Generation Tab */}
      {activeTab === 'script' && (
        <Card>
          <CardHeader>
            <CardTitle>Generate Script</CardTitle>
            <CardDescription>Costs 10 credits for gpt-4.1, 25 for gpt-4.1</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="s-brandName">Brand Name</Label>
                <Input 
                  id="s-brandName" 
                  value={scriptForm.brandName}
                  onChange={(e) => setScriptForm({...scriptForm, brandName: e.target.value})}
                  placeholder="Enter brand name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="s-summary">Summary</Label>
                <Input 
                  id="s-summary" 
                  value={scriptForm.summary}
                  onChange={(e) => setScriptForm({...scriptForm, summary: e.target.value})}
                  placeholder="Enter script summary"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="s-description">Description</Label>
                <Textarea 
                  id="s-description" 
                  value={scriptForm.description}
                  onChange={(e) => setScriptForm({...scriptForm, description: e.target.value})}
                  placeholder="Enter script description"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="segmentNumber">Segment Number</Label>
                <Input 
                  id="segmentNumber" 
                  type="number"
                  value={scriptForm.segmentNumber}
                  onChange={(e) => setScriptForm({...scriptForm, segmentNumber: parseInt(e.target.value) || 1})}
                  placeholder="Enter segment number"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={() => handleCheckCredits('script_generation', { model: 'gpt-4.1' })}
              disabled={loading}
            >
              Check Credit Cost
            </Button>
            <Button 
              onClick={handleGenerateScript}
              disabled={loading || !scriptForm.brandName || !scriptForm.summary}
            >
              {loading ? <Loader2 className="animate-spin mr-2" /> : null}
              Generate Script
            </Button>
          </CardFooter>
        </Card>
      )}
      
      {/* Results Display */}
      {result && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Result</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="p-4 bg-gray-100 rounded overflow-auto">{JSON.stringify(result, null, 2)}</pre>
          </CardContent>
        </Card>
      )}
      
      {/* Error Display */}
      {error && (
        <div className="mt-8 p-4 bg-red-50 border border-red-200 rounded-md flex items-start space-x-3">
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
          <div>
            <h4 className="font-medium text-red-800">Error</h4>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}
    </div>
  );
} 