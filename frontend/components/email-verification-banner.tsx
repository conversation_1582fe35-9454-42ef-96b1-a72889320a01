'use client';

import { useUser } from '@/lib/auth';
import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle2, X } from 'lucide-react';
import { resendVerificationEmail } from '@/app/verify-email/actions';
import { User } from '@/lib/db/schema';

interface VerifyEmailResult {
  success: boolean;
  error?: string;
}

export function EmailVerificationBanner() {
  const { userPromise } = useUser();
  const [isVisible, setIsVisible] = useState(true);
  const [resendStatus, setResendStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  
  const isMounted = useRef(true);

  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (!userPromise) {
      if (isMounted.current) {
        setLoading(false);
      }
      return;
    }

    const loadUser = async () => {
      try {
        const user = await userPromise;
        if (isMounted.current) {
          setCurrentUser(user);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error loading user:', error);
        if (isMounted.current) {
          setLoading(false);
        }
      }
    };

    loadUser();
  }, [userPromise]);

  if (loading || !currentUser || currentUser.emailVerified === true || !isVisible) {
    return null;
  }

  const handleResendVerification = async () => {
    setResendStatus('loading');
    
    try {
      const result: VerifyEmailResult = await resendVerificationEmail();
      
      if (!isMounted.current) return;
      
      if (result.success) {
        setResendStatus('success');
        setMessage('Verification email has been sent to your inbox.');
      } else {
        setResendStatus('error');
        setMessage(result.error || 'Failed to send verification email.');
      }
    } catch (error: unknown) {
      if (!isMounted.current) return;
      
      setResendStatus('error');
      setMessage('An error occurred. Please try again later.');
      console.error('Error resending verification email:', error);
    }
    
    const timerId = setTimeout(() => {
      if (isMounted.current) {
        setResendStatus('idle');
        setMessage('');
      }
    }, 5000);
    
    return () => clearTimeout(timerId);
  };

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-yellow-400 mr-3" />
          <div>
            <p className="text-sm text-yellow-700">
              Please verify your email address. 
              {resendStatus === 'idle' && (
                <Button 
                  variant="link" 
                  className="p-0 h-auto text-yellow-800 font-medium hover:underline" 
                  onClick={handleResendVerification}
                >
                  Resend verification email
                </Button>
              )}
              {resendStatus === 'loading' && <span className="ml-2">Sending...</span>}
              {resendStatus === 'success' && (
                <span className="ml-2 flex items-center text-green-600">
                  <CheckCircle2 className="h-4 w-4 mr-1" /> {message}
                </span>
              )}
              {resendStatus === 'error' && (
                <span className="ml-2 text-red-600">{message}</span>
              )}
            </p>
          </div>
        </div>
        <Button 
          variant="ghost" 
          size="sm"
          className="p-1 h-auto"
          onClick={() => setIsVisible(false)}
        >
          <X className="h-4 w-4 text-yellow-500" />
        </Button>
      </div>
    </div>
  );
} 