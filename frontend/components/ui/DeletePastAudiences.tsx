import React, { useEffect, useState, useRef } from 'react';
import {<PERSON><PERSON>,DialogContent,
    DialogHeader,
    DialogTitle} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
interface DeleteProps{
    Open: boolean;
    onClose?: () => void;
    adCampaignId:number;
    onDeleted: () => void;//to trigger page refresh right after deletion
}   


const DeleteDialog: React.FC<DeleteProps> = ({Open, onClose, adCampaignId, onDeleted}) =>{
    const [isError,setError] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    //delete an ad campaign
    const deleteaCampaign =async (adCampaignId: number) =>{
        try {
            const response = await fetch(`/api/ad-targeting/campaigns/${adCampaignId}`, {
              method: 'DELETE',
            });
            
            if (!response.ok) {
              throw new Error('Failed to fetch campaigns');
            }
            
            const data = await response.json();

          } catch (err) {
            console.error('Error deleting the campaign:', err);
            setError('Unable to delete the campaign');
          } finally {
            setLoading(false);
            onDeleted();
          }
        if (onClose) onClose();
    }
   return( <Dialog open={Open} onOpenChange={onClose} >
    <DialogContent className="sm:max-w-[425px] rounded-md shadow-md py-5">
      <DialogHeader>
        <DialogTitle className="text-md text-cyan-700 font-sans text-center mt-2">
         Are you sure you want to delete this ad campaign?
         <div className='flex flex-row mt-2'><span><Button onClick={()=>{if (onClose) onClose();}} className="h-8 bg-white border border-cyan-700 hover:cursor-pointer hover:border-none hover:bg-cyan-700 text-black hover:text-white font-sans text-sm">Cancel</Button></span><span><Button className="ml-[200px] h-8 bg-red-800 hover:cursor-pointer hover:bg-red-900 text-white font-sans text-sm" onClick={()=>deleteaCampaign(adCampaignId)}>Yes, Delete</Button></span></div>
        </DialogTitle>
      </DialogHeader>
    </DialogContent>
  </Dialog>);
}

export default DeleteDialog;