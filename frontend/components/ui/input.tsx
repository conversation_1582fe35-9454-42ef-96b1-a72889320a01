import * as React from "react"

import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-9 px-4 py-3 w-full border font-sans border-gray-200 rounded-md ring-offset-white focus:border-cyan-700 focus:ring-1 focus:ring-cyan-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neutral-400  focus-visible:ring-offset-2 placeholder:text-neutral-500",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
