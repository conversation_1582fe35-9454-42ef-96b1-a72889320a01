import React, { useEffect, useState, useRef } from 'react';
import {Dialog,DialogContent,
    DialogHeader,
    DialogTitle} from "@/components/ui/dialog"


interface ComingSoonProps{
    Open: boolean;
    onClose?: () => void;
}    
const ComingSoon: React.FC<ComingSoonProps> = ({Open, onClose}) =>{
   return( <Dialog open={Open} onOpenChange={onClose} >
    <DialogContent className="sm:max-w-[425px] rounded-md shadow-md">
      <DialogHeader>
        <DialogTitle className="text-md text-cyan-700 font-sans text-center">
         Coming Soon..
        </DialogTitle>
      </DialogHeader>
    </DialogContent>
  </Dialog>);
}

export default ComingSoon;