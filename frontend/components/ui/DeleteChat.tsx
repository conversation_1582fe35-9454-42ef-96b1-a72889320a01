import React, { useEffect, useState, useRef } from 'react';
import {Di<PERSON>,DialogContent,
    DialogHeader,
    DialogTitle} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
interface DeleteProps{
    Open: boolean;
    onClose?: () => void;
    chatId:string;
    onDeleted: () => void;//to trigger page refresh right after deletion
}   


const DeleteaChat: React.FC<DeleteProps> = ({Open, onClose, chatId, onDeleted}) =>{
    const [isError,setError] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    //delete a conversation
    const deleteaChat =async (chatId: string) =>{
        try {
            const response = await fetch(`/api/marketing-ai/delete-chat/${chatId}`, {
              method: 'DELETE',
            });
            
            if (!response.ok) {
              throw new Error('Failed to fetch the conversation');
            }
            
            const data = await response.json();

          } catch (err) {
            console.error('Error deleting the conversation:', err);
            setError('Unable to delete the conversation');
          } finally {
            setLoading(false);
            onDeleted();
          }
        if (onClose) onClose();
    }
   return( <Dialog open={Open} onOpenChange={onClose} >
    <DialogContent className="sm:max-w-[425px] rounded-md shadow-md py-5">
      <DialogHeader>
        <DialogTitle className="text-md text-cyan-700 font-sans text-center mt-2">
         Are you sure you want to delete this conversation?
         <div className='flex flex-row mt-2'><span><Button onClick={()=>{if (onClose) onClose();}} className="h-8 bg-white border border-cyan-700 hover:cursor-pointer hover:border-none hover:bg-cyan-700 text-black hover:text-white font-sans text-sm">Cancel</Button></span><span><Button className="ml-[200px] h-8 bg-red-800 hover:cursor-pointer hover:bg-red-900 text-white font-sans text-sm" onClick={()=>deleteaChat(chatId)}>Yes, Delete</Button></span></div>
        </DialogTitle>
      </DialogHeader>
    </DialogContent>
  </Dialog>);
}

export default DeleteaChat;