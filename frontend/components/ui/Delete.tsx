import React, { useEffect, useState, useRef } from 'react';
import {Dialog,DialogContent,
    DialogHeader,
    DialogTitle} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
import { useVideoProjectStore } from '@/lib/store/videoProjectStore';
interface DeleteProps{
    Open: boolean;
    onClose?: () => void;
    projectId: string;
}    
const DeleteDialog: React.FC<DeleteProps> = ({Open, onClose, projectId}) =>{
   const { 
           projects, 
           currentProject, 
           isLoading, 
           isCreating, 
           isUpdating, 
           isDeleting,
           fetchProjects, 
           fetchProject, 
           createProject, 
           updateProject, 
           deleteProject, 
           setCurrentProject 
         } = useVideoProjectStore(); 
    //delete a project
    const deleteaProject =async (projectId: string) =>{
        await deleteProject(projectId);
        if (onClose) onClose();
    }
   return( <Dialog open={Open} onOpenChange={onClose} >
    <DialogContent className="sm:max-w-[425px] rounded-md shadow-md py-5">
      <DialogHeader>
        <DialogTitle className="text-md text-cyan-700 font-sans text-center mt-2">
         Are you sure you want to delete this video campaign?
         <div className='flex flex-row mt-2'><span><Button onClick={()=>{if (onClose) onClose();}} className="h-8 bg-white border border-cyan-700 hover:cursor-pointer hover:border-none hover:bg-cyan-700 text-black hover:text-white font-sans text-sm">Cancel</Button></span><span><Button className="ml-[200px] h-8 bg-red-800 hover:cursor-pointer hover:bg-red-900 text-white font-sans text-sm" onClick={()=>deleteaProject(projectId)}>Yes, Delete</Button></span></div>
        </DialogTitle>
      </DialogHeader>
    </DialogContent>
  </Dialog>);
}

export default DeleteDialog;