{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:setup": "npx tsx lib/db/setup.ts", "db:seed": "npx tsx lib/db/seed.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "clean": "rm -rf node_modules pnpm-lock.yaml dist"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.782.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.8", "@remotion/cli": "4.0.289", "@remotion/lambda": "4.0.289", "@remotion/media-parser": "4.0.289", "@remotion/player": "4.0.289", "@remotion/transitions": "4.0.289", "@remotion/zod-types": "4.0.289", "@tailwindcss/postcss": "4.1.4", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.13.1", "@types/nodemailer": "^6.4.17", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@vercel/postgres": "^0.10.0", "antd": "^5.24.4", "autoprefixer": "^10.4.20", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.3", "elevenlabs": "^1.56.0", "jose": "^5.9.6", "lucide-react": "^0.474.0", "next": "15.2.0-canary.33", "next-auth": "^4.24.5", "nodemailer": "^6.9.9", "openai": "^4.89.0", "postcss": "^8.5.3", "postgres": "^3.4.5", "react": "19.0.0", "react-colorful": "^5.6.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.0.0", "react-markdown": "^10.1.0", "react-scroll": "^1.9.3", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remotion": "4.0.289", "server-only": "^0.0.1", "stripe": "^17.6.0", "tailwind-merge": "^3.0.1", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "2.0.0", "typescript": "^5.7.3", "zod": "^3.22.3", "zustand": "^5.0.3"}, "devDependencies": {"@swc/core": "^1.11.21", "drizzle-kit": "^0.31.1", "jest-worker": "^29.7.0", "prettier": "^3.5.3", "uglify-js": "^3.19.3", "webpack": "^5.96.1"}}