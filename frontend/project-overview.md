# Next.js SaaS Starter - Project Overview

This document provides an overview of the Next.js SaaS Starter template, exploring its architecture, features, and implementation details.

## Core Architecture

1. **Framework**: Next.js 15 with App Router
2. **Database**: PostgreSQL with Drizzle ORM
3. **Authentication**: Custom JWT-based auth with cookies
4. **Payments**: Stripe integration for subscriptions
5. **UI**: Uses shadcn/ui components with Tailwind CSS

## Key Features

### Authentication System
- Email/password authentication
- JWT tokens stored in cookies
- User registration and login flows
- Session management

### Team Management
- Multi-user teams with roles (owner, member)
- Team invitation system
- Role-based access control (RBAC)
- Member management (invite, remove)

### Subscription Management
- Stripe Checkout integration
- Customer portal for subscription management
- Trial periods (14 days)
- Subscription status tracking
- Webhook handling for subscription events

### Dashboard
- Team settings management
- Member management interface
- Subscription status and plan information
- Activity logging display

### Activity Logging
- Tracks user actions (sign-in, team creation, etc.)
- IP address tracking
- Audit trail for security and compliance

## Database Structure

The database schema includes several key tables:

### Users Table
```typescript
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  role: varchar('role', { length: 20 }).notNull().default('member'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});
```

### Teams Table
```typescript
export const teams = pgTable('teams', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  stripeCustomerId: text('stripe_customer_id').unique(),
  stripeSubscriptionId: text('stripe_subscription_id').unique(),
  stripeProductId: text('stripe_product_id'),
  planName: varchar('plan_name', { length: 50 }),
  subscriptionStatus: varchar('subscription_status', { length: 20 }),
});
```

### Team Members Table
```typescript
export const teamMembers = pgTable('team_members', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  role: varchar('role', { length: 50 }).notNull(),
  joinedAt: timestamp('joined_at').notNull().defaultNow(),
});
```

### Activity Logs Table
```typescript
export const activityLogs = pgTable('activity_logs', {
  id: serial('id').primaryKey(),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  userId: integer('user_id').references(() => users.id),
  action: text('action').notNull(),
  timestamp: timestamp('timestamp').notNull().defaultNow(),
  ipAddress: varchar('ip_address', { length: 45 }),
});
```

### Invitations Table
```typescript
export const invitations = pgTable('invitations', {
  id: serial('id').primaryKey(),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  email: varchar('email', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull(),
  invitedBy: integer('invited_by')
    .notNull()
    .references(() => users.id),
  invitedAt: timestamp('invited_at').notNull().defaultNow(),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
});
```

## Project Structure

- `/app`: Next.js App Router structure
  - `/(dashboard)`: Protected dashboard routes
  - `/(login)`: Authentication routes
  - `/api`: API endpoints including Stripe webhooks
- `/lib`: Core functionality
  - `/auth`: Authentication logic
  - `/db`: Database schema and queries
  - `/payments`: Stripe integration
- `/components`: Reusable UI components

## Authentication Flow

1. User signs up with email/password
2. Password is hashed using bcrypt
3. User record is created in the database
4. A team is automatically created for the user (if not joining via invitation)
5. JWT session is established and stored in cookies
6. User is redirected to the dashboard

## Subscription Flow

1. User selects a plan on the pricing page
2. Stripe Checkout session is created
3. User completes payment on Stripe hosted page
4. Webhook captures the event and updates the team's subscription status
5. User gets access to premium features

## Getting Started

According to the README, you can:
1. Set up the environment with `pnpm db:setup`
2. Run migrations with `pnpm db:migrate`
3. Seed the database with `pnpm db:seed`
4. Start the development server with `pnpm dev`

The seed creates a default test user:
- Email: `<EMAIL>`
- Password: `admin123`

## Testing Payments

To test Stripe payments, use the following test card details:
- Card Number: `4242 4242 4242 4242`
- Expiration: Any future date
- CVC: Any 3-digit number

## Deployment Considerations

When deploying to production:
1. Set up production Stripe webhook
2. Configure environment variables in production
3. Set up a production PostgreSQL database
4. Generate a secure AUTH_SECRET

## Potential Improvements

1. Add more authentication providers (OAuth, SSO)
2. Implement more robust error handling
3. Add comprehensive testing
4. Enhance the UI with more interactive elements
5. Add more subscription tiers and features
6. Implement usage analytics
7. Add more team collaboration features
