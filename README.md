# Adcreate

AdCreate is a comprehensive platform designed for automated ad targeting and video creation, utilizing AI to generate scripts and video content tailored to a target audience.

## Project Structure

```plaintext
adcreate/
├── backend/               # Backend API built with FastAPI
│
├── frontend/              # Frontend UI built with Next.js and React
│
└── README.md              # Project overview and setup instructions (this file)
```

## Features
**Ad Targeting:** Leverages behavioral and psychographic factors to recommend ad targeting strategies.
**AI Video Generation:** Uses AI to generate scripts and stitch together video clips, creating engaging ads.
**Modular Frontend and Backend:** Developed with FastAPI for the backend and Next.js/React for the frontend.

## Setup and Installation
### Prerequisites
* **Python** (for backend)
* **Node.js and npm** (for frontend)
* **Docker** (optional, for containerized deployment)

### Backend Setup
1. Navigate to the backend directory:
```bash
cd backend
```

2. Install dependencies and run the app:
```bash 
./run.sh
```

### Frontend Setup
1. Navigate to the frontend directory:
```bash
cd frontend
```
2. Install dependencies and start the development server:
```bash
npm install
npm run dev
```

### Docker Setup (optional)
You can use Docker Compose to build and run both services:

```bash
docker-compose up --build
```

## Usage
1. Access the **frontend UI** at `http://localhost:3000`.
2. The **backend API** can be accessed at `http://localhost:8000/docs` for Swagger API documentation.
